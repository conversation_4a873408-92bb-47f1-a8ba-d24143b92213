version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile.fast
      # Enable BuildKit for faster builds
      args:
        BUILDKIT_INLINE_CACHE: 1
    image: phongnha_app_fast:latest
    container_name: phongnha_app_fast
    ports:
      - "8080:8080"
    environment:
      - DB_HOST=db
      - DB_PORT=5432
      - DB_USER=postgres
      - DB_PASSWORD=password
      - DB_NAME=phongnha_valley
      - DB_SSLMODE=disable
      - JWT_SECRET=phongnha_valley_production_jwt_secret_key_2024_secure_random_string_for_production_use_only
      - GIN_MODE=release
    depends_on:
      db:
        condition: service_healthy
    volumes:
      - phongnha_uploads:/app/frontend/public/assets/uploads
    restart: unless-stopped
    # Resource limits to prevent system overload
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '1.0'
        reservations:
          memory: 256M
          cpus: '0.5'

  db:
    image: postgres:15-alpine
    container_name: phongnha_db_fast
    environment:
      POSTGRES_DB: phongnha_valley
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5433:5432"  # Different port to avoid conflict
    volumes:
      - phongnha_db_data:/var/lib/postgresql/data
    restart: unless-stopped
    # Health check for faster startup
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d phongnha_valley"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'

volumes:
  phongnha_db_data:
    external: true
  phongnha_uploads:
    external: true
