{"name": "phongnha-valley-frontend", "version": "1.0.0", "description": "Frontend for Phong Nha Valley Glamping & Adventure website", "main": "index.js", "scripts": {"build": "npm run build-vue && npm run copy-assets", "build-vue": "webpack --mode=production", "build-dev": "webpack --mode=development", "copy-assets": "npm run copy-css && npm run copy-images", "copy-css": "cp src/assets/css/* public/assets/css/", "copy-images": "cp src/assets/images/* public/assets/images/", "dev": "npm run build-dev && npm run copy-assets", "watch": "webpack --mode=development --watch", "serve": "echo 'Frontend assets built. Serve via Go backend.'", "clean": "rm -rf public/assets/*"}, "keywords": ["glamping", "tourism", "phong-nha", "frontend", "vue"], "author": "Phong Nha Valley Team", "license": "MIT", "devDependencies": {"@babel/core": "^7.23.0", "@babel/preset-env": "^7.23.0", "babel-loader": "^9.1.3", "css-loader": "^6.8.1", "html-webpack-plugin": "^5.5.3", "style-loader": "^3.3.3", "vue-loader": "^17.3.0", "vue-template-compiler": "^2.7.14", "webpack": "^5.89.0", "webpack-cli": "^5.1.4"}, "dependencies": {"vue": "^3.3.8"}}