# Frontend Makefile for Phong Nha Valley

.PHONY: install build clean dev copy-assets copy-css copy-images help build-vue

# Default target
all: install build

# Install dependencies
install:
	@echo "Installing dependencies..."
	@npm install

# Build all frontend assets
build: clean build-vue copy-assets
	@echo "Frontend build completed successfully!"

# Build Vue.js components
build-vue:
	@echo "Building Vue.js components..."
	@npm run build-vue

# Clean the public assets directory
clean:
	@echo "Cleaning public assets..."
	@rm -rf public/assets/*
	@mkdir -p public/assets/css public/assets/js public/assets/images

# Copy all assets from src to public
copy-assets: copy-css copy-images
	@echo "All assets copied successfully!"

# Copy CSS files
copy-css:
	@echo "Copying CSS files..."
	@cp src/assets/css/* public/assets/css/ 2>/dev/null || true

# Copy image files
copy-images:
	@echo "Copying image files..."
	@cp src/assets/images/* public/assets/images/ 2>/dev/null || true

# Development mode (build and watch for changes)
dev: install clean
	@echo "Building for development..."
	@npm run dev

# Watch mode for development
watch: install
	@echo "Starting watch mode..."
	@npm run watch

# Show help
help:
	@echo "Available targets:"
	@echo "  install     - Install npm dependencies"
	@echo "  build       - Build all frontend assets (Vue.js + static assets)"
	@echo "  build-vue   - Build only Vue.js components"
	@echo "  clean       - Clean the public assets directory"
	@echo "  copy-assets - Copy all static assets from src to public"
	@echo "  copy-css    - Copy only CSS files"
	@echo "  copy-images - Copy only image files"
	@echo "  dev         - Build for development"
	@echo "  watch       - Watch for changes and rebuild automatically"
	@echo "  help        - Show this help message"
