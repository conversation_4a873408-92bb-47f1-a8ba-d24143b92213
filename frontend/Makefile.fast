# Fast frontend build Makefile
# Optimized for development speed

.PHONY: install build clean dev watch help

# Default target
all: build

# Install dependencies with cache
install:
	@echo "📦 Installing dependencies..."
	@npm ci --silent --prefer-offline

# Fast build with parallel processing
build: install
	@echo "🔨 Building frontend (fast mode)..."
	@npm run build

# Clean build artifacts (safe clean)
clean:
	@echo "🧹 Cleaning build artifacts..."
	@rm -f public/assets/js/*.js 2>/dev/null || true
	@rm -f public/assets/js/*.map 2>/dev/null || true
	@rm -f public/assets/css/*.css 2>/dev/null || true
	@echo "✅ Clean completed"

# Development mode with hot reload
dev: install
	@echo "🚀 Starting development server..."
	@npm run dev

# Watch mode for continuous building
watch: install
	@echo "👀 Starting watch mode..."
	@npm run watch

# Quick build without dependency check
quick-build:
	@echo "⚡ Quick build (no dependency check)..."
	@npm run build

# Show help
help:
	@echo "Frontend Fast Build Commands:"
	@echo "  make install      - Install dependencies"
	@echo "  make build        - Full build with dependencies"
	@echo "  make quick-build  - Quick build without dependency check"
	@echo "  make clean        - Clean build artifacts"
	@echo "  make dev          - Start development server"
	@echo "  make watch        - Start watch mode"
	@echo "  make help         - Show this help"
