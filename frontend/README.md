# Phong Nha Valley - Frontend

This directory contains the frontend assets and structure for the Phong Nha Valley Glamping & Adventure website.

## Directory Structure

```
frontend/
├── public/                 # Built/served files (served by Go backend)
│   ├── index.html         # Main website page
│   ├── admin/             # Admin panel
│   │   └── index.html     # Admin interface
│   └── assets/            # Built static assets
│       ├── css/           # Compiled CSS files
│       ├── js/            # JavaScript files
│       └── images/        # Image assets
├── src/                   # Source files for development
│   ├── assets/            # Source assets
│   │   ├── css/           # Source CSS files
│   │   ├── js/            # Source JavaScript files
│   │   └── images/        # Source images
│   ├── pages/             # Page templates (future use)
│   └── components/        # Reusable components (future use)
├── package.json           # Frontend build configuration
└── README.md             # This file
```

## Development

### Building Assets

To build the frontend assets:

```bash
cd frontend
npm run build
```

This copies all source assets from `src/assets/` to `public/assets/`.

### Development Workflow

1. Edit files in `src/assets/`
2. Run `npm run build` to copy changes to `public/assets/`
3. The Go backend serves files from `frontend/public/`

### Asset Paths

All assets are served with the `/assets/` prefix:
- CSS: `/assets/css/style.css`, `/assets/css/admin.css`
- JavaScript: `/assets/js/app.js`, `/assets/js/admin.js`
- Images: `/assets/images/[filename]`

## Pages

- **Main Website** (`/`): Customer-facing website with services, gallery, and booking
- **Admin Panel** (`/admin/`): Administrative interface for managing services, bookings, and content

## Features

- Responsive design for mobile and desktop
- Interactive booking system
- Admin panel for content management
- Gallery with image showcase
- Service management
- User authentication

## Future Enhancements

- Component-based architecture
- CSS preprocessing (Sass/Less)
- JavaScript bundling and minification
- Image optimization
- Progressive Web App (PWA) features
