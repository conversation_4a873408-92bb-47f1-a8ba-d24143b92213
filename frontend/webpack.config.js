const path = require('path');
const { VueLoaderPlugin } = require('vue-loader');
const HtmlWebpackPlugin = require('html-webpack-plugin');

module.exports = {
  entry: {
    app: './src/js/app.js',
    admin: './src/js/admin.js',
    about: './src/js/about.js',
    service: './src/js/service.js',
    services: './src/js/services.js',
    contact: './src/js/contact.js',
    menu: './src/js/menu.js',
    gallery: './src/js/gallery.js',
    'booking-manager': './src/js/booking-manager.js'
  },
  output: {
    path: path.resolve(__dirname, 'public/assets/js'),
    filename: '[name].js',
    clean: true
  },
  module: {
    rules: [
      {
        test: /\.vue$/,
        loader: 'vue-loader'
      },
      {
        test: /\.js$/,
        loader: 'babel-loader',
        exclude: /node_modules/
      },
      {
        test: /\.css$/,
        use: [
          'style-loader',
          'css-loader'
        ]
      }
    ]
  },
  plugins: [
    new VueLoaderPlugin()
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
      'vue': 'vue/dist/vue.esm-bundler.js'
    },
    extensions: ['.js', '.vue', '.json']
  },
  mode: 'development',
  devtool: 'source-map',
  optimization: {
    minimize: false
  }
};
