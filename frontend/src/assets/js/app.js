// Global variables
let currentUser = null;
let services = [];
let authToken = localStorage.getItem('authToken');

// API Base URL
const API_BASE = '/api/v1';

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// Initialize application
async function initializeApp() {
    // Check if user is logged in
    if (authToken) {
        try {
            await getCurrentUser();
        } catch (error) {
            console.error('Failed to get current user:', error);
            localStorage.removeItem('authToken');
            authToken = null;
        }
    }

    // Load services
    await loadServices();
    
    // Update UI based on auth status
    updateAuthUI();
    
    // Set minimum date for booking form
    const dateInput = document.getElementById('date');
    if (dateInput) {
        const today = new Date().toISOString().split('T')[0];
        dateInput.min = today;
    }

    // Initialize smooth scrolling
    initializeSmoothScrolling();

    // Initialize booking form
    initializeBookingForm();

    // Load hero content
    try {
        await loadHeroContent();
    } catch (error) {
        console.error('Failed to load hero content in initializeApp:', error);
    }
}

// API Helper Functions
async function apiCall(endpoint, options = {}) {
    const url = `${API_BASE}${endpoint}`;
    const config = {
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        },
        ...options
    };

    if (authToken) {
        config.headers.Authorization = `Bearer ${authToken}`;
    }

    try {
        const response = await fetch(url, config);
        const data = await response.json();

        if (!response.ok) {
            throw new Error(data.error || 'API request failed');
        }

        return data;
    } catch (error) {
        console.error('API Error:', error);
        throw error;
    }
}

// Authentication Functions
async function getCurrentUser() {
    try {
        currentUser = await apiCall('/user/profile');
        return currentUser;
    } catch (error) {
        throw error;
    }
}

async function login(email, password) {
    try {
        const response = await apiCall('/auth/login', {
            method: 'POST',
            body: JSON.stringify({ email, password })
        });

        authToken = response.token;
        currentUser = response.user;
        localStorage.setItem('authToken', authToken);
        
        return response;
    } catch (error) {
        throw error;
    }
}

async function register(userData) {
    try {
        const response = await apiCall('/auth/register', {
            method: 'POST',
            body: JSON.stringify(userData)
        });

        authToken = response.token;
        currentUser = response.user;
        localStorage.setItem('authToken', authToken);
        
        return response;
    } catch (error) {
        throw error;
    }
}

function logout() {
    authToken = null;
    currentUser = null;
    localStorage.removeItem('authToken');
    updateAuthUI();
    showNotification('Logged out successfully', 'success');
}

// Services Functions
async function loadServices() {
    try {
        services = await apiCall('/services');
        renderServices();
        populateServiceSelect();
    } catch (error) {
        console.error('Failed to load services:', error);
        showNotification('Failed to load services', 'error');
    }
}

function renderServices() {
    const servicesGrid = document.getElementById('servicesGrid');
    if (!servicesGrid) return;

    servicesGrid.innerHTML = services.map(service => `
        <div class="service-card fade-in">
            <div class="service-image" style="background-image: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.3)), url('/static/images/${getServiceImage(service.type)}')">
                <div class="service-status ${service.is_active ? 'active' : 'inactive'}">
                    ${service.is_active ? 'Available' : 'Coming Soon'}
                </div>
            </div>
            <div class="service-content">
                <div class="service-type">${formatServiceType(service.type)}</div>
                <h3 class="service-name">${service.name}</h3>
                <p class="service-description">${service.description}</p>
                <div class="service-price">
                    <span class="price-adult">${formatPrice(service.price)}</span>
                    ${service.child_price ? `<span class="price-child">Child: ${formatPrice(service.child_price)}</span>` : ''}
                </div>
                <div class="service-hours">
                    <i class="icon-clock"></i> ${service.open_time} - ${service.close_time}
                </div>
                <button class="btn-book" ${!service.is_active ? 'disabled' : ''} onclick="selectServiceForBooking('${service.id}')">
                    ${service.is_active ? 'Book Now' : 'Coming Soon'}
                </button>
            </div>
        </div>
    `).join('');
}

function populateServiceSelect() {
    const serviceSelect = document.getElementById('service');
    if (!serviceSelect) return;

    serviceSelect.innerHTML = '<option value="">Select a service</option>' +
        services.filter(service => service.is_active)
                .map(service => `<option value="${service.id}">${service.name} - ${formatPrice(service.price)}</option>`)
                .join('');
}

// Booking Functions
async function createBooking(bookingData) {
    try {
        if (!authToken) {
            openAuthModal();
            throw new Error('Please login to make a booking');
        }

        const response = await apiCall('/bookings', {
            method: 'POST',
            body: JSON.stringify(bookingData)
        });

        return response;
    } catch (error) {
        throw error;
    }
}

async function checkAvailability(serviceId, date) {
    try {
        const response = await apiCall(`/availability?service_id=${serviceId}&date=${date}`);
        return response;
    } catch (error) {
        console.error('Failed to check availability:', error);
        return null;
    }
}

function selectServiceForBooking(serviceId) {
    const serviceSelect = document.getElementById('service');
    if (serviceSelect) {
        serviceSelect.value = serviceId;
        scrollToSection('contact');
    }
}

// UI Helper Functions
function formatServiceType(type) {
    return type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
}

function formatPrice(price) {
    return new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
    }).format(price);
}

function getServiceImage(type) {
    const imageMap = {
        'ACCOMMODATION': 'accommodation.jpg',
        'AFTERNOON_TEA': 'tea.jpg',
        'WATER_ACTIVITY': 'water-activities.jpg',
        'SIGHTSEEING': 'sightseeing.jpg',
        'GO_KART': 'gokart.jpg',
        'KIDS_WATER_PARK': 'kids-park.jpg',
        'PARAGLIDING': 'paragliding.jpg'
    };
    return imageMap[type] || 'service-default.jpg';
}

function updateAuthUI() {
    const loginBtn = document.querySelector('.btn-login');
    if (!loginBtn) return;

    if (currentUser) {
        loginBtn.textContent = `Hi, ${currentUser.first_name}`;
        loginBtn.onclick = showUserMenu;
    } else {
        loginBtn.textContent = 'Login';
        loginBtn.onclick = openAuthModal;
    }
}

function showUserMenu() {
    // Create a simple dropdown menu
    const existingMenu = document.querySelector('.user-menu');
    if (existingMenu) {
        existingMenu.remove();
        return;
    }

    const menu = document.createElement('div');
    menu.className = 'user-menu';
    menu.style.cssText = `
        position: absolute;
        top: 100%;
        right: 0;
        background: white;
        border-radius: 8px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        padding: 10px 0;
        min-width: 150px;
        z-index: 1001;
    `;

    menu.innerHTML = `
        <a href="#" onclick="showUserBookings()" style="display: block; padding: 10px 20px; color: #333; text-decoration: none;">My Bookings</a>
        <a href="#" onclick="logout()" style="display: block; padding: 10px 20px; color: #333; text-decoration: none;">Logout</a>
    `;

    const loginBtn = document.querySelector('.btn-login');
    loginBtn.parentElement.style.position = 'relative';
    loginBtn.parentElement.appendChild(menu);

    // Close menu when clicking outside
    setTimeout(() => {
        document.addEventListener('click', function closeMenu(e) {
            if (!menu.contains(e.target) && e.target !== loginBtn) {
                menu.remove();
                document.removeEventListener('click', closeMenu);
            }
        });
    }, 100);
}

function showUserBookings() {
    // This would typically open a bookings modal or redirect to a bookings page
    showNotification('Bookings feature coming soon!', 'info');
}

function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(n => n.remove());

    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        background: ${type === 'error' ? '#dc3545' : type === 'success' ? '#28a745' : '#17a2b8'};
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        z-index: 2001;
        animation: slideInRight 0.3s ease;
    `;
    notification.textContent = message;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => notification.remove(), 300);
    }, 3000);
}

// Modal Functions
function openAuthModal() {
    const modal = document.getElementById('authModal');
    if (modal) {
        modal.style.display = 'block';
        document.body.style.overflow = 'hidden';
    }
}

function closeAuthModal() {
    const modal = document.getElementById('authModal');
    if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';
    }
}

function showLogin() {
    document.getElementById('loginForm').style.display = 'block';
    document.getElementById('registerForm').style.display = 'none';
    document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
    document.querySelectorAll('.tab-btn')[0].classList.add('active');
}

function showRegister() {
    document.getElementById('loginForm').style.display = 'none';
    document.getElementById('registerForm').style.display = 'block';
    document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
    document.querySelectorAll('.tab-btn')[1].classList.add('active');
}

// Event Handlers
async function handleLogin(event) {
    event.preventDefault();
    const form = event.target;
    const formData = new FormData(form);
    
    const email = formData.get('email') || form.querySelector('input[type="email"]').value;
    const password = formData.get('password') || form.querySelector('input[type="password"]').value;

    try {
        form.classList.add('loading');
        await login(email, password);
        closeAuthModal();
        updateAuthUI();
        showNotification('Login successful!', 'success');
    } catch (error) {
        showNotification(error.message, 'error');
    } finally {
        form.classList.remove('loading');
    }
}

async function handleRegister(event) {
    event.preventDefault();
    const form = event.target;
    const inputs = form.querySelectorAll('input');
    
    const userData = {
        first_name: inputs[0].value,
        last_name: inputs[1].value,
        email: inputs[2].value,
        phone: inputs[3].value,
        password: inputs[4].value
    };

    try {
        form.classList.add('loading');
        await register(userData);
        closeAuthModal();
        updateAuthUI();
        showNotification('Registration successful!', 'success');
    } catch (error) {
        showNotification(error.message, 'error');
    } finally {
        form.classList.remove('loading');
    }
}

function initializeBookingForm() {
    const bookingForm = document.getElementById('bookingForm');
    if (!bookingForm) return;

    bookingForm.addEventListener('submit', async function(event) {
        event.preventDefault();
        
        if (!authToken) {
            openAuthModal();
            showNotification('Please login to make a booking', 'error');
            return;
        }

        const formData = new FormData(bookingForm);
        const serviceId = formData.get('service');
        const date = formData.get('date');
        const adults = parseInt(formData.get('adults'));
        const children = parseInt(formData.get('children'));
        const notes = formData.get('notes');

        if (!serviceId || !date || !adults) {
            showNotification('Please fill in all required fields', 'error');
            return;
        }

        // Check availability first
        const availability = await checkAvailability(serviceId, date);
        if (availability && !availability.available) {
            showNotification('Selected service is not available for this date', 'error');
            return;
        }

        const bookingData = {
            service_id: serviceId,
            booking_date: date,
            adults: adults,
            children: children,
            special_notes: notes
        };

        // Add check-in/check-out dates for accommodation
        const selectedService = services.find(s => s.id === serviceId);
        if (selectedService && selectedService.type === 'ACCOMMODATION') {
            bookingData.check_in_date = date;
            // Default to next day checkout
            const checkoutDate = new Date(date);
            checkoutDate.setDate(checkoutDate.getDate() + 1);
            bookingData.check_out_date = checkoutDate.toISOString().split('T')[0];
        }

        try {
            bookingForm.classList.add('loading');
            const booking = await createBooking(bookingData);
            showNotification('Booking created successfully!', 'success');
            bookingForm.reset();
        } catch (error) {
            showNotification(error.message, 'error');
        } finally {
            bookingForm.classList.remove('loading');
        }
    });
}

// Navigation Functions
function toggleMenu() {
    const navMenu = document.getElementById('navMenu');
    const hamburger = document.querySelector('.hamburger');
    
    navMenu.classList.toggle('active');
    hamburger.classList.toggle('active');
}

function scrollToSection(sectionId) {
    const element = document.getElementById(sectionId);
    if (element) {
        const headerHeight = document.querySelector('.header').offsetHeight;
        const elementPosition = element.offsetTop - headerHeight;
        
        window.scrollTo({
            top: elementPosition,
            behavior: 'smooth'
        });
    }
}

function initializeSmoothScrolling() {
    // Add smooth scrolling to all anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            if (targetId) {
                scrollToSection(targetId);
            }
        });
    });
}

// Close modal when clicking outside
window.onclick = function(event) {
    const modal = document.getElementById('authModal');
    if (event.target === modal) {
        closeAuthModal();
    }
}

// Header scroll effect
window.addEventListener('scroll', function() {
    const header = document.querySelector('.header');
    if (window.scrollY > 100) {
        header.style.background = 'rgba(45, 45, 45, 0.98)';
    } else {
        header.style.background = 'rgba(45, 45, 45, 0.95)';
    }
});

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
    
    .hamburger.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }
    
    .hamburger.active span:nth-child(2) {
        opacity: 0;
    }
    
    .hamburger.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }
`;
document.head.appendChild(style);

// Load hero content from API
async function loadHeroContent() {
    try {
        console.log('Loading hero content...');
        const response = await apiCall('/content/homepage/hero');
        const heroData = response;
        console.log('Hero data received:', heroData);

        // Update hero section content
        const heroSection = document.querySelector('.hero');
        const heroTitle = document.querySelector('.hero-title');
        const heroSubtitle = document.querySelector('.hero-subtitle');
        const heroDescription = document.querySelector('.hero-description');

        if (heroData.title && heroTitle) {
            heroTitle.innerHTML = heroData.title;
        }

        if (heroData.subtitle && heroSubtitle) {
            heroSubtitle.textContent = heroData.subtitle;
        }

        if (heroData.content && heroDescription) {
            heroDescription.textContent = heroData.content;
        }

        // Update background image if provided
        if (heroData.image_url && heroSection) {
            console.log('Setting hero background image:', heroData.image_url);
            // Create or update hero background
            let heroBg = heroSection.querySelector('.hero-bg');
            console.log('Found hero-bg element:', heroBg);
            if (!heroBg) {
                console.log('Creating new hero-bg element');
                heroBg = document.createElement('div');
                heroBg.className = 'hero-bg';
                heroSection.insertBefore(heroBg, heroSection.firstChild);
            }

            // Set background image with overlay
            const backgroundStyle = `
                linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)),
                url('${heroData.image_url}') center/cover no-repeat
            `;
            console.log('Setting background style:', backgroundStyle);
            heroBg.style.background = backgroundStyle;
        }

    } catch (error) {
        console.error('Failed to load hero content:', error);
        // Fallback to default styling if API fails
    }
}
