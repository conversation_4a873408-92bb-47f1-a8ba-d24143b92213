<template>
  <div id="menu-app">
    <!-- Header -->
    <header class="header">
      <div class="container">
        <div class="nav-brand">
          <a href="/" class="logo-link">
            <div class="logo">
              <img
                src="/logo/logo-ngang-08.png"
                alt="Phong Nha Valley"
                class="logo-img"
              />
            </div>
          </a>
        </div>
        <nav class="nav-menu" id="navMenu">
          <ul>
            <li><a href="/">Trang Chủ</a></li>
            <li><a href="/about.html">Về Chúng Tôi</a></li>
            <li><a href="/services.html">Dịch Vụ</a></li>
            <li><a href="/gallery.html">Th<PERSON> Viện</a></li>
            <li><a href="/menu.html" class="active">Menu</a></li>
            <li><a href="/contact.html"><PERSON><PERSON><PERSON></a></li>
            <li><a href="#" class="btn-call" @click="handleCallClick">G<PERSON><PERSON></a></li>
          </ul>
        </nav>

        <!-- Language Switcher -->
        <div class="language-switcher">
          <button
            class="lang-btn"
            :class="{ active: currentLanguage === 'vi' }"
            @click="switchLanguage('vi')"
            :disabled="translating"
            title="Tiếng Việt"
          >
            <span class="flag-emoji">🇻🇳</span>
            VI
          </button>
          <button
            class="lang-btn"
            :class="{ active: currentLanguage === 'en' }"
            @click="switchLanguage('en')"
            :disabled="translating"
            title="English - Powered by Google Translate"
          >
            <span class="flag-emoji">🇬🇧</span>
            EN
            <span v-if="currentLanguage === 'en'" class="google-icon">🌐</span>
          </button>
          <div v-if="translating" class="translation-loading">
            <span class="loading-spinner"></span>
            <span>Translating...</span>
          </div>
        </div>
        <div class="hamburger" id="hamburger" @click="toggleMenu">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>
    </header>

    <!-- Hero Section -->
    <section class="hero menu-hero" ref="menuHero">
      <div class="hero-overlay"></div>
      <div class="container">
        <div class="hero-content">
          <h1 class="hero-title">Thực Đơn Tại<span class="highlight">Phong Nha</span></h1>
          <p class="hero-subtitle">PHONG NHA VALLEY GLAMPING</p>
        </div>
      </div>
    </section>

    <!-- Menu Section -->
    <section class="menu-section">
      <div class="container">
        <div class="section-header">
        </div>

        <!-- Menu Tabs -->
        <div class="menu-tabs">
          <button 
            v-for="(tab, index) in menuTabs" 
            :key="index"
            class="menu-tab"
            :class="{ active: activeTab === index }"
            @click="setActiveTab(index)"
          >
            <i :class="tab.icon"></i>
            <span>{{ tab.name }}</span>
          </button>
        </div>

        <!-- Menu Content -->
        <div class="menu-content">
          <!-- Special Block Layout for Accommodation -->
          <div v-if="currentCategory === 'accommodation' && currentMenuImages.length > 0" class="menu-accommodation-grid">
            <div
              v-for="(block, blockIndex) in accommodationBlocks"
              :key="blockIndex"
              class="menu-block"
            >
              <!-- Menu Block Content (First Image) -->
              <div class="menu-block-content" @click="openMenuImageModal(block.contentImage, block.contentImage.originalIndex)">
                <div class="menu-block-content-image">
                  <img :src="block.contentImage.url" :alt="block.contentImage.alt || `Content ${blockIndex + 1}`" @error="handleImageError">
                  <div class="menu-block-content-overlay">
                    <h3 v-if="block.contentImage.title">{{ block.contentImage.title }}</h3>
                    <p v-if="block.contentImage.description">{{ block.contentImage.description }}</p>
                  </div>
                </div>
              </div>

              <!-- Menu Block Images (Remaining 4 Images) -->
              <div class="menu-block-images">
                <div
                  v-if="block.mainImage"
                  class="menu-block-main-image"
                  @click="openMenuImageModal(block.mainImage, block.mainImage.originalIndex)"
                >
                  <img :src="block.mainImage.url" :alt="block.mainImage.alt || `Main ${blockIndex + 1}`" @error="handleImageError">
                </div>
                <div class="menu-block-sub-images">
                  <div
                    v-for="(subImage, subIndex) in block.subImages"
                    :key="subIndex"
                    class="menu-block-sub-image"
                    @click="openMenuImageModal(subImage, subImage.originalIndex)"
                  >
                    <img :src="subImage.url" :alt="subImage.alt || `Sub ${blockIndex + 1}-${subIndex + 1}`" @error="handleImageError">
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Special Block Layout for Afternoon Tea -->
          <div v-else-if="currentCategory === 'afternoon_tea' && currentMenuImages.length > 0" class="menu-tea-grid">
            <div
              v-for="(block, blockIndex) in afternoonTeaBlocks"
              :key="blockIndex"
              class="menu-tea-block"
            >
              <!-- Tea Block Content (Text) -->
              <div class="menu-tea-content">
                <h2 class="menu-tea-title">{{ block.title || 'TRÀ MAN HOÀNG HÔN' }}</h2>
                <div class="menu-tea-price">{{ block.price || '120.000 - Trà, đồ ngọt' }}</div>
                <div class="menu-tea-description">{{ block.description || '( khoai dẻo, kẹo lạc, hạt sen sấy, trái cây)' }}</div>
              </div>

              <!-- Tea Block Images -->
              <div class="menu-tea-images">
                <div class="menu-tea-small-images">
                  <div
                    v-for="(smallImage, smallIndex) in block.smallImages"
                    :key="smallIndex"
                    class="menu-tea-small-image"
                    @click="openMenuImageModal(smallImage, smallImage.originalIndex)"
                  >
                    <img :src="smallImage.url" :alt="smallImage.alt || `Tea ${blockIndex + 1}-${smallIndex + 1}`" @error="handleImageError">
                  </div>
                </div>
                <div
                  v-if="block.largeImage"
                  class="menu-tea-large-image"
                  @click="openMenuImageModal(block.largeImage, block.largeImage.originalIndex)"
                >
                  <img :src="block.largeImage.url" :alt="block.largeImage.alt || `Tea Large ${blockIndex + 1}`" @error="handleImageError">
                </div>
              </div>
            </div>
          </div>

          <!-- Regular Grid Layout for Restaurant -->
          <div v-else-if="currentMenuImages.length > 0" class="menu-images-grid">
            <div
              v-for="(menuImage, index) in currentMenuImages"
              :key="index"
              class="menu-image-card"
              @click="openMenuImageModal(menuImage, index)"
            >
              <div class="menu-image-container">
                <img :src="menuImage.url" :alt="menuImage.alt || `Menu ${index + 1}`" @error="handleImageError">
                <div class="menu-image-info" v-if="menuImage.title">
                  <h3>{{ menuImage.title }}</h3>
                  <p v-if="menuImage.description">{{ menuImage.description }}</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Fallback to individual items if no menu images -->
          <div v-else-if="currentMenuItems.length > 0" class="menu-grid">
            <div
              v-for="(item, index) in currentMenuItems"
              :key="index"
              class="menu-item"
              @click="openMenuModal(item)"
            >
              <div class="menu-item-image">
                <img :src="item.image" :alt="item.name" @error="handleImageError">
                <div class="menu-item-overlay">
                  <i class="icon-zoom">🔍</i>
                  <span>View Details</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Menu Image Modal -->
    <div v-if="showMenuImageModal" class="menu-image-modal" @click="closeMenuImageModal">
      <div class="menu-image-modal-content" @click.stop>
        <button class="menu-image-modal-close" @click="closeMenuImageModal">&times;</button>

        <!-- Navigation Buttons -->
        <button
          v-if="currentMenuImages.length > 1"
          class="menu-image-modal-nav menu-image-modal-prev"
          @click="navigateMenuImageModal(-1)"
          :disabled="currentMenuImageIndex === 0"
        >
          &#8249;
        </button>
        <button
          v-if="currentMenuImages.length > 1"
          class="menu-image-modal-nav menu-image-modal-next"
          @click="navigateMenuImageModal(1)"
          :disabled="currentMenuImageIndex === currentMenuImages.length - 1"
        >
          &#8250;
        </button>

        <div class="menu-image-modal-image">
          <img :src="selectedMenuImage.url" :alt="selectedMenuImage.alt || 'Menu'" @load="adjustModalOverlayShape">
          <div class="menu-image-modal-overlay" ref="menuImageModalOverlay">
            <div class="menu-image-modal-info" v-if="selectedMenuImage.title">
              <h2>{{ selectedMenuImage.title }}</h2>
              <p v-if="selectedMenuImage.description">{{ selectedMenuImage.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Menu Modal -->
    <div v-if="showMenuModal" class="menu-modal" @click="closeMenuModal">
      <div class="menu-modal-content" @click.stop>
        <button class="menu-modal-close" @click="closeMenuModal">&times;</button>

        <!-- Navigation Buttons -->
        <button
          v-if="currentMenuItems.length > 1"
          class="menu-modal-nav menu-modal-prev"
          @click="navigateModal(-1)"
          :disabled="currentModalIndex === 0"
        >
          &#8249;
        </button>
        <button
          v-if="currentMenuItems.length > 1"
          class="menu-modal-nav menu-modal-next"
          @click="navigateModal(1)"
          :disabled="currentModalIndex === currentMenuItems.length - 1"
        >
          &#8250;
        </button>

        <div class="menu-modal-image">
          <img :src="selectedMenuItem.image" :alt="selectedMenuItem.name" @load="adjustModalOverlayShape">
          <div class="menu-modal-overlay" ref="modalOverlay">
            <div class="menu-modal-info">
              <h2>{{ selectedMenuItem.name }}</h2>
              <p class="menu-modal-description">{{ selectedMenuItem.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Notification -->
    <div v-if="notification.show" class="notification" :class="notification.type">
      {{ notification.message }}
    </div>
  </div>
</template>

<script>
import apiService from '../utils/api.js';

export default {
  name: 'MenuApp',
  data() {
    return {
      // Language
      currentLanguage: 'vi',
      translating: false,

      // Menu data
      menuTabs: [
        { name: 'MENU NHÀ HÀNG', icon: 'icon-restaurant', category: 'restaurant' },
        { name: 'MENU LƯU TRÚ', icon: 'icon-accommodation', category: 'accommodation' },
        { name: 'SET TRÀ CHIỀU', icon: 'icon-tea', category: 'afternoon_tea' }
      ],
      activeTab: 0,
      menuItems: {
        restaurant: [],
        accommodation: [],
        afternoon_tea: []
      },
      menuImages: [], // Menu images for display
      loading: false,

      // Modal
      showMenuModal: false,
      selectedMenuItem: {},
      currentModalIndex: 0,

      // Menu Image Modal
      showMenuImageModal: false,
      selectedMenuImage: {},
      currentMenuImageIndex: 0,

      // Hero background
      heroBackground: '/assets/images/menu-hero-bg.jpg',

      // Notification
      notification: {
        show: false,
        message: '',
        type: 'success'
      },


    };
  },

  computed: {
    currentCategory() {
      return this.menuTabs[this.activeTab].category;
    },

    currentMenuItems() {
      const currentCategory = this.menuTabs[this.activeTab].category;
      return this.menuItems[currentCategory] || [];
    },

    currentMenuImages() {
      const currentCategory = this.menuTabs[this.activeTab].category;
      return this.menuImages
        .filter(image => image.category === currentCategory)
        .sort((a, b) => {
          // Sort by ID ascending (oldest first) to maintain fixed positions
          if (a.id && b.id) {
            return a.id - b.id;
          }
          // Fallback to creation date if ID not available
          if (a.created_at && b.created_at) {
            return new Date(a.created_at) - new Date(b.created_at);
          }
          // Final fallback to maintain original order
          return 0;
        });
    },

    accommodationBlocks() {
      if (this.currentCategory !== 'accommodation') return [];

      const images = this.currentMenuImages;
      const blocks = [];

      // Group images into blocks of 5
      for (let i = 0; i < images.length; i += 5) {
        const blockImages = images.slice(i, i + 5);

        if (blockImages.length > 0) {
          const block = {
            contentImage: { ...blockImages[0], originalIndex: i },
            mainImage: blockImages.length > 1 ? { ...blockImages[1], originalIndex: i + 1 } : null,
            subImages: []
          };

          // Add remaining images as sub images (up to 3)
          for (let j = 2; j < blockImages.length && j < 5; j++) {
            block.subImages.push({ ...blockImages[j], originalIndex: i + j });
          }

          blocks.push(block);
        }
      }

      return blocks;
    },

    afternoonTeaBlocks() {
      if (this.currentCategory !== 'afternoon_tea') return [];

      const images = this.currentMenuImages;
      const blocks = [];

      // Group images into blocks of 4
      for (let i = 0; i < images.length; i += 4) {
        const blockImages = images.slice(i, i + 4);

        if (blockImages.length > 0) {
          const block = {
            title: blockImages[0]?.title || 'TRÀ MAN HOÀNG HÔN',
            price: blockImages[0]?.price || '120.000 - Trà, đồ ngọt',
            description: blockImages[0]?.description || '( khoai dẻo, kẹo lạc, hạt sen sấy, trái cây)',
            smallImages: blockImages.slice(0, 2).map((img, index) => ({
              ...img,
              originalIndex: i + index
            })),
            largeImage: blockImages.length > 2 ? {
              ...blockImages[2],
              originalIndex: i + 2
            } : null
          };

          blocks.push(block);
        }
      }

      return blocks;
    }
  },

  async mounted() {
    console.log('🔍 DEBUG MenuApp: Component mounted');
    console.log('🔍 DEBUG MenuApp: Language switcher data:', {
      currentLanguage: this.currentLanguage,
      translating: this.translating
    });

    await this.initializeMenuApp();
    await this.loadHeroImage();

    // Setup overlay shape adjustment for menu items
    this.$nextTick(() => {
      this.setupMenuItemOverlays();

      // Check if language switcher is in DOM
      const langSwitcher = document.querySelector('.language-switcher');
      console.log('🔍 DEBUG MenuApp: Language switcher in DOM:', !!langSwitcher);
      if (langSwitcher) {
        console.log('🔍 DEBUG MenuApp: Language switcher HTML:', langSwitcher.outerHTML);
      }

      // Listen for window resize to readjust overlays
      window.addEventListener('resize', this.setupMenuItemOverlays);
    });
  },

  beforeUnmount() {
    window.removeEventListener('resize', this.setupMenuItemOverlays);
  },

  methods: {
    // Language switcher methods
    switchLanguage(lang) {
      console.log('🔍 DEBUG MenuApp: switchLanguage called with:', lang);
      if (this.translating || this.currentLanguage === lang) {
        console.log('🔍 DEBUG MenuApp: Switch blocked - translating:', this.translating, 'currentLanguage:', this.currentLanguage);
        return;
      }

      console.log('🔍 DEBUG MenuApp: Starting language switch to:', lang);
      this.translating = true;
      this.currentLanguage = lang;

      if (lang === 'en') {
        this.translateToEnglish();
      } else {
        this.restoreVietnamese();
      }
    },

    translateToEnglish() {
      const translations = {
        // Navigation
        'Home': 'Home',
        'About Us': 'About Us',
        'Services': 'Services',
        'Gallery': 'Gallery',
        'Menu': 'Menu',
        'Contact': 'Contact',
        'Call': 'Call',

        // Menu specific
        'MENU NHÀ HÀNG': 'RESTAURANT MENU',
        'MENU LƯU TRÚ': 'ACCOMMODATION MENU',
        'SET TRÀ CHIỀU': 'AFTERNOON TEA SET',
        'Thực đơn của chúng tôi': 'Our Menu',
        'Khám phá hương vị': 'Discover the flavors',

        // Common
        'Xem chi tiết': 'View Details',
        'Đóng': 'Close'
      };

      this.applyTranslations(translations);

      setTimeout(() => {
        this.translating = false;
      }, 1000);
    },

    restoreVietnamese() {
      const reverseTranslations = {
        // Navigation
        'Home': 'Trang Chủ',
        'About Us': 'Về Phong Nha',
        'Services': 'Dịch Vụ',
        'Gallery': 'Thư viện',
        'Menu': 'Menu',
        'Contact': 'Liên hệ',
        'Call': 'Gọi ngay',

        // Menu specific
        'RESTAURANT MENU': 'MENU NHÀ HÀNG',
        'ACCOMMODATION MENU': 'MENU LƯU TRÚ',
        'AFTERNOON TEA SET': 'SET TRÀ CHIỀU',
        'Our Menu': 'Thực đơn của chúng tôi',
        'Discover the flavors': 'Khám phá hương vị',

        // Common
        'View Details': 'Xem chi tiết',
        'Close': 'Đóng'
      };

      this.applyTranslations(reverseTranslations);

      setTimeout(() => {
        this.translating = false;
      }, 1000);
    },

    applyTranslations(translations) {
      Object.keys(translations).forEach(originalText => {
        const translatedText = translations[originalText];

        const walker = document.createTreeWalker(
          document.body,
          NodeFilter.SHOW_TEXT,
          null,
          false
        );

        const textNodes = [];
        let node;

        while (node = walker.nextNode()) {
          if (node.textContent.trim() === originalText.trim()) {
            textNodes.push(node);
          }
        }

        textNodes.forEach(textNode => {
          textNode.textContent = translatedText;
        });
      });
    },

    async initializeMenuApp() {
      console.log('Initializing Menu App...');
      await this.loadMenuItems();
    },

    async loadHeroImage() {
      try {
        console.log('Loading hero image for Menu page...');
        const response = await apiService.get('/content/homepage/hero');

        // Apply background image if available
        if (response.image_url && this.$refs.menuHero) {
          this.applyHeroBackground(response.image_url);
        }
      } catch (error) {
        console.error('Failed to load hero image:', error);
      }
    },

    applyHeroBackground(imageUrl) {
      if (this.$refs.menuHero) {
        this.$refs.menuHero.style.backgroundImage = `linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('${imageUrl}')`;
        this.$refs.menuHero.style.backgroundSize = 'cover';
        this.$refs.menuHero.style.backgroundPosition = 'center';
        this.$refs.menuHero.style.backgroundAttachment = 'fixed';
      }
    },

    async loadMenuItems() {
      this.loading = true;
      try {
        // Load menu items from API for each category
        const categories = ['restaurant', 'accommodation', 'afternoon_tea'];
        this.menuItems = {};

        for (const category of categories) {
          try {
            const response = await apiService.get(`/menus/?category=${category}&available=true`);
            if (response && response.menus) {
              // Transform menu items to match new gallery-style structure
              this.menuItems[category] = response.menus.map(item => ({
                id: item.id,
                name: item.title, // Changed from name to title
                description: item.description,
                price: null, // No price in new structure
                image: item.image_url || '/assets/images/placeholder.jpg', // Single image URL
                images: [item.image_url], // Convert single image to array for compatibility
                ingredients: item.description, // Use description as ingredients for now
                alt_text: item.alt_text
              }));
            } else {
              this.menuItems[category] = [];
            }
          } catch (error) {
            console.error(`Error loading ${category} menu:`, error);
            this.menuItems[category] = [];
          }
        }

        console.log('Menu items loaded:', this.menuItems);

        // Load menu images
        await this.loadMenuImages();

        // Setup overlays after items are loaded
        this.$nextTick(() => {
          this.setupMenuItemOverlays();
        });
      } catch (error) {
        console.error('Failed to load menu items:', error);
        this.showNotification('Failed to load menu items', 'error');
      } finally {
        this.loading = false;
      }
    },

    async loadMenuImages() {
      try {
        console.log('Loading menu images...');
        const response = await apiService.get('/menus/');

        if (response && response.menus) {
          console.log('Raw menu data from API:', response.menus);

          // Extract all menu images that have menu_image field
          const filteredMenus = response.menus.filter(menu => menu.menu_image && menu.is_active);
          console.log('Filtered menus with menu_image:', filteredMenus);

          this.menuImages = filteredMenus
            .map(menu => ({
              id: menu.id,
              url: menu.menu_image,
              alt: menu.alt_text || menu.title || `Menu ${menu.category}`,
              title: menu.title,
              description: menu.description,
              category: menu.category,
              created_at: menu.created_at
            }))
            .sort((a, b) => {
              // First sort by category order: restaurant, accommodation, afternoon_tea
              const categoryOrder = { 'restaurant': 1, 'accommodation': 2, 'afternoon_tea': 3 };
              const categoryDiff = (categoryOrder[a.category] || 999) - (categoryOrder[b.category] || 999);

              if (categoryDiff !== 0) {
                return categoryDiff;
              }

              // Within same category, sort by ID ascending (oldest first) to maintain fixed positions
              if (a.id && b.id) {
                return a.id - b.id;
              }

              // Fallback to creation date if ID not available
              if (a.created_at && b.created_at) {
                return new Date(a.created_at) - new Date(b.created_at);
              }

              return 0;
            });

          console.log('Final sorted menuImages:', this.menuImages);
        } else {
          // Fallback to default menu images if no data
          this.menuImages = [
            {
              id: 1,
              url: '/assets/images/menu-restaurant.jpg',
              alt: 'Menu Nhà Hàng',
              title: 'Menu Nhà Hàng',
              description: 'Thực đơn đa dạng với các món ăn đặc sản địa phương',
              category: 'restaurant'
            },
            {
              id: 2,
              url: '/assets/images/menu-accommodation.jpg',
              alt: 'Menu Lưu Trú',
              title: 'Menu Lưu Trú',
              description: 'Dịch vụ ăn uống tại phòng và các gói combo',
              category: 'accommodation'
            },
            {
              id: 3,
              url: '/assets/images/menu-tea.jpg',
              alt: 'Set Trà Chiều',
              title: 'Set Trà Chiều',
              description: 'Thưởng thức trà chiều với bánh ngọt và view đẹp',
              category: 'afternoon_tea'
            }
          ];
        }

        console.log('Menu images loaded:', this.menuImages);
      } catch (error) {
        console.error('Failed to load menu images:', error);
        // Use fallback images on error
        this.menuImages = [
          {
            id: 1,
            url: '/assets/images/menu-restaurant.jpg',
            alt: 'Menu Nhà Hàng',
            title: 'Menu Nhà Hàng',
            description: 'Thực đơn đa dạng với các món ăn đặc sản địa phương',
            category: 'restaurant'
          },
          {
            id: 2,
            url: '/assets/images/menu-accommodation.jpg',
            alt: 'Menu Lưu Trú',
            title: 'Menu Lưu Trú',
            description: 'Dịch vụ ăn uống tại phòng và các gói combo',
            category: 'accommodation'
          },
          {
            id: 3,
            url: '/assets/images/menu-tea.jpg',
            alt: 'Set Trà Chiều',
            title: 'Set Trà Chiều',
            description: 'Thưởng thức trà chiều với bánh ngọt và view đẹp',
            category: 'afternoon_tea'
          }
        ];
      }
    },

    getCategoryDisplayName(category) {
      const categoryNames = {
        'restaurant': 'Nhà Hàng',
        'accommodation': 'Lưu Trú',
        'afternoon_tea': 'Trà Chiều'
      };
      return categoryNames[category] || category;
    },

    setActiveTab(index) {
      this.activeTab = index;

      // Setup overlays for new tab content
      this.$nextTick(() => {
        this.setupMenuItemOverlays();
      });
    },

    openMenuImageModal(menuImage, index) {
      this.currentMenuImageIndex = index;
      this.selectedMenuImage = menuImage;
      this.showMenuImageModal = true;
      document.body.style.overflow = 'hidden';

      // Adjust overlay shape after modal opens
      this.$nextTick(() => {
        this.adjustModalOverlayShape();
      });
    },

    closeMenuImageModal() {
      this.showMenuImageModal = false;
      this.selectedMenuImage = {};
      this.currentMenuImageIndex = 0;
      document.body.style.overflow = 'auto';
    },

    navigateMenuImageModal(direction) {
      const newIndex = this.currentMenuImageIndex + direction;
      if (newIndex >= 0 && newIndex < this.currentMenuImages.length) {
        this.currentMenuImageIndex = newIndex;
        this.selectedMenuImage = this.currentMenuImages[newIndex];

        // Adjust overlay shape for new image
        this.$nextTick(() => {
          this.adjustModalOverlayShape();
        });
      }
    },

    openMenuModal(item) {
      // Find the index of the clicked item in current menu items
      this.currentModalIndex = this.currentMenuItems.findIndex(menuItem => menuItem.id === item.id);
      if (this.currentModalIndex === -1) this.currentModalIndex = 0;

      this.selectedMenuItem = item;
      this.showMenuModal = true;
      document.body.style.overflow = 'hidden';

      // Adjust overlay shape after modal opens
      this.$nextTick(() => {
        this.adjustModalOverlayShape();
      });
    },

    closeMenuModal() {
      this.showMenuModal = false;
      this.selectedMenuItem = {};
      this.currentModalIndex = 0;
      document.body.style.overflow = 'auto';
    },



    navigateModal(direction) {
      const newIndex = this.currentModalIndex + direction;
      if (newIndex >= 0 && newIndex < this.currentMenuItems.length) {
        this.currentModalIndex = newIndex;
        this.selectedMenuItem = this.currentMenuItems[newIndex];

        // Adjust overlay shape for new image
        this.$nextTick(() => {
          this.adjustModalOverlayShape();
        });
      }
    },

    adjustModalOverlayShape() {
      const overlay = this.$refs.modalOverlay;
      const img = overlay?.parentElement?.querySelector('img');

      if (!overlay || !img) return;

      // Wait for image to load
      if (!img.complete) {
        img.onload = () => this.adjustModalOverlayShape();
        return;
      }

      const imgRect = img.getBoundingClientRect();
      const containerRect = img.parentElement.getBoundingClientRect();

      // Calculate the actual displayed image dimensions
      const imgAspectRatio = img.naturalWidth / img.naturalHeight;
      const containerAspectRatio = containerRect.width / containerRect.height;

      let displayedWidth, displayedHeight, offsetX, offsetY;

      if (imgAspectRatio > containerAspectRatio) {
        // Image is wider - fit to container width
        displayedWidth = containerRect.width;
        displayedHeight = containerRect.width / imgAspectRatio;
        offsetX = 0;
        offsetY = (containerRect.height - displayedHeight) / 2;
      } else {
        // Image is taller - fit to container height
        displayedHeight = containerRect.height;
        displayedWidth = containerRect.height * imgAspectRatio;
        offsetX = (containerRect.width - displayedWidth) / 2;
        offsetY = 0;
      }

      // Apply the calculated dimensions to overlay
      overlay.style.left = `${offsetX}px`;
      overlay.style.top = `${offsetY}px`;
      overlay.style.width = `${displayedWidth}px`;
      overlay.style.height = `${displayedHeight}px`;
    },

    setupMenuItemOverlays() {
      this.$nextTick(() => {
        const menuItems = document.querySelectorAll('.menu-item');
        menuItems.forEach(item => {
          const img = item.querySelector('img');
          const overlay = item.querySelector('.menu-item-overlay');

          if (!img || !overlay) return;

          const adjustOverlay = () => {
            if (!img.complete) {
              img.onload = adjustOverlay;
              return;
            }

            // Get container dimensions
            const container = img.parentElement;
            const containerWidth = container.offsetWidth;
            const containerHeight = container.offsetHeight;

            // Calculate the actual displayed image dimensions
            const imgAspectRatio = img.naturalWidth / img.naturalHeight;
            const containerAspectRatio = containerWidth / containerHeight;

            let displayedWidth, displayedHeight, offsetX, offsetY;

            if (imgAspectRatio > containerAspectRatio) {
              // Image is wider - fit to container width
              displayedWidth = containerWidth;
              displayedHeight = containerWidth / imgAspectRatio;
              offsetX = 0;
              offsetY = (containerHeight - displayedHeight) / 2;
            } else {
              // Image is taller - fit to container height
              displayedHeight = containerHeight;
              displayedWidth = containerHeight * imgAspectRatio;
              offsetX = (containerWidth - displayedWidth) / 2;
              offsetY = 0;
            }

            // Apply the calculated dimensions to overlay
            overlay.style.position = 'absolute';
            overlay.style.left = `${offsetX}px`;
            overlay.style.top = `${offsetY}px`;
            overlay.style.width = `${displayedWidth}px`;
            overlay.style.height = `${displayedHeight}px`;
          };

          adjustOverlay();
        });
      });
    },

    getHeroBackgroundStyle() {
      return {
        background: `linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('${this.heroBackground}') center/cover no-repeat`
      };
    },

    handleImageError(event) {
      event.target.src = '/assets/images/placeholder-menu.jpg';
    },

    formatPrice(price) {
      return new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
      }).format(price);
    },

    handleCallClick() {
      if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
        window.location.href = 'tel:+84941214444';
      } else {
        window.location.href = '/contact.html';
      }
    },

    toggleMenu() {
      const navMenu = document.getElementById('navMenu');
      const hamburger = document.getElementById('hamburger');

      if (navMenu && hamburger) {
        navMenu.classList.toggle('active');
        hamburger.classList.toggle('active');
      }
    },

    showNotification(message, type = 'success') {
      this.notification = {
        show: true,
        message,
        type
      };
      
      setTimeout(() => {
        this.notification.show = false;
      }, 3000);
    }
  }
};
</script>

<style scoped>
/* Logo image styling */
.logo-img {
  height: 100%;
  width: auto;
  max-width: 200px;
  object-fit: contain;
}

/* Menu App Styles */
#menu-app {
  min-height: 100vh;
  background: #f8f9fa;
}

/* Hero Section */
.menu-hero {
  height: 60vh;
  min-height: 500px;
  margin-top: 0; /* Remove margin-top, use body padding instead */
  background: linear-gradient(135deg, #2d2d2d 0%, #1a1a1a 100%);
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  display: flex;
  align-items: center;
  position: relative;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1;
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  color: white;
}

.hero-title {
  font-family: 'SVN-Megante', serif;
  font-size: 3.5rem;
  font-weight: normal;
  margin-bottom: 1.5rem;
  color: white;
}

.hero-subtitle {
  font-family: 'SVN-Alluring', serif;
  font-size: 1.2rem;
  font-weight: normal;
  margin-bottom: 2rem;
  color: #C4A962;
  letter-spacing: 2px;
  text-transform: uppercase;
}

.hero-title .highlight {
  color: #C4A962;
}

.hero-description {
  font-family: 'Poppins', sans-serif;
  font-size: 1.1rem;
  line-height: 1.8;
  opacity: 0.9;
  max-width: 600px;
  margin: 0 auto;
}

/* Menu Section */
.menu-section {
  padding: 80px 0;
  background: #f8f9fa;
}

.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-header h2 {
  font-size: 3rem;
  font-weight: 700;
  color: #333;
  margin-bottom: 20px;
}

.section-header p {
  font-size: 1.1rem;
  color: #666;
  max-width: 600px;
  margin: 0 auto;
}

/* Menu Tabs */
.menu-tabs {
  display: flex;
  justify-content: space-between;
  gap: 0;
  margin-bottom: 50px;
  width: 100vw;
  margin-left: calc(-50vw + 50%);
  padding: 0 20px;
  box-sizing: border-box;
}

.menu-tab {
  background: white;
  border: 2px solid #e9ecef;
  border-radius: 15px;
  padding: 20px 30px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  flex: 1;
  margin: 0 5px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.menu-tab:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #C4A962;
}

.menu-tab.active {
  background: #C4A962;
  border-color: #C4A962;
  color: white;
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(196, 169, 98, 0.3);
}

.menu-tab i {
  font-size: 2rem;
  margin-bottom: 5px;
}

.menu-tab span {
  font-weight: 600;
  font-size: 1rem;
}

/* Menu Content */
.menu-content {
  min-height: 400px;
}

/* Menu Images Grid - Default Layout for Restaurant & Tea */
.menu-images-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30px;
  margin-top: 30px;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

/* Menu Accommodation Block Layout */
.menu-accommodation-grid {
  display: flex;
  flex-direction: column;
  gap: 40px;
  margin-top: 30px;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

.menu-image-card {
  background: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.menu-image-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.2);
}

.menu-image-container {
  position: relative;
  width: 100%;
  overflow: hidden;
  background: white;
}

.menu-image-container img {
  width: 100%;
  height: auto;
  object-fit: contain;
  transition: transform 0.3s ease;
  display: block;
}

.menu-image-card:hover .menu-image-container img {
  transform: scale(1.05);
}



.menu-image-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: white;
  padding: 30px 25px 25px;
  text-align: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.menu-image-card:hover .menu-image-info {
  opacity: 1;
}

.menu-image-info h3 {
  color: white;
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 10px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.menu-image-info p {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1rem;
  line-height: 1.6;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* Menu Grid */
.menu-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-top: 20px;
  justify-content: space-between;
}

.menu-item {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  flex: 0 0 calc(50% - 10px);
  max-width: calc(50% - 10px);
}

/* Menu Block Layout for Accommodation */
.menu-block {
  background: white;
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  margin-bottom: 40px;
  display: flex;
  align-items: center;
  gap: 40px;
  min-height: 400px;
}

.menu-block:nth-child(even) {
  flex-direction: row-reverse;
}

.menu-block-content {
  flex: 1;
  text-align: center;
  cursor: pointer;
}

.menu-block-content-image {
  position: relative;
  width: 100%;
  height: 400px;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.menu-block-content-image:hover {
  transform: translateY(-5px);
}

.menu-block-content-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.menu-block-content-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: white;
  padding: 30px 20px 20px;
  text-align: left;
}

.menu-block-content-overlay h3 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 8px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.menu-block-content-overlay p {
  font-size: 0.95rem;
  opacity: 0.9;
  margin: 0;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.menu-block-title {
  font-size: 2.5rem;
  font-weight: 300;
  color: #2c3e50;
  margin-bottom: 20px;
  text-transform: uppercase;
  letter-spacing: 2px;
}

.menu-block-subtitle {
  font-size: 1.2rem;
  color: #7f8c8d;
  margin-bottom: 30px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.menu-block-description {
  background: #f8f9fa;
  border-radius: 15px;
  padding: 30px;
  margin-bottom: 20px;
}

.menu-block-list {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  text-align: left;
}

.menu-block-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px dotted #ddd;
  font-size: 0.95rem;
}

.menu-block-item:last-child {
  border-bottom: none;
}

.menu-block-item-name {
  color: #2c3e50;
  font-weight: 500;
}

.menu-block-item-description {
  color: #7f8c8d;
  font-size: 0.85rem;
  font-style: italic;
}

.menu-block-images {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.menu-block-main-image {
  width: 100%;
  height: 250px;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.3s ease;
}

.menu-block-main-image:hover {
  transform: translateY(-3px);
}

.menu-block-main-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.menu-block-sub-images {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
}

.menu-block-sub-image {
  height: 100px;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.3s ease;
}

.menu-block-sub-image:hover {
  transform: translateY(-2px);
}

.menu-block-sub-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.menu-item:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);
}

.menu-item-image {
  position: relative;
  height: 300px;
  overflow: hidden;
  width: 100%;
}

.menu-item-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  background: #f8f9fa;
  transition: transform 0.3s ease;
}

.menu-item:hover .menu-item-image img {
  transform: scale(1.1);
}

.menu-item-overlay {
  position: absolute;
  background: rgba(196, 169, 98, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  color: white;
  border-radius: 15px;
  /* Will be positioned dynamically by JavaScript */
}

.menu-item:hover .menu-item-overlay {
  opacity: 1;
}

.menu-item-overlay i {
  font-size: 2rem;
  margin-bottom: 10px;
}

/* Menu Tea Block Layout */
.menu-tea-grid {
  display: flex;
  flex-direction: column;
  gap: 40px;
  margin-top: 30px;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
}

.menu-tea-block {
  display: flex;
  gap: 30px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-height: 300px;
}

.menu-tea-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding-right: 20px;
}

.menu-tea-title {
  font-size: 2.2rem;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 15px;
  line-height: 1.2;
}

.menu-tea-price {
  font-size: 1.3rem;
  color: #e74c3c;
  font-weight: 600;
  margin-bottom: 10px;
}

.menu-tea-description {
  font-size: 1.1rem;
  color: #7f8c8d;
  line-height: 1.4;
}

.menu-tea-images {
  flex: 1.5;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.menu-tea-small-images {
  display: flex;
  gap: 15px;
  height: 140px;
}

.menu-tea-small-image {
  flex: 1;
  border-radius: 10px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.menu-tea-small-image:hover {
  transform: scale(1.05);
}

.menu-tea-small-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.menu-tea-large-image {
  height: 140px;
  border-radius: 10px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.menu-tea-large-image:hover {
  transform: scale(1.02);
}

.menu-tea-large-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Menu Image Modal */
.menu-image-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 20px;
}

.menu-image-modal-content {
  background: white;
  border-radius: 20px;
  max-width: 1200px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
}

.menu-image-modal-close {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  font-size: 24px;
  cursor: pointer;
  z-index: 10;
  transition: background 0.3s ease;
}

.menu-image-modal-close:hover {
  background: rgba(0, 0, 0, 0.7);
}

/* Menu Image Modal Navigation */
.menu-image-modal-nav {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  font-size: 24px;
  cursor: pointer;
  z-index: 10;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.menu-image-modal-nav:hover:not(:disabled) {
  background: rgba(0, 0, 0, 0.8);
  transform: translateY(-50%) scale(1.1);
}

.menu-image-modal-nav:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.menu-image-modal-prev {
  left: 20px;
}

.menu-image-modal-next {
  right: 20px;
}

.menu-image-modal-image {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  height: 600px;
}

.menu-image-modal-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  background: #f8f9fa;
}

.menu-image-modal-overlay {
  position: absolute;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: 60px 40px 40px;
  color: white;
  border-radius: 20px;
  opacity: 0;
  transition: opacity 0.3s ease;
  bottom: 0;
  left: 0;
  right: 0;
}

.menu-image-modal-image:hover .menu-image-modal-overlay {
  opacity: 1;
}

.menu-image-modal-info {
  display: flex;
  flex-direction: column;
}

.menu-image-modal-info h2 {
  font-size: 2rem;
  font-weight: 700;
  color: white;
  margin-bottom: 15px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.menu-image-modal-info p {
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  font-size: 1.1rem;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

/* Menu Modal */
.menu-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 20px;
}

.menu-modal-content {
  background: white;
  border-radius: 20px;
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  display: flex;
  flex-direction: column;
}

.menu-modal-close {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  font-size: 1.5rem;
  cursor: pointer;
  z-index: 10;
  transition: background 0.3s ease;
}

.menu-modal-close:hover {
  background: rgba(0, 0, 0, 0.7);
}

/* Navigation Buttons */
.menu-modal-nav {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  font-size: 24px;
  cursor: pointer;
  z-index: 15;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.menu-modal-nav:hover:not(:disabled) {
  background: rgba(0, 0, 0, 0.8);
  transform: translateY(-50%) scale(1.1);
}

.menu-modal-nav:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.menu-modal-prev {
  left: 20px;
}

.menu-modal-next {
  right: 20px;
}

.menu-modal-image {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  height: 500px;
}

.menu-modal-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  background: #f8f9fa;
}

.menu-modal-overlay {
  position: absolute;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: 60px 40px 40px;
  color: white;
  border-radius: 20px;
  opacity: 0;
  transition: opacity 0.3s ease;
  /* Position will be set dynamically by JavaScript */
  bottom: 0;
  left: 0;
  right: 0;
}

.menu-modal-image:hover .menu-modal-overlay {
  opacity: 1;
}

.menu-modal-info {
  display: flex;
  flex-direction: column;
}

.menu-modal-info h2 {
  font-size: 2rem;
  font-weight: 700;
  color: white;
  margin-bottom: 15px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.menu-modal-description {
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  font-size: 1.1rem;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

/* Icons */
.icon-restaurant::before { content: "🍽️"; }
.icon-accommodation::before { content: "🏕️"; }
.icon-tea::before { content: "🍵"; }
.icon-zoom::before { content: "🔍"; }

/* Notification */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 15px 25px;
  border-radius: 8px;
  color: white;
  font-weight: 500;
  z-index: 3000;
  animation: slideIn 0.3s ease;
}

.notification.success {
  background: #28a745;
}

.notification.error {
  background: #dc3545;
}

.notification.info {
  background: #17a2b8;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .menu-hero {
    height: 50vh;
    min-height: 400px;
    margin-top: 70px;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .section-header h2 {
    font-size: 2rem;
  }

  .menu-tabs {
    flex-direction: column;
    gap: 15px;
    width: 100vw;
    margin-left: calc(-50vw + 50%);
    padding: 0 15px;
  }

  .menu-tab {
    margin: 0;
    padding: 15px 25px;
    flex: none;
  }

  .menu-grid {
    flex-direction: column;
    gap: 20px;
  }

  .menu-item {
    flex: none;
    max-width: 100%;
  }



  .menu-modal-content {
    max-width: 95%;
  }

  .menu-modal-image {
    height: 350px;
  }

  .menu-modal-overlay {
    padding: 40px 25px 25px;
  }

  .menu-modal-info h2 {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .menu-hero {
    height: 45vh;
    min-height: 350px;
    margin-top: 65px;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-description {
    font-size: 1rem;
  }

  .menu-section {
    padding: 60px 0;
  }

  .menu-tab {
    padding: 12px 20px;
  }

  .menu-tab span {
    font-size: 0.9rem;
  }

  .menu-images-grid {
    grid-template-columns: 1fr;
    gap: 20px;
    max-width: 100%;
    margin-left: 10px;
    margin-right: 10px;
  }

  .menu-accommodation-grid {
    gap: 20px;
    max-width: 100%;
    margin-left: 10px;
    margin-right: 10px;
  }

  .menu-tea-grid {
    gap: 20px;
    max-width: 100%;
    margin-left: 10px;
    margin-right: 10px;
  }

  .menu-tea-block {
    flex-direction: column !important;
    padding: 20px;
    gap: 20px;
    min-height: auto;
  }

  .menu-tea-title {
    font-size: 1.8rem;
  }

  .menu-tea-price {
    font-size: 1.1rem;
  }

  .menu-tea-description {
    font-size: 1rem;
  }

  .menu-tea-images {
    flex: none;
  }

  .menu-tea-small-images {
    height: 120px;
    gap: 10px;
  }

  .menu-tea-large-image {
    height: 120px;
  }

  /* Mobile responsive for menu blocks */
  .menu-block {
    flex-direction: column !important;
    padding: 20px;
    gap: 20px;
    min-height: auto;
  }

  .menu-block-title {
    font-size: 1.8rem;
  }

  .menu-block-subtitle {
    font-size: 1rem;
  }

  .menu-block-list {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .menu-block-content-image {
    height: 250px;
  }

  .menu-block-main-image {
    height: 150px;
  }

  .menu-block-sub-images {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }

  .menu-block-sub-image {
    height: 60px;
  }

  .menu-image-container {
    /* Remove fixed height for mobile */
  }

  .menu-image-modal-content {
    max-width: 95%;
  }

  .menu-image-modal-image {
    height: 450px;
  }

  .menu-modal-image {
    height: 300px;
  }

  .menu-modal-overlay {
    padding: 30px 20px 20px;
  }

  .menu-modal-nav {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }

  .menu-modal-prev {
    left: 10px;
  }

  .menu-modal-next {
    right: 10px;
  }

  .menu-image-container {
    /* Remove fixed height for small mobile */
  }

  .menu-image-modal-image {
    height: 350px;
  }

  .menu-image-modal-nav {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }

  .menu-image-modal-prev {
    left: 10px;
  }

  .menu-image-modal-next {
    right: 10px;
  }
}
</style>
