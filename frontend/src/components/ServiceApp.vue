<template>
  <div id="service-app" class="service-page">
    <!-- Header -->
    <header class="header">
      <div class="container">
        <div class="nav-brand">
          <a href="/" class="logo-link">
            <div class="logo">
              <img
                src="/logo/logo-ngang-08.png"
                alt="Phong Nha Valley"
                class="logo-img"
              />
            </div>
          </a>
        </div>
        <nav class="nav-menu" id="navMenu">
          <ul>
            <li><a href="/">Trang Chủ</a></li>
            <li><a href="/about.html">Về Phong Nha</a></li>
            <li><a href="/services.html" class="active">Dịch Vụ</a></li>
            <li><a href="/gallery.html">Thư Viện</a></li>
            <li><a href="/menu.html">Menu</a></li>
            <li><a href="/contact.html"><PERSON><PERSON><PERSON></a></li>
            <li><a href="#" class="btn-call" @click="handleCallClick">G<PERSON>i Ngay</a></li>
          </ul>
        </nav>

        <!-- Language Switcher -->
        <div class="language-switcher">
          <button
            class="lang-btn"
            :class="{ active: currentLanguage === 'vi' }"
            @click="switchLanguage('vi')"
            :disabled="translating"
            title="Tiếng Việt"
          >
            <span class="flag-emoji">🇻🇳</span>
            VI
          </button>
          <button
            class="lang-btn"
            :class="{ active: currentLanguage === 'en' }"
            @click="switchLanguage('en')"
            :disabled="translating"
            title="English - Powered by Google Translate"
          >
            <span class="flag-emoji">🇬🇧</span>
            EN
            <span v-if="currentLanguage === 'en'" class="google-icon">🌐</span>
          </button>
          <div v-if="translating" class="translation-loading">
            <span class="loading-spinner"></span>
            <span>Translating...</span>
          </div>
        </div>
        <div class="hamburger" id="hamburger" @click="toggleMenu">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>
    </header>

    <!-- Hero Section -->
    <section class="service-hero" ref="serviceHero">
      <div class="hero-content">
        <h1 class="hero-title">Dịch vụ</h1>
      </div>
    </section>

    <!-- Service Content Section -->
    <section class="service-content">
      <div class="container">
        <!-- Loading State -->
        <div v-if="loading" class="service-loading">
          <div class="loading-spinner"></div>
          <p>Đang tải thông tin dịch vụ...</p>
        </div>

        <!-- Error State -->
        <div v-else-if="error" class="service-error">
          <h2>Không tìm thấy dịch vụ</h2>
          <p>{{ error }}</p>
          <a href="/" class="btn-primary">Quay về trang chủ</a>
        </div>

        <!-- Service Details -->
        <div v-else-if="service" class="service-details">
          <!-- Service Header -->
          <div class="service-header">
            <div class="service-type-badge">{{ formatServiceType(service.type) }}</div>
            <h1 class="service-title">{{ service.name }}</h1>
          </div>

          <!-- Service Info Card -->
          <div class="service-info-card">
            <div class="info-content">
              <div class="info-item">
                <strong>Giá vé:</strong>
                <span>{{ formatPrice(service.price) }}</span>
              </div>
              <div v-if="service.child_price" class="info-item">
                <strong>Giá trẻ em:</strong>
                <span>{{ formatPrice(service.child_price) }}</span>
              </div>
              <div class="info-item">
                <strong>Giờ hoạt động:</strong>
                <span>{{ formatTime(service.open_time) }} - {{ formatTime(service.close_time) }}</span>
              </div>
              <div class="info-item">
                <strong>Sức chứa:</strong>
                <span>{{ service.capacity }} người</span>
              </div>
              <div v-if="service.inclusions && service.inclusions.length > 0" class="info-item">
                <strong>Bao gồm:</strong>
                <ul class="inclusion-list">
                  <li v-for="inclusion in service.inclusions" :key="inclusion">{{ inclusion }}</li>
                </ul>
              </div>
              <div v-if="service.requirements && service.requirements.length > 0" class="info-item">
                <strong>Yêu cầu:</strong>
                <ul class="requirement-list">
                  <li v-for="requirement in service.requirements" :key="requirement">{{ requirement }}</li>
                </ul>
              </div>
            </div>
          </div>

          <!-- Main Content Section (Image + Description) -->
          <div v-if="service.description" class="main-content-section">
            <div class="main-image">
              <img v-if="service.images && service.images.length > 0"
                   :src="buildImageUrl(service.images[0])"
                   :alt="service.name"
                   @error="handleImageError">
              <div v-else class="placeholder-box">
                <span>Hình ảnh dịch vụ</span>
              </div>
            </div>
            <div class="main-description">
              <p class="service-detail-description">{{ service.description }}</p>
            </div>
          </div>

          <!-- Service Gallery -->
          <div v-if="getGalleryImages().length > 0" class="service-gallery">
            <div class="gallery-grid">
              <div v-for="(image, index) in getGalleryImages()"
                   :key="index"
                   class="gallery-item"
                   @click="openLightbox(image, index)">
                <img :src="image" :alt="`${service.name} - Hình ${index + 1}`" @error="handleImageError">
              </div>
            </div>


          </div>

          <!-- Placeholder Gallery (if no images) -->
          <div v-else class="service-gallery">
            <h3>Hình ảnh dịch vụ</h3>
            <div class="gallery-grid">
              <div v-for="n in 6" :key="n" class="gallery-item">
                <div class="placeholder-box">
                  <span>Hình {{ n }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Book Now Section -->
          <div class="service-booking">
            <div class="booking-card">
              <h3>Đặt dịch vụ ngay</h3>
              <p>Liên hệ với chúng tôi để đặt {{ service.name }}</p>
              <button class="btn-book-now" @click="scrollToBooking">
                Đặt ngay
              </button>
            </div>
          </div>



        </div>
      </div>
    </section>

    <!-- Other Services Section -->
    <section class="other-services">
      <div class="container">
        <div class="other-services-section">
          <h2 class="section-title">DỊCH VỤ KHÁC</h2>

          <div v-if="otherServicesLoading" class="loading-state">
            <p>Đang tải dịch vụ...</p>
          </div>

          <div v-else-if="otherServices.length === 0" class="no-services-state">
            <p>Không có dịch vụ khác để hiển thị</p>
          </div>

          <div v-else class="services-carousel">
            <!-- Navigation Arrows -->
            <button
              class="carousel-nav prev"
              @click="prevSlide"
              :disabled="currentSlide === 0"
            >
              <i class="fas fa-chevron-left"></i>
            </button>

            <button
              class="carousel-nav next"
              @click="nextSlide"
              :disabled="currentSlide >= maxSlide"
            >
              <i class="fas fa-chevron-right"></i>
            </button>

            <!-- Services Container -->
            <div class="services-container">
              <div
                class="services-track"
                :style="{ transform: `translateX(-${currentSlide * slideWidth}%)` }"
              >
                <div
                  v-for="otherService in otherServices"
                  :key="otherService.id"
                  class="service-slide"
                  :style="{ flex: `0 0 ${slideWidth}%` }"
                >
                  <div class="service-card" @click="goToServicePage(otherService, $event)">
                    <div class="service-image">
                      <img v-if="otherService.images && otherService.images.length > 0"
                           :src="buildImageUrl(otherService.images[0])"
                           :alt="otherService.name"
                           @error="handleImageError">
                      <div v-else class="placeholder-box">
                        <span>{{ otherService.name }}</span>
                      </div>
                      <div
                        class="service-status-badge"
                        :class="otherService.is_active ? 'available' : 'unavailable'"
                      >
                        {{ otherService.is_active ? 'AVAILABLE' : 'COMING SOON' }}
                      </div>
                    </div>
                    <div class="service-content">
                      <div class="service-category">{{ formatServiceType(otherService.type) }}</div>
                      <h3 class="service-title">{{ otherService.name }}</h3>
                      <p class="service-description">{{ otherService.description }}</p>
                      <div class="service-pricing">
                        <span class="main-price">{{ formatPrice(otherService.price) }}</span>
                        <span v-if="otherService.child_price" class="child-price">
                          Child: {{ formatPrice(otherService.child_price) }}
                        </span>
                      </div>
                      <div class="service-time">
                        <i class="fas fa-clock"></i> {{ formatTime(otherService.open_time) }} - {{ formatTime(otherService.close_time) }}
                      </div>
                      <button
                        class="btn-book-now"
                        @click.stop="viewServiceDetails(otherService, $event)"
                        :disabled="!otherService.is_active"
                      >
                        {{ otherService.is_active ? 'BOOK NOW' : 'COMING SOON' }}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Dots Indicator -->
            <div class="carousel-dots">
              <button
                v-for="(dot, index) in totalDots"
                :key="index"
                class="dot"
                :class="{ active: index === currentSlide }"
                @click="goToSlide(index)"
              ></button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact">
      <div class="container">
        <div class="contact-content">
          <div class="contact-info">
            <h2>Contact Us</h2>
            <div class="contact-details">
              <div class="contact-item">
                <h3>Phone</h3>
                <p>{{ contactInfo.phone }}</p>
              </div>
              <div class="contact-item">
                <h3>Email</h3>
                <p>{{ contactInfo.email }}</p>
              </div>
              <div class="contact-item">
                <h3>Address</h3>
                <p>{{ contactInfo.address }}<br>{{ contactInfo.city }}</p>
              </div>
              <div class="contact-item">
                <h3>Opening Hours</h3>
                <p>{{ contactInfo.hours }}</p>
              </div>
            </div>
            <div class="social-links">
              <h3>Follow Us</h3>
              <div class="social-buttons">
                <a href="#" class="social-link instagram">Instagram</a>
                <a href="#" class="social-link facebook">Facebook</a>
              </div>
            </div>
          </div>

          <div class="booking-form">
            <h3>Book Your Experience</h3>
            <form @submit.prevent="handleBookingSubmit" :class="{ loading: bookingLoading }">
              <div class="form-group">
                <label for="service">Service</label>
                <select v-model="bookingForm.service" id="service" name="service" required>
                  <option value="">Select a service</option>
                  <option
                    v-for="service in otherServices"
                    :key="service.id"
                    :value="service.id"
                  >
                    {{ service.name }} - {{ formatPrice(service.price) }}
                  </option>
                  <!-- Include current service -->
                  <option v-if="service" :value="service.id">
                    {{ service.name }} - {{ formatPrice(service.price) }} (Current)
                  </option>
                </select>
              </div>

              <!-- Customer Information -->
              <div class="form-row">
                <div class="form-group">
                  <label for="customer_name">Full Name *</label>
                  <input
                    v-model="bookingForm.customer_name"
                    type="text"
                    id="customer_name"
                    name="customer_name"
                    required
                    placeholder="Enter your full name"
                  >
                </div>
                <div class="form-group">
                  <label for="customer_phone">Phone Number *</label>
                  <input
                    v-model="bookingForm.customer_phone"
                    type="tel"
                    id="customer_phone"
                    name="customer_phone"
                    required
                    placeholder="0123456789"
                  >
                </div>
              </div>

              <!-- Booking Date -->
              <div class="form-group">
                <label for="booking_date">Booking Date *</label>
                <input
                  v-model="bookingForm.booking_date"
                  type="date"
                  id="booking_date"
                  name="booking_date"
                  required
                  :min="todayDate"
                >
              </div>

              <!-- Guest Information -->
              <div class="form-row">
                <div class="form-group">
                  <label for="adults">Adults *</label>
                  <input
                    v-model.number="bookingForm.adults"
                    type="number"
                    id="adults"
                    name="adults"
                    min="1"
                    required
                  >
                </div>
                <div class="form-group">
                  <label for="children_6_to_11">Children (6-11 years)</label>
                  <input
                    v-model.number="bookingForm.children_6_to_11"
                    type="number"
                    id="children_6_to_11"
                    name="children_6_to_11"
                    min="0"
                  >
                </div>
              </div>

              <!-- Special Notes -->
              <div class="form-group">
                <label for="special_notes">Special Notes</label>
                <textarea
                  v-model="bookingForm.special_notes"
                  id="special_notes"
                  name="special_notes"
                  rows="3"
                  placeholder="Any special requirements..."
                ></textarea>
              </div>

              <button type="submit" class="btn-primary" :disabled="bookingLoading">
                {{ bookingLoading ? 'Processing...' : 'BOOK NOW' }}
              </button>
            </form>
          </div>
        </div>
      </div>
    </section>

    <!-- Lightbox Modal -->
    <div v-if="lightboxImage" class="lightbox-overlay" @click="closeLightbox">
      <div class="lightbox-content" @click.stop>
        <button class="lightbox-close" @click="closeLightbox">&times;</button>
        <img :src="lightboxImage" :alt="service.name">
      </div>
    </div>
  </div>
</template>

<script>
import { formatServiceType, formatPrice, formatTime, showNotification } from '../utils/helpers.js';
import { getServiceIdFromURL, isValidUUID, navigateToService } from '../utils/serviceUtils.js';
import apiService from '../utils/api.js';

export default {
  name: 'ServiceApp',
  data() {
    return {
      // Language
      currentLanguage: 'vi',
      translating: false,

      service: null,
      loading: true,
      error: null,
      lightboxImage: null,
      serviceId: null,

      // Other services data
      otherServices: [],
      otherServicesLoading: false,

      // Contact data
      contactInfo: {
        phone: '************',
        email: '<EMAIL>',
        address: 'Chày Lập Hamlet, Phúc Trạch Commune\nBố Trạch District, Quảng Bình Province',
        city: 'Quảng Bình Province',
        hours: '6:30 – 21:00'
      },

      // Booking form data
      bookingForm: {
        service: '',
        customer_name: '',
        customer_phone: '',
        booking_date: '',
        adults: 2,
        children_under_6: 0,
        children_6_to_11: 0,
        special_notes: ''
      },
      bookingLoading: false,
      navigationLoading: false,

      // Today's date for date input minimum
      todayDate: new Date().toISOString().split('T')[0],

      // Carousel data
      currentSlide: 0,
      slidesPerView: 3, // Show 3 services at once
      slideWidth: 33.333, // 100% / 3 slides


    };
  },

  computed: {
    maxSlide() {
      return Math.max(0, this.otherServices.length - this.slidesPerView);
    },

    totalDots() {
      return Math.ceil(this.otherServices.length / this.slidesPerView);
    }
  },

  async mounted() {
    console.log('ServiceApp mounted, initializing...');
    await this.initializeApp();
    console.log('ServiceApp initialized, loading other services...');
    await this.loadOtherServices();
    console.log('Other services loaded:', this.otherServices.length);
    await this.loadContactInfo();
    this.updateCarouselSettings();
    window.addEventListener('resize', this.updateCarouselSettings);

    // Auto-scroll to booking form if hash is #contact and pre-select current service
    this.$nextTick(() => {
      if (window.location.hash === '#contact') {
        // Pre-select current service in booking form
        if (this.service && this.service.id) {
          this.bookingForm.service = this.service.id;
        }

        setTimeout(() => {
          this.scrollToBooking();
        }, 500); // Small delay to ensure page is fully loaded
      }
    });
  },

  beforeUnmount() {
    window.removeEventListener('resize', this.updateCarouselSettings);
  },

  methods: {
    // Utility methods
    formatServiceType,
    formatPrice,
    formatTime,
    showNotification,

    // Language switcher methods
    switchLanguage(lang) {
      console.log('🔍 DEBUG ServiceApp: switchLanguage called with:', lang);
      if (this.translating || this.currentLanguage === lang) {
        console.log('🔍 DEBUG ServiceApp: Switch blocked - translating:', this.translating, 'currentLanguage:', this.currentLanguage);
        return;
      }

      console.log('🔍 DEBUG ServiceApp: Starting language switch to:', lang);
      this.translating = true;
      this.currentLanguage = lang;

      if (lang === 'en') {
        this.translateToEnglish();
      } else {
        this.restoreVietnamese();
      }
    },

    translateToEnglish() {
      const translations = {
        // Navigation
        'Home': 'Home',
        'About Us': 'About Us',
        'Services': 'Services',
        'Gallery': 'Gallery',
        'Menu': 'Menu',
        'Contact': 'Contact',
        'Call': 'Call',

        // Service page specific
        'Dịch vụ khác': 'Other Services',
        'Đặt ngay': 'Book Now',
        'Liên hệ': 'Contact',
        'Thông tin liên hệ': 'Contact Information',
        'Đặt dịch vụ': 'Book Service',

        // Form labels
        'Dịch vụ': 'Service',
        'Họ và tên': 'Full Name',
        'Số điện thoại': 'Phone Number',
        'Ngày đặt dịch vụ': 'Service Date',
        'Người lớn': 'Adults',
        'Trẻ em (6-11 tuổi)': 'Children (6-11 years)',
        'Trẻ em (Dưới 6 tuổi)': 'Children (Under 6 years)',
        'Ghi chú đặc biệt': 'Special Notes',
        'Đang xử lý...': 'Processing...'
      };

      this.applyTranslations(translations);

      setTimeout(() => {
        this.translating = false;
      }, 1000);
    },

    restoreVietnamese() {
      const reverseTranslations = {
        // Navigation
        'Home': 'Trang Chủ',
        'About Us': 'Về Phong Nha',
        'Services': 'Dịch Vụ',
        'Gallery': 'Thư viện',
        'Menu': 'Menu',
        'Contact': 'Liên hệ',
        'Call': 'Gọi ngay',

        // Service page specific
        'Other Services': 'Dịch vụ khác',
        'Book Now': 'Đặt ngay',
        'Contact': 'Liên hệ',
        'Contact Information': 'Thông tin liên hệ',
        'Book Service': 'Đặt dịch vụ',

        // Form labels
        'Service': 'Dịch vụ',
        'Full Name': 'Họ và tên',
        'Phone Number': 'Số điện thoại',
        'Service Date': 'Ngày đặt dịch vụ',
        'Adults': 'Người lớn',
        'Children (6-11 years)': 'Trẻ em (6-11 tuổi)',
        'Children (Under 6 years)': 'Trẻ em (Dưới 6 tuổi)',
        'Special Notes': 'Ghi chú đặc biệt',
        'Processing...': 'Đang xử lý...'
      };

      this.applyTranslations(reverseTranslations);

      setTimeout(() => {
        this.translating = false;
      }, 1000);
    },

    applyTranslations(translations) {
      Object.keys(translations).forEach(originalText => {
        const translatedText = translations[originalText];

        const walker = document.createTreeWalker(
          document.body,
          NodeFilter.SHOW_TEXT,
          null,
          false
        );

        const textNodes = [];
        let node;

        while (node = walker.nextNode()) {
          if (node.textContent.trim() === originalText.trim()) {
            textNodes.push(node);
          }
        }

        textNodes.forEach(textNode => {
          textNode.textContent = translatedText;
        });
      });
    },

    handleCallClick() {
      if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
        window.location.href = 'tel:+84941214444';
      } else {
        window.location.href = '/contact.html';
      }
    },

    toggleMenu() {
      const navMenu = document.getElementById('navMenu');
      const hamburger = document.getElementById('hamburger');

      if (navMenu && hamburger) {
        navMenu.classList.toggle('active');
        hamburger.classList.toggle('active');
      }
    },

    // Build proper image URL with path prefix
    buildImageUrl(imageUrl) {
      if (!imageUrl) return null;

      // If it's already a full URL or starts with /, use as is
      if (imageUrl.startsWith('http') || imageUrl.startsWith('/')) {
        console.log('🖼️ Using full URL:', imageUrl);
        return imageUrl;
      }

      // Otherwise, prepend the uploads path
      const fullUrl = `/assets/uploads/${imageUrl}`;
      console.log('🖼️ Building image URL:', imageUrl, '->', fullUrl);
      return fullUrl;
    },

    // Get gallery images based on description availability
    getGalleryImages() {
      console.log('🎯 getGalleryImages called');
      console.log('Service:', this.service);
      console.log('Service images:', this.service?.images);

      if (!this.service || !this.service.images || this.service.images.length === 0) {
        console.log('❌ No service or images available');
        return [];
      }

      // If service has description, start from image 2 (index 1)
      // If no description, start from image 1 (index 0)
      const startIndex = this.service.description ? 1 : 0;
      const slicedImages = this.service.images.slice(startIndex);
      const galleryImages = slicedImages.map(img => this.buildImageUrl(img));

      console.log('✅ Gallery images generated:', {
        startIndex,
        slicedImages,
        galleryImages
      });

      return galleryImages;
    },

    // Show all images (for future implementation)
    showAllImages() {
      // Could open a modal or navigate to full gallery
      console.log('Show all images clicked');
    },

    async initializeApp() {
      console.log('🔍 ServiceApp initializing...');
      console.log('🔗 Current URL:', window.location.href);

      // Get service ID using utility function
      this.serviceId = getServiceIdFromURL();

      if (!this.serviceId) {
        console.error('❌ No valid service ID found in URL');
        this.error = 'ID dịch vụ không hợp lệ hoặc không tồn tại trong URL';
        this.loading = false;
        return;
      }

      console.log('✅ Valid service ID found:', this.serviceId);
      await this.loadService();
    },

    async loadService() {
      try {
        this.loading = true;
        this.error = null;
        console.log('🔄 Loading service with ID:', this.serviceId);

        this.service = await apiService.getService(this.serviceId);

        if (!this.service) {
          throw new Error('Service data is empty');
        }

        console.log('✅ Service loaded successfully:', {
          id: this.service.id,
          name: this.service.name,
          type: this.service.type,
          active: this.service.is_active
        });

        // Parse JSON fields if they are strings
        if (this.service) {
          // Parse inclusions if it's a JSON string
          if (typeof this.service.inclusions === 'string') {
            try {
              this.service.inclusions = JSON.parse(this.service.inclusions);
            } catch (e) {
              console.warn('Failed to parse inclusions:', e);
              this.service.inclusions = [];
            }
          }

          // Parse requirements if it's a JSON string
          if (typeof this.service.requirements === 'string') {
            try {
              this.service.requirements = JSON.parse(this.service.requirements);
            } catch (e) {
              console.warn('Failed to parse requirements:', e);
              this.service.requirements = [];
            }
          }

          // Parse images if it's a JSON string
          if (typeof this.service.images === 'string') {
            try {
              this.service.images = JSON.parse(this.service.images);
              console.log('🖼️ Parsed images from JSON string:', this.service.images);
            } catch (e) {
              console.warn('Failed to parse images:', e);
              this.service.images = [];
            }
          }

          console.log('🔍 Service data loaded:', {
            name: this.service.name,
            images: this.service.images,
            imagesType: typeof this.service.images,
            imagesLength: this.service.images ? this.service.images.length : 0
          });

          // Update page title
          document.title = `${this.service.name} - Phong Nha Valley Glamping`;
        }
      } catch (error) {
        console.error('❌ Failed to load service:', error);

        // Check if it's a 404 error (service not found)
        if (error.message && error.message.includes('404')) {
          this.error = `Dịch vụ với ID "${this.serviceId}" không tồn tại.`;

          // Redirect to homepage after 3 seconds
          setTimeout(() => {
            console.log('🔄 Redirecting to homepage...');
            window.location.href = '/';
          }, 3000);
        } else {
          this.error = 'Không thể tải thông tin dịch vụ. Vui lòng thử lại sau.';
        }
      } finally {
        this.loading = false;
      }
    },

    openLightbox(image, index) {
      this.lightboxImage = image;
    },

    closeLightbox() {
      this.lightboxImage = null;
    },

    handleImageError(event) {
      // Suppress console spam
      const src = event.target.src;
      if (!window.loggedImageErrors) window.loggedImageErrors = new Set();
      if (!window.loggedImageErrors.has(src)) {
        console.warn('Service image failed to load:', src);
        window.loggedImageErrors.add(src);
      }

      // Prevent infinite loop
      if (src.includes('placeholder') ||
          src.includes('data:image') ||
          event.target.dataset.errorHandled === 'true') {
        event.target.style.display = 'none';
        event.target.style.visibility = 'hidden';
        return;
      }

      // Mark as handled and use data URI
      event.target.dataset.errorHandled = 'true';
      event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmMGYwIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIG5vdCBhdmFpbGFibGU8L3RleHQ+PC9zdmc+';
    },

    scrollToBooking() {
      // Pre-select current service in booking form
      if (this.service && this.service.id) {
        this.bookingForm.service = this.service.id;
      }

      // Scroll to booking form in current service page
      const bookingForm = document.getElementById('contact');
      if (bookingForm) {
        const elementPosition = bookingForm.getBoundingClientRect().top;
        const offsetPosition = elementPosition + window.pageYOffset - 80; // 80px offset for header

        window.scrollTo({
          top: offsetPosition,
          behavior: 'smooth'
        });
      }
    },

    scrollToBookingWithService(serviceId) {
      // Pre-select specified service in booking form
      this.bookingForm.service = serviceId;

      // Scroll to booking form in current service page
      const bookingForm = document.getElementById('contact');
      if (bookingForm) {
        const elementPosition = bookingForm.getBoundingClientRect().top;
        const offsetPosition = elementPosition + window.pageYOffset - 80; // 80px offset for header

        window.scrollTo({
          top: offsetPosition,
          behavior: 'smooth'
        });
      }
    },

    // Load other services (excluding current service)
    async loadOtherServices() {
      try {
        console.log('Loading other services...');
        this.otherServicesLoading = true;
        const services = await apiService.getServices();
        console.log('Raw services from API:', services);

        if (services && services.length > 0) {
          // Filter out current service and get only active services
          const filtered = services
            .filter(service => service.id !== this.serviceId && service.is_active);
          console.log('Filtered services (excluding current):', filtered);

          this.otherServices = filtered.slice(0, 6); // Limit to 6 services
          console.log('Final other services:', this.otherServices);
        } else {
          console.log('No services received from API');
        }
      } catch (error) {
        console.error('Error loading other services:', error);
        this.showNotification('Không thể tải dịch vụ khác', 'error');
      } finally {
        this.otherServicesLoading = false;
      }
    },

    async loadContactInfo() {
      try {
        const response = await apiService.get('/settings/');
        const settingsData = response;

        if (settingsData && settingsData.settings) {
          const settings = settingsData.settings;
          this.contactInfo = {
            phone: settings.contact.phone || '123456789',
            email: settings.contact.email || '<EMAIL>',
            address: settings.contact.address || 'Test Address Line 1',
            city: 'Quảng Bình', // Static value since not in API
            hours: settings.business.opening_hours || '6:30 – 21:00'
          };
        }
      } catch (error) {
        console.error('Failed to load contact info:', error);
        // Use default values if API fails
        this.contactInfo = {
          phone: '123456789',
          email: '<EMAIL>',
          address: 'Test Address Line 1',
          city: 'Quảng Bình',
          hours: '6:30 – 21:00'
        };
      }
    },

    // Booking methods
    async handleBookingSubmit() {
      if (!this.bookingForm.service || !this.bookingForm.customer_name || !this.bookingForm.customer_phone || !this.bookingForm.booking_date || !this.bookingForm.adults) {
        this.showNotification('Please fill in all required fields', 'error');
        return;
      }

      this.bookingLoading = true;

      try {
        // Convert date to ISO datetime format for backend
        const bookingDate = new Date(this.bookingForm.booking_date + 'T00:00:00.000Z');

        const bookingData = {
          service_id: this.bookingForm.service,
          customer_name: this.bookingForm.customer_name,
          customer_phone: this.bookingForm.customer_phone,
          booking_date: bookingDate.toISOString(),
          adults: this.bookingForm.adults,
          children_6_to_11: this.bookingForm.children_6_to_11,
          children_under_6: this.bookingForm.children_under_6,
          special_notes: this.bookingForm.special_notes
        };

        // Add check-in/check-out dates for accommodation
        const selectedService = this.otherServices.find(s => s.id === this.bookingForm.service);
        if (selectedService && selectedService.type === 'ACCOMMODATION') {
          bookingData.check_in_date = bookingDate.toISOString();
          // Add one day for check-out
          const checkOutDate = new Date(bookingDate);
          checkOutDate.setDate(checkOutDate.getDate() + 1);
          bookingData.check_out_date = checkOutDate.toISOString();
        }

        console.log('📤 Submitting booking data:', bookingData);
        await apiService.createBooking(bookingData);

        this.showNotification('Booking created successfully! We will contact you soon.', 'success');
        this.resetBookingForm();
      } catch (error) {
        console.error('Booking error:', error);
        this.showNotification(error.message || 'Failed to create booking. Please try again.', 'error');
      } finally {
        this.bookingLoading = false;
      }
    },

    resetBookingForm() {
      this.bookingForm = {
        service: '',
        customer_name: '',
        customer_phone: '',
        booking_date: '',
        adults: 2,
        children_under_6: 0,
        children_6_to_11: 0,
        special_notes: ''
      };
    },

    // Carousel methods
    prevSlide() {
      if (this.currentSlide > 0) {
        this.currentSlide--;
      }
    },

    nextSlide() {
      if (this.currentSlide < this.maxSlide) {
        this.currentSlide++;
      }
    },

    goToSlide(index) {
      this.currentSlide = Math.min(index, this.maxSlide);
    },

    viewServiceDetails(service, event) {
      // Set loading state
      this.navigationLoading = true;

      // Add smooth transition effect before navigation
      if (event && event.target) {
        const button = event.target;
        const originalText = button.textContent;
        button.textContent = 'Loading...';
        button.style.transform = 'scale(0.95)';
        button.style.transition = 'transform 0.1s ease';

        setTimeout(() => {
          button.style.transform = 'scale(1)';
          // Navigate to different service page and scroll to booking form
          window.location.href = `/service.html?id=${service.id}#contact`;
        }, 200);
      } else {
        // Fallback without animation
        setTimeout(() => {
          window.location.href = `/service.html?id=${service.id}#contact`;
        }, 100);
      }
    },

    goToServicePage(service, event) {
      // Prevent navigation if clicking on button
      if (event.target.tagName === 'BUTTON' || event.target.closest('button')) {
        return;
      }

      // Navigate to service detail page (like homepage behavior)
      window.location.href = `/service.html?id=${service.id}`;
    },

    updateCarouselSettings() {
      const width = window.innerWidth;
      if (width < 768) {
        // Mobile: 1 slide
        this.slidesPerView = 1;
        this.slideWidth = 100;
      } else if (width < 1024) {
        // Tablet: 2 slides
        this.slidesPerView = 2;
        this.slideWidth = 50;
      } else {
        // Desktop: 3 slides
        this.slidesPerView = 3;
        this.slideWidth = 33.333;
      }

      // Reset slide if needed
      if (this.currentSlide > this.maxSlide) {
        this.currentSlide = this.maxSlide;
      }
    }
  }
};
</script>

<style scoped>
/* Logo image styling */
.logo-img {
  height: 100%;
  width: auto;
  max-width: 200px;
  object-fit: contain;
}

/* Service Hero Section */
.service-hero {
  height: 60vh;
  min-height: 500px;
  margin-top: 80px;
  background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), #ccc;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  text-align: center;
  position: relative;
}

.hero-title {
  font-family: 'SVN-Megante', serif;
  font-size: 3rem;
  font-weight: normal;
  margin: 0;
}

/* Service Content */
.service-content {
  padding: 4rem 0;
  background: #f8f9fa;
}

/* Other Services Section */
.other-services {
  padding: 4rem 0;
  background: #fff;
}

/* Service Info Card */
.service-info-card {
  background: white;
  border: 2px solid #ddd;
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 3rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-family: 'Poppins', sans-serif;
}

.info-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.info-item strong {
  color: #333;
  font-weight: 600;
  font-family: 'Poppins', sans-serif;
}

.inclusion-list,
.requirement-list {
  list-style: none;
  padding: 0;
  margin: 0.5rem 0 0 0;
  font-family: 'Poppins', sans-serif;
}

.inclusion-list li,
.requirement-list li {
  padding: 0.25rem 0;
  font-size: 0.9rem;
  color: #666;
  font-family: 'Poppins', sans-serif;
}

.inclusion-list li:before {
  content: "✓ ";
  color: #28a745;
  font-weight: bold;
}

.requirement-list li:before {
  content: "• ";
  color: #d4a574;
  font-weight: bold;
}

/* Main Content Section */
.main-content-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  margin-bottom: 3rem;
  align-items: stretch;
  min-height: 450px;
}

.main-image {
  width: 100%;
  height: 100%;
  min-height: 400px;
  border-radius: 15px;
  overflow: hidden;
  background: #f0f0f0;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.main-image:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.main-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.main-image .placeholder-box {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f0f0f0 0%, #e0e0e0 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #888;
  font-weight: 500;
  font-size: 16px;
  border-radius: 15px;
}

.main-description {
  padding: 2rem;
  background: white;
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  justify-content: center;
  font-family: 'Poppins', sans-serif;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  /* DEBUG BORDER */
  border: 2px solid green;
}

.main-description:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.main-description p {
  line-height: 1.8;
  color: #333;
  margin: 0;
  text-align: justify;
  font-family: 'Poppins', sans-serif;
  font-size: 16px;
}

.service-loading,
.service-error {
  text-align: center;
  padding: 4rem 0;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #d4a574;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Service Details */
.service-header {
  text-align: center;
  margin-bottom: 3rem;
}

.service-type-badge {
  display: inline-block;
  background: #d4a574;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  margin-bottom: 1rem;
}

.service-title {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: #333;
}

.service-description {
  font-size: 1.2rem;
  color: #666;
  max-width: 600px;
  margin: 0 auto;
}

/* Service Info Grid */
.service-info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 3rem;
}

.service-main-image .placeholder-box,
.content-image-placeholder .placeholder-box,
.gallery-item .placeholder-box {
  background: #ccc;
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  font-weight: 500;
  border-radius: 8px;
}

.service-main-image img,
.gallery-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.info-card {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  gap: 0.5rem;
}

.info-item i {
  color: #d4a574;
  width: 20px;
}

/* Service Content Section */
.service-content-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 3rem;
}

.content-left .placeholder-box {
  height: 250px;
}

.content-text {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.service-inclusions,
.service-requirements {
  margin-top: 1.5rem;
}

.service-inclusions ul,
.service-requirements ul {
  list-style: none;
  padding: 0;
}

.service-inclusions li,
.service-requirements li {
  padding: 0.5rem 0;
  border-bottom: 1px solid #eee;
}

.service-inclusions li:before {
  content: "✓ ";
  color: #28a745;
  font-weight: bold;
}

.service-requirements li:before {
  content: "• ";
  color: #d4a574;
  font-weight: bold;
}

/* Service Gallery */
.service-gallery {
  margin-bottom: 6rem;
}

.gallery-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 3rem;
}

.gallery-item {
  flex: 0 0 calc(33.333% - 0.67rem);
  height: 200px;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.3s ease;
  background: #f0f0f0;
}

.gallery-item:hover {
  transform: scale(1.02);
}

.gallery-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.gallery-more {
  text-align: center;
}

.btn-view-more {
  background: #d4a574;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-view-more:hover {
  background: #c19660;
  transform: translateY(-2px);
}

.gallery-item .placeholder-box {
  height: 200px;
}

/* Other Services Section */
.other-services-section {
  margin: 4rem 0;
  padding: 3rem 0;
  background: #f8f9fa;
}

.section-title {
  text-align: center;
  color: #2d3748;
  margin-bottom: 3rem;
  font-size: 2.5rem;
  font-weight: 600;
  letter-spacing: 2px;
}

.services-carousel {
  position: relative;
  max-width: 1200px;
  margin: 0 auto;
}

.carousel-nav {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(196, 169, 98, 0.9);
  border: none;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
}

.carousel-nav:hover:not(:disabled) {
  background: #C4A962;
  transform: translateY(-50%) scale(1.1);
}

.carousel-nav:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.carousel-nav.prev {
  left: -25px;
}

.carousel-nav.next {
  right: -25px;
}

.services-container {
  overflow: hidden;
  border-radius: 12px;
}

.services-track {
  display: flex;
  transition: transform 0.5s ease;
}

.service-slide {
  padding: 0 10px;
  box-sizing: border-box;
}

.service-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
  cursor: pointer;
}

.service-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.service-card:active {
  transform: translateY(-2px);
  transition: transform 0.1s ease;
}

.service-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.service-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.service-status-badge {
  position: absolute;
  top: 15px;
  right: 15px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.service-status-badge.available {
  background: #10B981;
  color: white;
}

.service-status-badge.unavailable {
  background: #EF4444;
  color: white;
}

.service-content {
  padding: 1.5rem;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.service-category {
  font-size: 0.75rem;
  color: #C4A962;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 8px;
}

.service-title {
  font-size: 1.3rem;
  color: #2d3748;
  font-weight: 600;
  margin-bottom: 10px;
  line-height: 1.3;
}

.service-description {
  color: #718096;
  font-size: 0.9rem;
  line-height: 1.5;
  margin-bottom: 15px;
  flex: 1;
  /* Limit to 4 lines for service slides */
  display: -webkit-box;
  -webkit-line-clamp: 4;
  line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.service-pricing {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.main-price {
  font-size: 1.2rem;
  color: #C4A962;
  font-weight: 600;
}

.child-price {
  font-size: 0.9rem;
  color: #718096;
}

.service-time {
  font-size: 0.85rem;
  color: #718096;
  margin-bottom: 20px;
  padding: 8px 0;
  border-top: 1px solid #E2E8F0;
  border-bottom: 1px solid #E2E8F0;
}

.btn-book-now {
  background: #d4a574;
  color: white;
  border: none;
  padding: 0.8rem 1.5rem;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  width: 100%;
}

.btn-book-now:hover:not(:disabled) {
  background: #B8A055;
  transform: translateY(-2px);
}

.btn-book-now:disabled {
  background: #ccc;
  cursor: not-allowed;
}

/* Carousel Dots */
.carousel-dots {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 30px;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: none;
  background: #ddd;
  cursor: pointer;
  transition: background 0.3s ease;
}

.dot.active {
  background: #d4a574;
}

.dot:hover {
  background: #bbb;
}

/* Loading and No Services States */
.loading-state,
.no-services-state {
  text-align: center;
  padding: 60px 20px;
  color: #718096;
  font-family: 'SVN-Alluring', serif;
}

/* Contact Section */
.contact {
  font-family: Poppins, sans-serif;
  padding: 100px 0;
  background: var(--dark-blue);
  color: var(--white);
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
}

.contact-info h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 40px;
  color: var(--accent-gold);
}

.contact-details {
  margin-bottom: 40px;
}

.contact-item {
  margin-bottom: 30px;
}

.contact-item h3 {
  color: var(--accent-gold);
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 10px;
}

.contact-item p {
  font-family: 'Poppins', sans-serif;
  color: #ccc;
  line-height: 1.6;
}

.social-links {
  margin-top: 30px;
}

.social-links h3 {
  color: var(--accent-gold);
  font-size: 1.2rem;
  margin-bottom: 15px;
}

.social-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.social-link {
  color: var(--accent-gold);
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.social-link:hover {
  color: var(--white);
}

/* Social media specific colors */
.social-link.instagram {
  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
  color: white;
  padding: 12px 20px;
  border-radius: 25px;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: transform 0.3s ease;
}

.social-link.facebook {
  background: #1877f2;
  color: white;
  padding: 12px 20px;
  border-radius: 25px;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: transform 0.3s ease;
}

.social-link.instagram:hover,
.social-link.facebook:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

/* Booking Form */
.booking-form {
  background: rgba(255, 255, 255, 0.1);
  padding: 40px;
  border-radius: 15px;
  backdrop-filter: blur(10px);
}

.booking-form h3 {
  color: var(--accent-gold);
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 30px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  font-family: 'Poppins', sans-serif;
  display: block;
  color: #C4A962;
  font-weight: 600;
  margin-bottom: 8px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.form-group input,
.form-group select,
.form-group textarea {
  font-family: 'Poppins', sans-serif;
  width: 100%;
  padding: 12px 15px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #C4A962;
  background: rgba(255, 255, 255, 0.15);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

/* Select dropdown styling */
.form-group select {
  background-color: rgba(255, 255, 255, 0.9);
  color: #2d2d2d;
  font-weight: 500;
}

.form-group select:focus {
  background-color: rgba(255, 255, 255, 0.95);
  color: #2d2d2d;
}

.form-group select option {
  background-color: #fff;
  color: #2d2d2d;
  padding: 8px 12px;
  font-weight: 500;
}

.form-group select option:hover,
.form-group select option:focus {
  background-color: #C4A962;
  color: #fff;
}

.form-group select option:checked {
  background-color: #C4A962;
  color: #fff;
}



.btn-primary {
  width: 100%;
  background: #C4A962;
  color: #fff;
  padding: 12px;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary:hover:not(:disabled) {
  background: #B8A055;
}

.btn-primary:disabled {
  background: #ccc;
  cursor: not-allowed;
}

/* Service Booking */
.service-booking {
  text-align: center;
}

.booking-card {
  background: white;
  padding: 3rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  max-width: 500px;
  margin: 0 auto;
}

.btn-book-now {
  background: #d4a574;
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-size: 1.1rem;
  cursor: pointer;
  transition: background 0.3s ease;
}

.btn-book-now:hover {
  background: #c19660;
}

/* Lightbox */
.lightbox-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.lightbox-content {
  position: relative;
  max-width: 90%;
  max-height: 90%;
}

.lightbox-content img {
  width: 100%;
  height: auto;
  border-radius: 8px;
}

.lightbox-close {
  position: absolute;
  top: -40px;
  right: 0;
  background: none;
  border: none;
  color: white;
  font-size: 2rem;
  cursor: pointer;
}

/* Responsive */
@media (max-width: 768px) {
  .main-content-section {
    grid-template-columns: 1fr;
    gap: 2rem;
    min-height: auto;
  }

  .main-image {
    height: 300px;
    min-height: 300px;
  }

  .main-description {
    padding: 1.5rem;
  }

  .main-description p {
    font-size: 15px;
  }

  .info-content {
    grid-template-columns: 1fr;
  }

  .service-info-card {
    padding: 1.5rem;
  }

  .service-hero {
    height: 50vh;
    min-height: 400px;
    margin-top: 70px;
  }

  .hero-title {
    font-size: 2rem;
  }

  /* Gallery Responsive */
  .gallery-item {
    flex: 0 0 calc(50% - 0.5rem);
    height: 150px;
  }

  .service-gallery {
    margin-bottom: 4rem;
  }

  /* Other Services Responsive */
  .section-title {
    font-size: 2rem;
  }

  .service-slide {
    flex: 0 0 100%;
  }

  .carousel-nav {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .carousel-nav.prev {
    left: -20px;
  }

  .carousel-nav.next {
    right: -20px;
  }

  .other-services-section {
    margin: 2rem 0;
    padding: 2rem 0;
  }

  .contact {
    padding: 60px 0;
  }

  .contact-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .contact-info h2 {
    font-size: 2rem;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .booking-form {
    padding: 30px 20px;
  }

  .service-title {
    font-size: 2rem;
  }

  /* Service description responsive for mobile */
  .service-description {
    font-size: 0.85rem;
    line-height: 1.4;
    -webkit-line-clamp: 3; /* Reduce to 3 lines on mobile */
    line-clamp: 3;
  }

  .gallery-item {
    flex: 0 0 100%;
    height: 200px;
  }
}

@media (max-width: 480px) {
  .service-hero {
    height: 45vh;
    min-height: 350px;
    margin-top: 65px;
  }

  .hero-title {
    font-size: 1.8rem;
  }
}
</style>
