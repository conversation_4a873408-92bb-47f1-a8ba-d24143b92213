<template>
  <div id="main-app">
    <!-- Header -->
    <header class="header">
      <div class="container">
        <div class="nav-brand">
          <a href="/" class="logo-link">
            <div class="logo">
              <img
                src="/logo/logo-ngang-08.png"
                alt="Phong Nha Valley"
                class="logo-img"
              />
            </div>
          </a>
        </div>
        <nav class="nav-menu" id="navMenu">
          <ul>
            <li><a href="/" class="active">Trang Chủ</a></li>
            <li><a href="/about.html">Về Phong Nha</a></li>
            <li><a href="/services.html">Dịch Vụ</a></li>
            <li><a href="/gallery.html">Th<PERSON> Viện</a></li>
            <li><a href="/menu.html">Menu</a></li>
            <li><a href="/contact.html"><PERSON><PERSON><PERSON></a></li>
            <li><a href="#" class="btn-call" @click="handleCallClick">G<PERSON><PERSON></a></li>
          </ul>
        </nav>

        <!-- Language Switcher with Google Translate -->
        <div class="language-switcher">
          <button
            class="lang-btn"
            :class="{ active: currentLanguage === 'vi' }"
            @click="switchLanguage('vi')"
            :disabled="translating"
            title="Tiếng Việt"
          >
            <span class="flag-emoji">🇻🇳</span>
            VI
          </button>
          <button
            class="lang-btn"
            :class="{ active: currentLanguage === 'en' }"
            @click="switchLanguage('en')"
            :disabled="translating"
            title="English - Powered by Google Translate"
          >
            <span class="flag-emoji">🇬🇧</span>
            EN
            <span v-if="currentLanguage === 'en'" class="google-icon">🌐</span>
          </button>
          <div v-if="translating" class="translation-loading">
            <span class="loading-spinner"></span>
            <span>Translating...</span>
          </div>
        </div>

        <!-- Translation Progress Indicator - Removed for cleaner UI -->
        <div class="hamburger" id="hamburger" @click="toggleMenu">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>
    </header>

    <!-- Hero Section -->
    <section id="hero" class="hero">
      <!-- Loading state -->
      <div v-if="heroLoading" class="hero-loading">
        <div style="text-align: center; color: white;">
          <div class="loading-spinner"></div>
          <p>Loading...</p>
        </div>
      </div>

      <!-- Hero content -->
      <div v-else class="hero-content" :class="{ 'loaded': !heroLoading }">
        <div class="hero-text">
          <p class="hero-subtitle">{{ translatedHeroSubtitle }}</p>
          <h1 class="hero-title" v-html="translatedHeroTitle"></h1>
          <p class="hero-description">{{ translatedHeroDescription }}</p>
          <button class="btn-primary" @click="scrollToSection('contact')">{{ translatedBookNowText }}</button>
        </div>

        <!-- Hero Navigation -->
        <div v-if="heroImages.length > 1" class="hero-navigation">
          <button
            class="hero-nav-btn hero-nav-prev"
            @click="previousHeroImage"
            :disabled="heroImages.length <= 1"
          >
            <i class="fas fa-chevron-left"></i>
          </button>
          <button
            class="hero-nav-btn hero-nav-next"
            @click="nextHeroImage"
            :disabled="heroImages.length <= 1"
          >
            <i class="fas fa-chevron-right"></i>
          </button>
        </div>

        <!-- Hero Indicators -->
        <div v-if="heroImages.length > 1" class="hero-indicators">
          <button
            v-for="(image, index) in heroImages"
            :key="index"
            class="hero-indicator"
            :class="{ active: currentHeroSlide === index }"
            @click="goToHeroSlide(index)"
          ></button>
        </div>
      </div>

      <!-- Hero Background Carousel -->
      <div class="hero-bg-carousel">
        <div
          v-for="(image, index) in heroImages"
          :key="index"
          class="hero-bg-slide"
          :class="{ active: currentHeroSlide === index }"
          :style="getHeroSlideStyle(image)"
        ></div>
      </div>
    </section>

    <!-- Services Section with Vue.js -->
    <section id="services" class="services">
      <div class="container">
        <div class="section-header">
          <h2>Dịch Vụ Tại Phong Nha</h2>
          <p>Chúng tôi tạo ra một không gian vui chơi đa dạng, phù hợp với mọi lứa tuổi, nơi mỗi khoảnh khắc đều tràn ngập niềm vui và trở thành kỷ niệm đáng nhớ..
</p>
        </div>


        <!-- Services Carousel Container -->
        <div class="services-carousel-container">
          <!-- Navigation Button Left -->
          <button
            class="carousel-nav-btn carousel-nav-left"
            @click="previousServices"
            :disabled="currentServiceSlide === 0"
          >
            <i class="fas fa-chevron-left"></i>
          </button>

          <!-- Services Grid with Carousel -->
          <div class="services-carousel-wrapper">
            <div
              class="services-grid carousel-track"
              id="servicesGrid"
              :style="{ transform: `translateX(-${currentServiceSlide * slideWidth}%)` }"
            >
              <div
                v-for="service in services"
                :key="service.id"
                class="service-slide"
              >
                <a
                  class="service-card fade-in"
                  :href="`/service.html?id=${service.id}`"
                  :data-service-id="service.id"
                  :data-service-name="service.name"
                  :data-service-type="service.type"
                  style="cursor: pointer; text-decoration: none; color: inherit; display: block;"
                  :title="`Click to view ${service.name} details`"
                >
                  <div
                    class="service-image"
                    :style="getServiceImageStyle(service)"
                    :data-service-name="service.name"
                    :data-image-url="service.images && service.images.length > 0 ? service.images[0] : service.image"
                  >
                    <div
                      class="service-status"
                      :class="service.is_active ? 'active' : 'inactive'"
                    >
                      {{ service.is_active ? 'Available' : 'Coming Soon' }}
                    </div>
                  </div>
                  <div class="service-content">
                    <div class="service-type">{{ getTranslatedServiceType(service.type) }}</div>
                    <h3 class="service-name">{{ getTranslatedServiceName(service.name) }}</h3>
                    <p class="service-description">{{ getTranslatedServiceDescription(service.description) }}</p>
                    <div class="service-price">
                      <span class="price-adult">{{ formatPrice(service.price) }}</span>
                      <span v-if="service.child_price" class="price-child">
                        Child: {{ formatPrice(service.child_price) }}
                      </span>
                    </div>
                    <div class="service-hours">
                      <i class="icon-clock"></i> {{ formatTime(service.open_time) }} - {{ formatTime(service.close_time) }}
                    </div>
                    <div class="service-actions">
                      <button
                        class="btn-book"
                        :disabled="!service.is_active"
                        @click.stop.prevent="handleBookNowClick($event, service.id)"
                      >
                        {{ service.is_active ? 'Đặt Ngay' : 'Sớm ra mắt' }}
                      </button>
                    </div>
                  </div>
                </a>
              </div>
            </div>
          </div>

          <!-- Navigation Button Right -->
          <button
            class="carousel-nav-btn carousel-nav-right"
            @click="nextServices"
            :disabled="currentServiceSlide >= maxServiceSlide"
          >
            <i class="fas fa-chevron-right"></i>
          </button>
        </div>

        <!-- Carousel Indicators -->
        <div class="carousel-indicators">
          <button
            v-for="index in (maxServiceSlide + 1)"
            :key="index - 1"
            class="indicator"
            :class="{ active: currentServiceSlide === (index - 1) }"
            @click="goToServiceSlide(index - 1)"
          ></button>
        </div>
      </div>
    </section>

    <!-- About Section -->
    <section id="about" class="about">
        <div class="container">


            <div class="about-layout">
                <!-- Large image section with small image overlay -->
                <div class="about-image-container">
                    <!-- Large background image - always show with static fallback -->
                    <img
                        :src="aboutLargeImageSrc"
                        :alt="aboutData.title || 'About Phong Nha Valley'"
                        class="about-large-img"
                        :key="'large-' + aboutLargeImageSrc"
                    />

                    <!-- Small image overlay on top-left corner - always show with static fallback -->
                    <div class="about-small-overlay">
                        <img
                            :src="aboutSmallImageSrc"
                            :alt="aboutData.title || 'About Phong Nha Valley'"
                            class="about-small-img"
                            :key="'small-' + aboutSmallImageSrc"
                        />
                    </div>
                </div>

                <!-- Text content in white box -->
                <div class="about-text-section">
                    <div class="about-text-box">
                        <h3 class="about-subtitle">{{ aboutSubtitle }}</h3>
                        <h2 class="about-title">{{ aboutTitle }}</h2>
                        <p class="about-description">{{ aboutDescription }}</p>
                        <button class="about-btn" @click="goToAboutPage">
                            Xem thêm
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Gallery Section with Vue.js -->
    <section id="gallery" class="gallery">
      <div class="container">
        <div class="section-header">
          <div class="section-header-content">
            <div class="section-header-text">
              <h2>Thư Viện Ảnh</h2>
            </div>
            <div class="section-header-action">
              <a href="/gallery.html" class="view-more-link">
                <span>Xem thêm</span>
                <i class="fas fa-arrow-right"></i>
              </a>
            </div>
          </div>
        </div>
        <div v-if="galleryLoading" class="loading-state">
          <p>Loading gallery...</p>
        </div>
        <div v-else-if="galleryError" class="error-state">
          <p>{{ galleryError }}</p>
          <button @click="loadGalleryImages" class="btn-retry">Retry</button>
        </div>
        <div v-else-if="galleryImages.length === 0" class="gallery-grid">
          <!-- Placeholder images for testing layout -->
          <div class="gallery-item fade-in" v-for="n in 7" :key="'placeholder-' + n">
            <img
              :src="'https://picsum.photos/400/300?random=' + n"
              :alt="'Gallery Image ' + n"
              @error="handleImageError"
            />
            <div class="gallery-overlay">
              <h4>Gallery Image {{ n }}</h4>
              <p>Beautiful scenery at Phong Nha Valley</p>
            </div>
          </div>
        </div>
        <div v-else class="gallery-grid">
          <!-- Row 1: Large image + Small image -->
          <div class="gallery-row" v-if="galleryImages.length >= 2">
            <div class="gallery-item fade-in" @click="openLightbox(galleryImages[0])">
              <img
                :src="galleryImages[0].image_url"
                :alt="galleryImages[0].alt_text || galleryImages[0].title"
                @error="handleImageError"
              />
              <div class="gallery-overlay">
                <h4>{{ galleryImages[0].title }}</h4>
                <p v-if="galleryImages[0].description">{{ galleryImages[0].description }}</p>
              </div>
            </div>
            <div class="gallery-item fade-in" @click="openLightbox(galleryImages[1])">
              <img
                :src="galleryImages[1].image_url"
                :alt="galleryImages[1].alt_text || galleryImages[1].title"
                @error="handleImageError"
              />
              <div class="gallery-overlay">
                <h4>{{ galleryImages[1].title }}</h4>
                <p v-if="galleryImages[1].description">{{ galleryImages[1].description }}</p>
              </div>
            </div>
          </div>

          <!-- Row 2+3: Large image (spans 2 rows) + 4 small images (2x2 grid) -->
          <div class="gallery-row" v-if="galleryImages.length >= 3">
            <!-- Large image on left (spans full height) -->
            <div class="gallery-item fade-in" @click="openLightbox(galleryImages[2])">
              <img
                :src="galleryImages[2].image_url"
                :alt="galleryImages[2].alt_text || galleryImages[2].title"
                @error="handleImageError"
              />
              <div class="gallery-overlay">
                <h4>{{ galleryImages[2].title }}</h4>
                <p v-if="galleryImages[2].description">{{ galleryImages[2].description }}</p>
              </div>
            </div>

            <!-- Right side: 4 small images in 2x2 grid -->
            <div class="gallery-item-group">
              <!-- Sub-row 1: 2 small images -->
              <div class="gallery-sub-row">
                <div class="gallery-item fade-in" v-if="galleryImages[3]" @click="openLightbox(galleryImages[3])">
                  <img
                    :src="galleryImages[3].image_url"
                    :alt="galleryImages[3].alt_text || galleryImages[3].title"
                    @error="handleImageError"
                  />
                  <div class="gallery-overlay">
                    <h4>{{ galleryImages[3].title }}</h4>
                    <p v-if="galleryImages[3].description">{{ galleryImages[3].description }}</p>
                  </div>
                </div>
                <div class="gallery-item fade-in" v-else>
                  <img src="https://picsum.photos/400/300?random=4" alt="Gallery Image 4" />
                  <div class="gallery-overlay">
                    <h4>Gallery Image 4</h4>
                    <p>Beautiful scenery at Phong Nha Valley</p>
                  </div>
                </div>

                <div class="gallery-item fade-in" v-if="galleryImages[4]" @click="openLightbox(galleryImages[4])">
                  <img
                    :src="galleryImages[4].image_url"
                    :alt="galleryImages[4].alt_text || galleryImages[4].title"
                    @error="handleImageError"
                  />
                  <div class="gallery-overlay">
                    <h4>{{ galleryImages[4].title }}</h4>
                    <p v-if="galleryImages[4].description">{{ galleryImages[4].description }}</p>
                  </div>
                </div>
                <div class="gallery-item fade-in" v-else>
                  <img src="https://picsum.photos/400/300?random=5" alt="Gallery Image 5" />
                  <div class="gallery-overlay">
                    <h4>Gallery Image 5</h4>
                    <p>Beautiful scenery at Phong Nha Valley</p>
                  </div>
                </div>
              </div>

              <!-- Sub-row 2: 2 small images -->
              <div class="gallery-sub-row">
                <div class="gallery-item fade-in" v-if="galleryImages[5]" @click="openLightbox(galleryImages[5])">
                  <img
                    :src="galleryImages[5].image_url"
                    :alt="galleryImages[5].alt_text || galleryImages[5].title"
                    @error="handleImageError"
                  />
                  <div class="gallery-overlay">
                    <h4>{{ galleryImages[5].title }}</h4>
                    <p v-if="galleryImages[5].description">{{ galleryImages[5].description }}</p>
                  </div>
                </div>
                <div class="gallery-item fade-in" v-else>
                  <img src="https://picsum.photos/400/300?random=6" alt="Gallery Image 6" />
                  <div class="gallery-overlay">
                    <h4>Gallery Image 6</h4>
                    <p>Beautiful scenery at Phong Nha Valley</p>
                  </div>
                </div>

                <div class="gallery-item fade-in" v-if="galleryImages.length > 6" @click="openLightbox(galleryImages[6])">
                  <img
                    :src="galleryImages[6].image_url"
                    :alt="galleryImages[6].alt_text || galleryImages[6].title"
                    @error="handleImageError"
                  />
                  <div class="gallery-overlay">
                    <h4>{{ galleryImages[6].title }}</h4>
                    <p v-if="galleryImages[6].description">{{ galleryImages[6].description }}</p>
                  </div>
                </div>
                <div class="gallery-item fade-in" v-else>
                  <img src="https://picsum.photos/400/300?random=7" alt="Gallery Image 7" />
                  <div class="gallery-overlay">
                    <h4>Gallery Image 7</h4>
                    <p>Beautiful scenery at Phong Nha Valley</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Load More Button -->
        <div v-if="galleryPagination && galleryPagination.has_next" class="gallery-load-more">
          <button
            @click="loadMoreGalleryImages"
            class="btn-load-more"
            :disabled="galleryLoadingMore"
          >
            {{ galleryLoadingMore ? 'Loading...' : 'Load More' }}
          </button>
        </div>
      </div>
    </section>

    <!-- Map Section -->
    <section id="map" class="map-section">
        <div class="container">
            <!-- Booking Policy Section -->
            <div class="map-container">
                <div class="map-header" @click="toggleBookingPolicy">
                    <div class="map-header-content">
                        <h3>{{ bookingPolicyContent.title || 'Booking & Cancellation Policy' }}</h3>
                        <p>{{ bookingPolicyContent.subtitle || 'Important information about reservations' }}</p>
                    </div>
                    <div class="map-toggle-icon" :class="{ 'expanded': bookingPolicyExpanded }">
                        <span class="chevron-icon">▼</span>
                    </div>
                </div>
                <div class="map-content" :class="{ 'expanded': bookingPolicyExpanded }">
                    <div class="policy-content" v-html="bookingPolicyContent.content || defaultBookingPolicyContent">
                    </div>
                    <!-- Debug info -->
                    <!-- Debug info - disabled -->
                    <div style="font-size: 10px; color: #999; margin-top: 10px;" v-if="false">
                        Debug: {{ bookingPolicyContent.content ? 'API Content' : 'Default Content' }}
                        ({{ bookingPolicyContent.content ? bookingPolicyContent.content.length : 0 }} chars)
                    </div>
                </div>
            </div>

            <!-- Important Notes Section -->
            <div class="map-container">
                <div class="map-header" @click="toggleNotes">
                    <div class="map-header-content">
                        <h3>{{ notesContent.title || 'Important Notes' }}</h3>
                        <p>{{ notesContent.subtitle || 'Please read before your visit' }}</p>
                    </div>
                    <div class="map-toggle-icon" :class="{ 'expanded': notesExpanded }">
                        <span class="chevron-icon">▼</span>
                    </div>
                </div>
                <div class="map-content" :class="{ 'expanded': notesExpanded }">
                    <div class="notes-content" v-html="notesContent.content || defaultNotesContent">
                    </div>
                    <!-- Debug info -->
                    <!-- Debug info - disabled -->
                    <div style="font-size: 10px; color: #999; margin-top: 10px;" v-if="false">
                        Debug: {{ notesContent.content ? 'API Content' : 'Default Content' }}
                        ({{ notesContent.content ? notesContent.content.length : 0 }} chars)
                    </div>
                </div>
            </div>

            <!-- Map Section -->
            <div class="map-container">
                <div class="map-header" @click="toggleMap">
                    <div class="map-header-content">
                        <h3>Địa điểm và hướng dẫn</h3>
                        <p>Tìm chúng tôi trên bản đồ</p>
                    </div>
                    <div class="map-toggle-icon" :class="{ 'expanded': mapExpanded }">
                        <i class="fas fa-chevron-down"></i>
                    </div>
                </div>
                <div class="map-content" :class="{ 'expanded': mapExpanded }">
                    <div class="map-iframe-container">
                        <iframe
                            src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d4523.0931926577!2d106.25202649152008!3d17.58121803789516!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3138bb6a15bf5217%3A0xb85b1c60997c9889!2sPhong%20Nha%20Valley!5e0!3m2!1svi!2s!4v1751965804130!5m2!1svi!2s"
                            width="100%"
                            height="450"
                            style="border:0;"
                            allowfullscreen=""
                            loading="lazy"
                            referrerpolicy="no-referrer-when-downgrade">
                        </iframe>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact">
      <div class="container">
        <div class="contact-content">
          <div class="contact-info">
            <h2>Liên Hệ Chúng Tôi</h2>
            <div class="contact-details">
                <div class="contact-item">
                    <h3>Điện Thoại</h3>
                    <p>094.121.4444</p>
                </div>
                <div class="contact-item">
                    <h3>Email</h3>
                    <p><EMAIL></p>
                </div>
                <div class="contact-item">
                    <h3>Address</h3>
                    <p>Chày Lập Hamlet, Phúc Trạch Commune<br>Bố Trạch District, Quảng Bình Province</p>
                </div>
                <div class="contact-item">
                    <h3>Giờ Hoạt Động</h3>
                    <p>6:30 – 21:00</p>
                </div>
            </div>
            <div class="social-links">
              <h3>Thêm Thông Tin Về Chúng Tôi </h3>
              <div class="social-buttons">
                <a v-if="settings.social_media.instagram" :href="settings.social_media.instagram" target="_blank" class="social-link instagram">Instagram</a>
                <a v-if="settings.social_media.facebook" :href="settings.social_media.facebook" target="_blank" class="social-link facebook">Facebook</a>
                <a v-if="settings.social_media.youtube" :href="settings.social_media.youtube" target="_blank" class="social-link youtube">YouTube</a>
              </div>
            </div>
          </div>
          <div class="booking-form">
            <h3>{{ currentLanguage === 'en' ? 'Book Service' : 'Đặt dịch vụ' }}</h3>
            <form @submit.prevent="handleBookingSubmit" :class="{ loading: bookingLoading }">
              <!-- Service Selection -->
              <div class="form-group">
                <label for="service">{{ currentLanguage === 'en' ? 'Service' : 'Dịch vụ' }}</label>
                <select v-model="bookingForm.service" id="service" name="service" required @change="updatePriceEstimate">
                  <option value="">{{ currentLanguage === 'en' ? 'Select service' : 'Chọn dịch vụ' }}</option>
                  <option
                    v-for="service in activeServices"
                    :key="service.id"
                    :value="service.id"
                  >
                    {{ service.name }} - {{ formatPrice(service.price) }}
                  </option>
                </select>
              </div>

              <!-- Customer Information -->
              <div class="form-row">
                <div class="form-group">
                  <label for="customer_name">{{ currentLanguage === 'en' ? 'Full Name *' : 'Họ và tên *' }}</label>
                  <input
                    v-model="bookingForm.customer_name"
                    type="text"
                    id="customer_name"
                    name="customer_name"
                    :placeholder="currentLanguage === 'en' ? 'Enter full name' : 'Nhập họ và tên'"
                    required
                  >
                </div>
                <div class="form-group">
                <label for="customer_phone">{{ currentLanguage === 'en' ? 'Phone Number *' : 'Số điện thoại *' }}</label>
                <input
                  v-model="bookingForm.customer_phone"
                  type="tel"
                  id="customer_phone"
                  name="customer_phone"
                  placeholder="0123456789"
                  required
                >
              </div>  

              </div>
              <!-- Booking Date -->
              <div class="form-group">
                <label for="booking_date">{{ currentLanguage === 'en' ? 'Service Date *' : 'Ngày đặt dịch vụ *' }}</label>
                <input
                  v-model="bookingForm.date"
                  type="date"
                  id="booking_date"
                  name="booking_date"
                  :min="todayDate"
                  required
                  @change="updatePriceEstimate"
                >
              </div>

              <!-- Accommodation Dates (shown only for accommodation services) -->
              <div v-if="selectedService && selectedService.type === 'ACCOMMODATION'" class="form-row">
                <div class="form-group">
                  <label for="check_in_date">{{ currentLanguage === 'en' ? 'Check-in Date' : 'Ngày nhận phòng' }}</label>
                  <input
                    v-model="bookingForm.check_in_date"
                    type="date"
                    id="check_in_date"
                    name="check_in_date"
                    :min="todayDate"
                  >
                </div>
                <div class="form-group">
                  <label for="check_out_date">{{ currentLanguage === 'en' ? 'Check-out Date' : 'Ngày trả phòng' }}</label>
                  <input
                    v-model="bookingForm.check_out_date"
                    type="date"
                    id="check_out_date"
                    name="check_out_date"
                    :min="bookingForm.check_in_date"
                  >
                </div>
              </div>

              <!-- Guest Information -->
              <div class="form-row">
                <div class="form-group">
                  <label for="adults">{{ currentLanguage === 'en' ? 'Adults *' : 'Người lớn *' }}</label>
                  <input
                    v-model.number="bookingForm.adults"
                    type="number"
                    id="adults"
                    name="adults"
                    min="1"
                    value="2"
                    required
                    @change="updatePriceEstimate"
                  >
                </div>
                <div class="form-group">
                  <label for="children_6_to_11">{{ currentLanguage === 'en' ? 'Children (6-11 years)' : 'Trẻ em (6-11 tuổi)' }}</label>
                  <input
                    v-model.number="bookingForm.children_6_to_11"
                    type="number"
                    id="children_6_to_11"
                    name="children_6_to_11"
                    min="0"
                    value="0"
                    @change="updatePriceEstimate"
                  >
                </div>
              </div>

              <div class="form-group">
                <label for="children_under_6">{{ currentLanguage === 'en' ? 'Children (Under 6 years)' : 'Trẻ em (Dưới 6 tuổi)' }}</label>
                <input
                  v-model.number="bookingForm.children_under_6"
                  type="number"
                  id="children_under_6"
                  name="children_under_6"
                  min="0"
                  value="0"
                  @change="updatePriceEstimate"
                >
              </div>

              <div class="form-group">
                <label for="special_notes">{{ currentLanguage === 'en' ? 'Special Notes' : 'Lưu ý cho chúng tôi' }}</label>
                <textarea
                  v-model="bookingForm.notes"
                  id="special_notes"
                  name="special_notes"
                  rows="3"
                  :placeholder="currentLanguage === 'en' ? 'Any special requirements or requests...' : 'Yêu cầu đặc biệt hoặc ghi chú...'"
                ></textarea>
              </div>

              <!-- Price calculation removed as requested -->
              <button type="submit" class="btn-primary" :disabled="bookingLoading">
                {{ bookingLoading ? (currentLanguage === 'en' ? 'Processing...' : 'Đang xử lý...') : (currentLanguage === 'en' ? 'Book Now' : 'Đặt ngay') }}
              </button>
            </form>
          </div>
        </div>
      </div>
    </section>

    <!-- Auth Modal -->
    <div v-if="showAuthModal" id="authModal" class="modal" @click="closeAuthModal">
      <div class="modal-content" @click.stop>
        <span class="close" @click="closeAuthModal">&times;</span>
        <div class="auth-tabs">
          <button 
            class="tab-btn" 
            :class="{ active: authMode === 'login' }" 
            @click="authMode = 'login'"
          >
            Login
          </button>
          <button 
            class="tab-btn" 
            :class="{ active: authMode === 'register' }" 
            @click="authMode = 'register'"
          >
            Register
          </button>
        </div>
        
        <!-- Login Form -->
        <div v-if="authMode === 'login'" class="auth-form">
          <h3>Login to Your Account</h3>
          <form @submit.prevent="handleLogin" :class="{ loading: authLoading }">
            <div class="form-group">
              <input v-model="loginForm.email" type="email" placeholder="Email" required>
            </div>
            <div class="form-group">
              <input v-model="loginForm.password" type="password" placeholder="Password" required>
            </div>
            <button type="submit" class="btn-primary" :disabled="authLoading">
              {{ authLoading ? 'Logging in...' : 'Login' }}
            </button>
          </form>
        </div>

        <!-- Register Form -->
        <div v-if="authMode === 'register'" class="auth-form">
          <h3>Create New Account</h3>
          <form @submit.prevent="handleRegister" :class="{ loading: authLoading }">
            <div class="form-row">
              <div class="form-group">
                <input v-model="registerForm.first_name" type="text" placeholder="First Name" required>
              </div>
              <div class="form-group">
                <input v-model="registerForm.last_name" type="text" placeholder="Last Name" required>
              </div>
            </div>
            <div class="form-group">
              <input v-model="registerForm.email" type="email" placeholder="Email" required>
            </div>
            <div class="form-group">
              <input v-model="registerForm.phone" type="tel" placeholder="Phone Number" required>
            </div>
            <div class="form-group">
              <input v-model="registerForm.password" type="password" placeholder="Password" required>
            </div>
            <button type="submit" class="btn-primary" :disabled="authLoading">
              {{ authLoading ? 'Creating Account...' : 'Register' }}
            </button>
          </form>
        </div>
      </div>
    </div>

    <!-- User Menu -->
    <div v-if="showUserMenu" class="user-menu" ref="userMenu">
      <a href="#" @click.prevent="showUserBookings">My Bookings</a>
      <a href="#" @click.prevent="logout">Logout</a>
    </div>

    <!-- Zalo Contact Button -->
    <a
      href="https://zalo.me/**********"
      target="_blank"
      rel="noopener noreferrer"
      class="zalo-contact-btn"
      title="Chat với chúng tôi qua Zalo"
    >
      <img src="/logo/zalo.svg" alt="Zalo" class="zalo-icon" />
    </a>

  </div>
</template>

<script>
import apiService from '../utils/api.js';
import { formatServiceType, formatPrice, getServiceImage, scrollToSection, showNotification, getTodayDate } from '../utils/helpers.js';

export default {
  name: 'MainApp',
  data() {
    return {
      // Language
      currentLanguage: 'vi',
      translating: false,
      contentObserver: null,

      // Hero section
      heroData: {},
      heroLoading: true,
      currentHeroSlide: 0,
      heroAutoSlideInterval: null,

      // Services carousel
      currentServiceSlide: 0,
      slidesPerView: 3, // Show 3 services at once
      slideWidth: 33.333, // 100% / 3 slides

      // About Us data
      aboutData: {
        title: 'Loading...',
        subtitle: 'Loading...',
        description: 'Loading...',
        large_image: '/assets/images/about-large.jpg',
        small_image: '/assets/images/about-small.jpg'
      },

      // Services data
      services: [],

      // Gallery data
      galleryImages: [],
      galleryLoading: false,
      galleryLoadingMore: false,
      galleryError: null,
      galleryPagination: null,

      // Navigation state
      lightboxImage: null,
      currentLightboxIndex: 0,

      galleryCurrentPage: 1,
      galleryItemsPerPage: 12,

      // User authentication
      currentUser: null,
      showAuthModal: false,
      authMode: 'login',
      authLoading: false,
      showUserMenu: false,
      
      // Forms
      loginForm: {
        email: '',
        password: ''
      },
      registerForm: {
        first_name: '',
        last_name: '',
        email: '',
        phone: '',
        password: ''
      },
      bookingForm: {
        customer_name: '',
        customer_phone: '',
        service: '',
        date: '',
        check_in_date: '',
        check_out_date: '',
        adults: 2,
        children_6_to_11: 0,
        children_under_6: 0,
        notes: ''
      },

      // Map section state
      mapExpanded: false,
      bookingPolicyExpanded: false,
      notesExpanded: false,

      // Content for collapsible sections
      bookingPolicyContent: {
        title: 'Booking & Cancellation Policy',
        subtitle: 'Important information about reservations',
        content: 'Loading...'
      },
      notesContent: {
        title: 'Important Notes',
        subtitle: 'Please read before your visit',
        content: 'Loading...'
      },

      // Price calculation removed
      bookingLoading: false,
      
      // UI state
      todayDate: getTodayDate(),

      // Settings data
      settings: {
        general: {
          site_title: ''
        },
        contact: {
          email: '',
          phone: '',
          address: ''
        },
        social_media: {
          instagram: '',
          facebook: '',
          youtube: ''
        },
        business: {
          opening_hours: '',
          google_maps_embed: ''
        }
      },


    };
  },
  
  computed: {
    // Hero Translation Computed Properties
    translatedHeroSubtitle() {
      const originalSubtitle = this.heroData.subtitle || 'PHONG NHA - VALLEY GLAMPING';
      if (this.currentLanguage === 'en') {
        return originalSubtitle; // Keep English as is
      }
      return originalSubtitle; // Vietnamese original
    },

    translatedHeroTitle() {
      const originalTitle = this.heroData.title || 'Glamping, Because<br>Therapy is <span class="highlight">Expensive</span>';
      if (this.currentLanguage === 'en') {
        return originalTitle; // Keep English as is
      }
      return originalTitle; // Vietnamese original
    },

    translatedHeroDescription() {
      const originalDescription = this.heroData.content || 'Experience nature-integrated glamping beside Chày River. Connect with nature and foster positive energy in our tranquil retreat.';
      if (this.currentLanguage === 'en') {
        // English translations
        if (originalDescription.includes('Khơi gợi cảm hứng')) {
          return 'Discover the pristine beauty of nature, where your soul finds peace, your mind finds tranquility and your heart finds the true meaning of life.';
        }
        return originalDescription;
      } else {
        // Vietnamese translations
        if (originalDescription.includes('Experience nature-integrated')) {
          return 'Khơi gợi cảm hứng về vẻ đẹp nguyên sơ của thiên nhiên, nơi tâm hồn bạn tìm thấy sự bình yên, tâm trí tìm thấy sự tĩnh lặng và trái tim tìm thấy ý nghĩa thực sự của cuộc sống.';
        }
        return originalDescription;
      }
    },

    translatedBookNowText() {
      return this.currentLanguage === 'en' ? 'BOOK NOW' : 'ĐẶT NGAY';
    },

    activeServices() {
      return this.services.filter(service => service.is_active);
    },

    // Services carousel computed properties
    maxServiceSlide() {
      // Maximum slide index
      return Math.max(0, this.services.length - this.slidesPerView);
    },

    totalServiceSlides() {
      // Total number of dots for pagination
      return Math.ceil(this.services.length / this.slidesPerView);
    },

    isLoggedIn() {
      return !!this.currentUser;
    },

    // Computed properties for about data to ensure reactivity
    aboutTitle() {
      console.log('🔧 aboutTitle computed called, aboutData.title:', this.aboutData.title);
      return this.aboutData.title || 'THUNG LŨNG XANH';
    },

    aboutSubtitle() {
      console.log('🔧 aboutSubtitle computed called, aboutData.subtitle:', this.aboutData.subtitle);
      return this.aboutData.subtitle || 'Giới thiệu';
    },

    aboutDescription() {
      console.log('🔧 aboutDescription computed called, aboutData.description:', this.aboutData.description);
      return this.aboutData.description || 'Phong Nha - Kẻ Bàng là khu rừng nguyên sinh sông Chày, trở mộng, một điểm đến độc đáo được hình thành từ sự hài hòa và gần gũi với thiên nhiên của du khách có thể tận gác, tại những bờ bể của cuộc sống và hòa mình vào vẻ đẹp của thiên nhiên.';
    },

    aboutLargeImage() {
      console.log('🔧 aboutLargeImage computed called, aboutData.large_image:', this.aboutData.large_image);
      return this.aboutData.large_image || '/assets/images/about-large.jpg';
    },

    aboutSmallImage() {
      console.log('🔧 aboutSmallImage computed called, aboutData.small_image:', this.aboutData.small_image);
      return this.aboutData.small_image || '/assets/images/about-small.jpg';
    },

    // Static image sources that never change to prevent race conditions
    aboutLargeImageSrc() {
      return this.aboutData?.large_image || '';
    },

    aboutSmallImageSrc() {
      return this.aboutData?.small_image || '';
    },

    aboutDataLoaded() {
      return !!(this.aboutData?.title && this.aboutData?.title !== 'Loading...');
    },

    // Hero carousel computed properties
    heroImages() {
      // Return images array from hero data, fallback to single image
      if (this.heroData.images && this.heroData.images.length > 0) {
        return this.heroData.images;
      } else if (this.heroData.image_url) {
        return [this.heroData.image_url];
      } else {
        return [];
      }
    },

    selectedService() {
      return this.services.find(s => s.id === this.bookingForm.service);
    },

    defaultBookingPolicyContent() {
      return `
        <h4>Booking Policy</h4>
        <ul>
          <li>Advance booking required</li>
          <li>50% deposit required to confirm booking</li>
          <li>Full payment due on arrival</li>
          <li>Cancellation 24 hours before arrival for full refund</li>
        </ul>
      `;
    },

    defaultNotesContent() {
      return `
        <h4>Important Notes</h4>
        <ul>
          <li>Please arrive 15 minutes before your scheduled time</li>
          <li>Bring comfortable clothing and shoes</li>
          <li>Weather-dependent activities may be rescheduled</li>
          <li>Children must be supervised at all times</li>
        </ul>
      `;
    }
  },
  
  async mounted() {
    console.log('🚀 MainApp mounted, initializing...');
    console.log('🚀 Initial aboutData:', this.aboutData);

    await this.initializeApp();

    // Setup carousel responsive settings
    this.updateCarouselSettings();
    window.addEventListener('resize', this.updateCarouselSettings);

    console.log('🚀 MainApp initialization complete');
    console.log('🚀 Final aboutData:', this.aboutData);
    console.log('🚀 Services loaded:', this.services.length);
  },

  beforeUnmount() {
    // Clean up hero auto-slide interval and event listeners
    this.stopHeroAutoSlide();
    window.removeEventListener('resize', this.updateCarouselSettings);
  },

  watch: {
    aboutData: {
      handler(newVal, oldVal) {
        console.log('🔧 aboutData watcher triggered');
        console.log('🔧 Old aboutData:', oldVal);
        console.log('🔧 New aboutData:', newVal);
      },
      deep: true
    }
  },

  methods: {
    // Utility methods
    formatServiceType,
    formatPrice,
    scrollToSection,
    showNotification,



    // Services carousel methods - now moves 1 card at a time
    nextServices() {
      if (this.currentServiceSlide < this.maxServiceSlide) {
        this.currentServiceSlide++;
      }
    },

    previousServices() {
      if (this.currentServiceSlide > 0) {
        this.currentServiceSlide--;
      }
    },

    goToServiceSlide(index) {
      this.currentServiceSlide = index;
    },

    // Navigation methods
    goToAboutPage() {
      window.location.href = '/about.html';
    },

    // Format time from ISO string to readable format
    formatTime(timeString) {
      if (!timeString) return '';

      try {
        // Extract time part from ISO string (e.g., "0000-01-01T16:00:00Z" -> "16:00")
        const timeMatch = timeString.match(/T(\d{2}):(\d{2})/);
        if (timeMatch) {
          const hours = timeMatch[1];
          const minutes = timeMatch[2];
          return `${hours}:${minutes}`;
        }

        // Fallback: if it's already in HH:MM format
        if (timeString.match(/^\d{2}:\d{2}$/)) {
          return timeString;
        }

        return timeString;
      } catch (error) {
        console.error('Error formatting time:', error);
        return timeString;
      }
    },


    
    async initializeApp() {
      console.log('🚀 initializeApp() started');

      // Check if user is logged in
      if (apiService.getToken()) {
        try {
          this.currentUser = await apiService.getCurrentUser();
        } catch (error) {
          console.error('Failed to get current user:', error);
          apiService.logout();
        }
      }

      // Load hero content, about us, services and gallery
      console.log('🚀 Loading hero content...');
      await this.loadHeroContent();

      console.log('🚀 Loading about data...');
      try {
        await this.loadAboutData();
        console.log('🚀 About data loaded successfully:', this.aboutData);
      } catch (error) {
        console.error('🚨 Failed to load about data:', error);
      }

      console.log('🚀 Loading services...');
      try {
        await this.loadServices();
      } catch (error) {
        console.error('❌ Failed to load services:', error);
      }

      console.log('🚀 Loading gallery...');
      try {
        await this.loadGalleryImages();
      } catch (error) {
        console.error('❌ Failed to load gallery:', error);
      }

      console.log('🚀 Loading booking policy content...');
      try {
        await this.loadBookingPolicyContent();
      } catch (error) {
        console.error('❌ Failed to load booking policy:', error);
      }

      console.log('🚀 Loading important notes content...');
      try {
        await this.loadNotesContent();
      } catch (error) {
        console.error('❌ Failed to load important notes:', error);
      }

      console.log('🚀 Loading settings...');
      try {
        await this.loadSettings();
      } catch (error) {
        console.error('❌ Failed to load settings:', error);
      }

      // Update auth UI
      this.updateAuthUI();
    },

    async loadHeroContent() {
      this.heroLoading = true;
      try {
        console.log('Loading hero content...');
        const response = await apiService.get('/content/homepage/hero');
        this.heroData = response;
        console.log('Hero data received:', this.heroData);

        // Auto-translate if currently in English mode
        if (this.currentLanguage === 'en') {
          setTimeout(() => {
            this.translateDynamicContentWithGoogle();
          }, 500);
        }

        // Start auto-slide if multiple images
        this.startHeroAutoSlide();
      } catch (error) {
        console.error('Failed to load hero content:', error);
        // Use empty values if API fails
        this.heroData = {
          title: '',
          subtitle: '',
          content: '',
          image_url: null,
          images: []
        };

        // Start auto-slide even with default images
        this.startHeroAutoSlide();
      } finally {
        this.heroLoading = false;
      }
    },

    async loadAboutData() {
      try {
        const response = await apiService.get('/content/homepage/about');

        // Parse metadata to get image URLs
        let metadata = {};
        if (response.metadata) {
          try {
            metadata = JSON.parse(response.metadata);
          } catch (e) {
            console.error('Error parsing metadata:', e);
          }
        }

        // Update aboutData - match About page logic exactly
        this.aboutData = {
          title: response.title || 'About Phong Nha Valley',
          subtitle: response.subtitle || 'XIN CHÀO!',
          description: response.content || 'Phong Nha Valley is an experimental tourism project offering nature camping and glamping experiences at Trằm Mé – Chày Lập.',
          large_image: response.image_url || metadata.large_image || '/assets/images/about-large.jpg',
          small_image: metadata.small_image || '/assets/images/about-small.jpg'
        };

        console.log('About data loaded on Homepage:', this.aboutData);
      } catch (error) {
        console.error('Error loading about content:', error);
        console.error('Error details:', error.message);

        // Use empty data if API fails
        this.aboutData = {
          title: '',
          subtitle: '',
          description: '',
          large_image: '',
          small_image: ''
        };

        console.log('Using fallback about data:', this.aboutData);
      }
    },

    async loadServices() {
      try {
        this.services = await apiService.getServices();
        console.log('🔍 Loaded services:', this.services.map(s => ({ id: s.id, name: s.name, type: s.type })));

        // Auto-translate if currently in English mode
        if (this.currentLanguage === 'en') {
          setTimeout(() => {
            this.translateDynamicContentWithGoogle();
          }, 500);
        }

      } catch (error) {
        console.error('Failed to load services:', error);
        this.showNotification('Failed to load services', 'error');
      }
    },

    async loadBookingPolicyContent() {
      try {
        console.log('🚀 Loading booking policy content from API...');
        console.log('🚀 API URL will be:', apiService.baseURL + '/content/homepage/booking-policy');

        const response = await apiService.call('/content/homepage/booking-policy');
        console.log('✅ Booking policy response received:', response);
        console.log('✅ Response content length:', response.content ? response.content.length : 'No content');

        if (response && response.content) {
          // Convert plain text to HTML by replacing newlines with <br> tags
          const formattedContent = response.content.replace(/\n/g, '<br>');

          this.bookingPolicyContent = {
            title: response.title || 'Booking & Cancellation Policy',
            subtitle: response.subtitle || 'Important information about reservations',
            content: formattedContent
          };
          console.log('✅ Booking policy content loaded from API:', this.bookingPolicyContent.title);
          console.log('✅ Formatted content preview:', formattedContent.substring(0, 100) + '...');
        } else {
          throw new Error('No content in API response');
        }
      } catch (error) {
        console.error('❌ Error loading booking policy content:', error);
        console.error('❌ Error details:', error.message);
        console.log('⚠️ Using default booking policy content');

        // Use default content if API fails
        this.bookingPolicyContent = {
          title: 'Booking & Cancellation Policy',
          subtitle: 'Important information about reservations',
          content: this.defaultBookingPolicyContent
        };
      }
    },

    async loadNotesContent() {
      try {
        console.log('🚀 Loading important notes content from API...');
        console.log('🚀 API URL will be:', apiService.baseURL + '/content/homepage/notes');

        const response = await apiService.get('/content/homepage/notes');
        console.log('✅ Important notes response received:', response);
        console.log('✅ Response content length:', response.content ? response.content.length : 'No content');

        if (response && response.content) {
          this.notesContent = {
            title: response.title || 'Important Notes',
            subtitle: response.subtitle || 'Please read before your visit',
            content: response.content
          };
          console.log('✅ Important notes content loaded from API:', this.notesContent.title);
          console.log('✅ Content preview:', response.content.substring(0, 100) + '...');
        } else {
          throw new Error('No content in API response');
        }
      } catch (error) {
        console.error('❌ Error loading important notes content:', error);
        console.error('❌ Error details:', error.message);
        console.log('⚠️ Using default important notes content');

        // Use default content if API fails
        this.notesContent = {
          title: 'Important Notes',
          subtitle: 'Please read before your visit',
          content: this.defaultNotesContent
        };
      }
    },

    async loadGalleryImages() {
      this.galleryLoading = true;
      this.galleryError = null;

      try {
        console.log('🖼️ Loading gallery images for homepage...');
        const response = await apiService.getGalleryImages(this.galleryCurrentPage, this.galleryItemsPerPage);
        console.log('📡 Gallery API Response:', response);

        // Extract gallery array and pagination from response
        this.galleryImages = response.gallery || [];
        this.galleryPagination = response.pagination || null;

        console.log(`✅ Loaded ${this.galleryImages.length} gallery images`);
      } catch (error) {
        console.error('❌ Failed to load gallery images:', error);
        this.galleryError = 'Failed to load gallery images';
        this.galleryImages = [];
      } finally {
        this.galleryLoading = false;
      }
    },

    async loadMoreGalleryImages() {
      if (!this.galleryPagination || !this.galleryPagination.has_next) {
        return;
      }

      this.galleryLoadingMore = true;

      try {
        const nextPage = this.galleryCurrentPage + 1;
        console.log(`🔄 Loading more gallery images (page ${nextPage})...`);

        const response = await apiService.getGalleryImages(nextPage, this.galleryItemsPerPage);

        // Append new images to existing array
        this.galleryImages = [...this.galleryImages, ...(response.gallery || [])];
        this.galleryPagination = response.pagination || null;
        this.galleryCurrentPage = nextPage;

        console.log(`✅ Loaded ${response.gallery?.length || 0} more images`);
      } catch (error) {
        console.error('❌ Failed to load more gallery images:', error);
        this.showNotification('Failed to load more images', 'error');
      } finally {
        this.galleryLoadingMore = false;
      }
    },

    openLightbox(image) {
      // Find the index of the clicked image
      this.currentLightboxIndex = this.galleryImages.findIndex(img => img.id === image.id);
      this.lightboxImage = image;

      // Create lightbox element
      const lightbox = document.createElement('div');
      lightbox.className = 'lightbox-overlay';
      lightbox.id = 'gallery-lightbox';

      // Create lightbox content with navigation buttons
      lightbox.innerHTML = `
        <div class="lightbox-content">
          <button class="lightbox-nav lightbox-prev">&lt;</button>
          <div class="lightbox-image-container">
            <img src="${image.image_url}" alt="${image.alt_text || image.title}" id="lightbox-image">
            <div class="lightbox-info">
              <h3 id="lightbox-title">${image.title}</h3>
              <p id="lightbox-description">${image.description || ''}</p>
            </div>
          </div>
          <button class="lightbox-nav lightbox-next">&gt;</button>
          <button class="lightbox-close">&times;</button>
        </div>
      `;

      // Add event listeners
      lightbox.addEventListener('click', (e) => {
        if (e.target === lightbox || e.target.classList.contains('lightbox-close')) {
          document.body.removeChild(lightbox);
          document.body.style.overflow = 'auto';
          this.lightboxImage = null;
        }
      });

      // Add to DOM
      document.body.appendChild(lightbox);
      document.body.style.overflow = 'hidden';

      // Add navigation event listeners
      const prevBtn = lightbox.querySelector('.lightbox-prev');
      const nextBtn = lightbox.querySelector('.lightbox-next');

      prevBtn.addEventListener('click', () => this.navigateLightbox('prev'));
      nextBtn.addEventListener('click', () => this.navigateLightbox('next'));

      // Add keyboard navigation
      document.addEventListener('keydown', this.handleLightboxKeydown);
    },

    navigateLightbox(direction) {
      const totalImages = this.galleryImages.length;
      if (totalImages <= 1) return;

      // Calculate new index
      let newIndex;
      if (direction === 'next') {
        newIndex = (this.currentLightboxIndex + 1) % totalImages;
      } else {
        newIndex = (this.currentLightboxIndex - 1 + totalImages) % totalImages;
      }

      // Update current image
      this.currentLightboxIndex = newIndex;
      this.lightboxImage = this.galleryImages[newIndex];

      // Update DOM elements
      const lightbox = document.getElementById('gallery-lightbox');
      if (lightbox) {
        const img = lightbox.querySelector('#lightbox-image');
        const title = lightbox.querySelector('#lightbox-title');
        const description = lightbox.querySelector('#lightbox-description');

        img.src = this.lightboxImage.image_url;
        img.alt = this.lightboxImage.alt_text || this.lightboxImage.title;
        title.textContent = this.lightboxImage.title;
        description.textContent = this.lightboxImage.description || '';
      }
    },

    handleLightboxKeydown(e) {
      if (!this.lightboxImage) return;

      if (e.key === 'ArrowLeft') {
        this.navigateLightbox('prev');
      } else if (e.key === 'ArrowRight') {
        this.navigateLightbox('next');
      } else if (e.key === 'Escape') {
        const lightbox = document.getElementById('gallery-lightbox');
        if (lightbox) {
          document.body.removeChild(lightbox);
          document.body.style.overflow = 'auto';
          this.lightboxImage = null;
          document.removeEventListener('keydown', this.handleLightboxKeydown);
        }
      }
    },

    handleImageError(event) {
      // Suppress console spam
      const src = event.target.src;
      if (!window.loggedImageErrors) window.loggedImageErrors = new Set();
      if (!window.loggedImageErrors.has(src)) {
        console.warn('Image failed to load:', src);
        window.loggedImageErrors.add(src);
      }

      // Prevent infinite loop
      if (src.includes('placeholder') || event.target.dataset.errorHandled === 'true') {
        event.target.style.display = 'none';
        event.target.style.visibility = 'hidden';
        return;
      }

      // Mark as handled and use data URI fallback
      event.target.dataset.errorHandled = 'true';
      event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmMGYwIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIG5vdCBhdmFpbGFibGU8L3RleHQ+PC9zdmc+';
    },

    handleImageLoad() {
      // Image loaded successfully
    },


    
    getServiceImageStyle(service) {
      // Use first image from images array, fallback to service.image for backward compatibility
      let imageUrl = null;

      if (service.images && service.images.length > 0) {
        imageUrl = service.images[0];
      } else if (service.image) {
        imageUrl = service.image;
      }

      console.log('=== getServiceImageStyle DEBUG ===');
      console.log('Service:', service.name);
      console.log('Service.images:', service.images);
      console.log('Service.image:', service.image);
      console.log('Selected imageUrl:', imageUrl);

      if (imageUrl) {
        console.log('Using service image:', imageUrl, 'for service:', service.name);

        // Check if it's a full URL or path
        if (imageUrl.startsWith('http') || imageUrl.startsWith('/assets/')) {
          const style = {
            backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.3)), url('${imageUrl}')`
          };
          console.log('Full URL/path style for', service.name, ':', style);
          return style;
        } else {
          const style = {
            backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.3)), url('/assets/uploads/${imageUrl}')`
          };
          console.log('Filename style for', service.name, ':', style);
          return style;
        }
      } else {
        // Fallback to type-based images if no service image
        const fallbackImage = getServiceImage(service.type);
        return {
          backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.3)), url('/assets/images/${fallbackImage}')`
        };
      }
    },
    
    handleBookNowClick(event, serviceId) {
      console.log('🎯 handleBookNowClick called with event:', event, 'serviceId:', serviceId);

      // Manually stop event propagation and prevent default
      event.stopPropagation();
      event.preventDefault();

      console.log('🛑 Event stopped and prevented');

      // Call the booking function
      this.selectServiceForBooking(serviceId);

      return false; // Extra safety
    },

    selectServiceForBooking(serviceId) {
      console.log('🎯 Book Now clicked for service ID:', serviceId);
      this.bookingForm.service = serviceId;
      console.log('📝 Booking form updated:', this.bookingForm);
      console.log('📍 Scrolling to contact section...');
      this.scrollToSection('contact');
    },

    handleServiceCardClick(service) {
      console.log('🎯 Service card clicked!');
      console.log('🎯 Service object:', service);
      console.log('🎯 Service ID:', service.id);
      console.log('🎯 Service name:', service.name);

      // Call viewServiceDetails with the service ID
      this.viewServiceDetails(service.id);
    },



    handleViewDetailsClick(event, serviceId) {
      console.log('🎯 View details button clicked!');
      console.log('🎯 Service ID:', serviceId);

      // Prevent default link behavior and handle navigation manually
      event.preventDefault();
      this.viewServiceDetails(serviceId);
    },

    viewServiceDetails(serviceId) {
      console.log('🎯 viewServiceDetails called with serviceId:', serviceId);
      console.log('🎯 Service ID type:', typeof serviceId);
      console.log('🎯 Current location:', window.location.href);

      try {
        const targetUrl = `/service.html?id=${serviceId}`;
        console.log('🎯 Navigating to:', targetUrl);

        // Use window.location.href for navigation
        window.location.href = targetUrl;

        console.log('✅ Navigation command executed');
      } catch (error) {
        console.error('❌ Navigation error:', error);
      }
    },
    
    // Authentication methods
    openAuthModal() {
      this.showAuthModal = true;
      document.body.style.overflow = 'hidden';
    },
    
    closeAuthModal() {
      this.showAuthModal = false;
      document.body.style.overflow = 'auto';
      this.resetAuthForms();
    },
    
    resetAuthForms() {
      this.loginForm = { email: '', password: '' };
      this.registerForm = { first_name: '', last_name: '', email: '', phone: '', password: '' };
      this.authLoading = false;
    },
    
    async handleLogin() {
      this.authLoading = true;
      try {
        const response = await apiService.login(this.loginForm.email, this.loginForm.password);
        this.currentUser = response.user;
        this.closeAuthModal();
        this.updateAuthUI();
        this.showNotification('Login successful!', 'success');
      } catch (error) {
        this.showNotification(error.message, 'error');
      } finally {
        this.authLoading = false;
      }
    },
    
    async handleRegister() {
      this.authLoading = true;
      try {
        const response = await apiService.register(this.registerForm);
        this.currentUser = response.user;
        this.closeAuthModal();
        this.updateAuthUI();
        this.showNotification('Registration successful!', 'success');
      } catch (error) {
        this.showNotification(error.message, 'error');
      } finally {
        this.authLoading = false;
      }
    },
    
    logout() {
      apiService.logout();
      this.currentUser = null;
      this.showUserMenu = false;
      this.updateAuthUI();
      this.showNotification('Logged out successfully', 'success');
    },
    
    updateAuthUI() {
      const loginBtn = document.querySelector('.btn-login');
      if (!loginBtn) return;

      if (this.currentUser) {
        loginBtn.textContent = `Hi, ${this.currentUser.first_name}`;
        loginBtn.onclick = () => this.toggleUserMenu();
      } else {
        loginBtn.textContent = 'Login';
        loginBtn.onclick = () => this.openAuthModal();
      }
    },
    
    toggleUserMenu() {
      this.showUserMenu = !this.showUserMenu;
      
      if (this.showUserMenu) {
        this.$nextTick(() => {
          const loginBtn = document.querySelector('.btn-login');
          if (loginBtn && this.$refs.userMenu) {
            loginBtn.parentElement.style.position = 'relative';
            loginBtn.parentElement.appendChild(this.$refs.userMenu);
          }
        });
      }
    },
    
    showUserBookings() {
      this.showUserMenu = false;
      this.showNotification('Bookings feature coming soon!', 'info');
    },
    
    // Booking methods
    async handleBookingSubmit() {
      // Validate required fields
      if (!this.bookingForm.customer_name || !this.bookingForm.customer_phone ||
          !this.bookingForm.service || !this.bookingForm.date || !this.bookingForm.adults) {
        this.showNotification('Please fill in all required fields', 'error');
        return;
      }

      this.bookingLoading = true;
      
      try {
        // Skip availability check for now - endpoint not implemented
        // TODO: Implement availability check endpoint
        // const availability = await apiService.checkAvailability(this.bookingForm.service, this.bookingForm.date);
        // if (availability && !availability.available) {
        //   this.showNotification('Selected service is not available for this date', 'error');
        //   return;
        // }

        // Convert date to ISO datetime format for backend
        const bookingDate = new Date(this.bookingForm.date + 'T00:00:00.000Z');

        const bookingData = {
          customer_name: this.bookingForm.customer_name,
          customer_phone: this.bookingForm.customer_phone,
          service_id: this.bookingForm.service,
          booking_date: bookingDate.toISOString(),
          adults: this.bookingForm.adults,
          children_6_to_11: this.bookingForm.children_6_to_11,
          children_under_6: this.bookingForm.children_under_6,
          special_notes: this.bookingForm.notes
        };

        // Add check-in/check-out dates for accommodation
        const selectedService = this.services.find(s => s.id === this.bookingForm.service);
        if (selectedService && selectedService.type === 'ACCOMMODATION') {
          // Convert check-in/check-out dates to ISO format
          const checkInDate = new Date(this.bookingForm.check_in_date + 'T00:00:00.000Z');
          const checkOutDate = new Date(this.bookingForm.check_out_date + 'T00:00:00.000Z');

          bookingData.check_in_date = checkInDate.toISOString();
          bookingData.check_out_date = checkOutDate.toISOString();
        }

        await apiService.createBooking(bookingData);
        this.showNotification('Booking created successfully!', 'success');
        this.resetBookingForm();
      } catch (error) {
        this.showNotification(error.message, 'error');
      } finally {
        this.bookingLoading = false;
      }
    },
    
    resetBookingForm() {
      this.bookingForm = {
        customer_name: '',
        customer_phone: '',
        service: '',
        date: '',
        check_in_date: '',
        check_out_date: '',
        adults: 2,
        children_6_to_11: 0,
        children_under_6: 0,
        notes: ''
      };
      // Price calculation removed
    },

    // Map section methods
    toggleMap() {
      this.mapExpanded = !this.mapExpanded;
    },

    toggleBookingPolicy() {
      this.bookingPolicyExpanded = !this.bookingPolicyExpanded;
    },

    toggleNotes() {
      this.notesExpanded = !this.notesExpanded;
    },

    // Price calculation methods removed

    // Hero section methods
    getHeroBackgroundStyle() {
      if (this.heroData.image_url) {
        return {
          background: `linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('${this.heroData.image_url}') center/cover no-repeat`
        };
      }
      return {};
    },

    getHeroSlideStyle(imageUrl) {
      return {
        background: `linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('${imageUrl}') center/cover no-repeat`
      };
    },

    nextHeroImage() {
      if (this.heroImages.length <= 1) return;
      this.currentHeroSlide = (this.currentHeroSlide + 1) % this.heroImages.length;
      this.resetHeroAutoSlide();
    },

    previousHeroImage() {
      if (this.heroImages.length <= 1) return;
      this.currentHeroSlide = this.currentHeroSlide === 0
        ? this.heroImages.length - 1
        : this.currentHeroSlide - 1;
      this.resetHeroAutoSlide();
    },

    goToHeroSlide(index) {
      if (index >= 0 && index < this.heroImages.length) {
        this.currentHeroSlide = index;
        this.resetHeroAutoSlide();
      }
    },

    startHeroAutoSlide() {
      // Only start auto-slide if there are multiple images
      if (this.heroImages.length <= 1) return;

      // Clear existing interval
      if (this.heroAutoSlideInterval) {
        clearInterval(this.heroAutoSlideInterval);
      }

      // Start new interval (5 seconds)
      this.heroAutoSlideInterval = setInterval(() => {
        this.nextHeroImage();
      }, 5000);
    },

    resetHeroAutoSlide() {
      // Restart auto-slide timer when user manually navigates
      this.startHeroAutoSlide();
    },

    stopHeroAutoSlide() {
      if (this.heroAutoSlideInterval) {
        clearInterval(this.heroAutoSlideInterval);
        this.heroAutoSlideInterval = null;
      }
    },

    updateCarouselSettings() {
      const width = window.innerWidth;
      if (width < 768) {
        // Mobile: 1 slide
        this.slidesPerView = 1;
        this.slideWidth = 100;
      } else if (width < 1024) {
        // Tablet: 2 slides
        this.slidesPerView = 2;
        this.slideWidth = 50;
      } else {
        // Desktop: 3 slides
        this.slidesPerView = 3;
        this.slideWidth = 33.333;
      }

      // Reset slide if current position is invalid
      if (this.currentServiceSlide > this.maxServiceSlide) {
        this.currentServiceSlide = this.maxServiceSlide;
      }
    },

    // Navigation methods
    handleCallClick() {
      // Check if mobile device
      if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
        // Mobile: trigger phone call
        window.location.href = 'tel:+84941214444';
      } else {
        // Desktop: scroll to contact section
        scrollToSection('contact');
      }
    },

    toggleMenu() {
      const navMenu = document.getElementById('navMenu');
      const hamburger = document.getElementById('hamburger');

      if (navMenu && hamburger) {
        navMenu.classList.toggle('active');
        hamburger.classList.toggle('active');
      }
    },

    // Language Translation Methods
    async switchLanguage(lang) {
      this.currentLanguage = lang;

      if (lang === 'vi') {
        this.restoreVietnamese();
      } else if (lang === 'en') {
        this.translating = true;

        try {
          // Step 1: Apply manual translations for static content immediately
          this.translateStaticContent();

          // Step 2: Wait for Vue to update DOM, then translate dynamic content
          await this.$nextTick();

          // Step 3: Multiple attempts to catch all dynamic content
          await this.translateAllDynamicContent();

          // Step 4: Set up watcher for future dynamic content
          this.setupDynamicContentWatcher();

        } finally {
          this.translating = false;
        }
      }
    },

    async translateAllDynamicContent() {
      // Multiple translation passes to catch content that loads at different times
      const translationPasses = [
        { delay: 0, name: 'Immediate' },
        { delay: 500, name: 'After 500ms' },
        { delay: 1000, name: 'After 1s' },
        { delay: 2000, name: 'After 2s' }
      ];

      for (const pass of translationPasses) {
        await new Promise(resolve => setTimeout(resolve, pass.delay));

        const dynamicContent = this.collectDynamicContent();

        if (dynamicContent.length > 0) {
          try {
            const translations = await this.callGoogleTranslateAPI(dynamicContent);
            this.applyGoogleTranslations(translations);
          } catch (error) {
            console.warn(`Translation failed in ${pass.name} pass:`, error);
          }
        }
      }
    },

    // Translate static/fixed content using manual dictionary
    translateStaticContent() {
      console.log('🌐 Translating static content...');

      const staticTranslations = {
        // Navigation
        'Trang Chủ': 'Home',
        'Về Phong Nha': 'About Us',
        'Dịch Vụ': 'Services',
        'Thư viện': 'Gallery',
        'Liên hệ': 'Contact',
        'Gọi ngay': 'Call',
        'Menu': 'Menu',

        // Fixed UI elements
        'Dịch vụ của chúng tôi': 'Our Services',
        'Đặt dịch vụ': 'Book Service',
        'Thông tin liên hệ': 'Contact Information',
        'Lưu ý quan trọng': 'Important Notes',
        'Thư viện ảnh': 'Photo Gallery',

        // Booking form labels
        'Họ và tên': 'Full Name',
        'Số điện thoại': 'Phone Number',
        'Ngày đặt dịch vụ': 'Service Date',
        'Người lớn': 'Adults',
        'Trẻ em (6-11 tuổi)': 'Children (6-11 years)',
        'Trẻ em (Dưới 6 tuổi)': 'Children (Under 6 years)',
        'Lưu ý cho chúng tôi': 'Special Notes',
        'Đặt ngay': 'Book Now',
        'Đang xử lý...': 'Processing...'
      };

      this.applyTranslations(staticTranslations);
    },

    // Translate dynamic content from API using Google Translate
    async translateDynamicContentWithGoogle() {
      console.log('🌐 Translating dynamic content with Google Translate...');
      this.translating = true;

      try {
        // Collect dynamic content (from API)
        const dynamicContent = this.collectDynamicContent();
        console.log('📝 Collected dynamic content:', dynamicContent);

        if (dynamicContent.length === 0) {
          console.log('⚠️ No dynamic content found to translate');
          return;
        }

        // Translate using Google Translate API
        const translations = await this.callGoogleTranslateAPI(dynamicContent);
        console.log('✅ Received translations:', translations);

        // Apply translations to the page
        this.applyGoogleTranslations(translations);

      } catch (error) {
        console.error('❌ Google Translate failed:', error);
      } finally {
        this.translating = false;
      }
    },

    collectDynamicContent() {
      const dynamicElements = [];

      // Comprehensive selectors for dynamic content from API
      const dynamicSelectors = [
        // Hero content (from API)
        '.hero-subtitle',
        '.hero-title',
        '.hero-description',

        // Service content (from API) - more specific selectors
        '.service-card .service-name',
        '.service-card .service-description',
        '.service-content .service-name',
        '.service-content .service-description',

        // About content (from API)
        '.about-content p',
        '.about-text',
        '.about-description',

        // Contact content (from API)
        '.contact-info .info-value',
        '.contact-content',

        // Important notes content (from API)
        '.policy-content',
        '.notes-content',
        '.map-content',

        // Gallery content
        '.gallery-item .caption',
        '.gallery-description',

        // Any element with Vietnamese text that's not already translated
        'p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'span', 'div'
      ];

      // First pass: specific selectors
      dynamicSelectors.slice(0, -1).forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
          const text = element.textContent?.trim();
          if (text && this.isVietnamese(text) && !element.classList.contains('google-translated')) {
            dynamicElements.push({
              element: element,
              originalText: text,
              selector: selector
            });
          }
        });
      });

      // Second pass: general elements (but only if they contain substantial Vietnamese text)
      const generalElements = document.querySelectorAll('p, h1, h2, h3, h4, h5, h6, span, div');
      generalElements.forEach(element => {
        const text = element.textContent?.trim();
        if (text &&
            text.length > 10 && // Only substantial text
            this.isVietnamese(text) &&
            !element.classList.contains('google-translated') &&
            !element.closest('.language-switcher') && // Exclude language switcher
            !element.closest('.nav-menu') && // Exclude navigation (handled by static)
            !dynamicElements.some(item => item.element === element) // Avoid duplicates
        ) {
          dynamicElements.push({
            element: element,
            originalText: text,
            selector: 'general'
          });
        }
      });

      return dynamicElements;
    },

    setupDynamicContentWatcher() {

      // Watch for DOM changes and auto-translate new content
      if (this.contentObserver) {
        this.contentObserver.disconnect();
      }

      this.contentObserver = new MutationObserver((mutations) => {
        if (this.currentLanguage === 'en') {
          let hasNewContent = false;

          mutations.forEach((mutation) => {
            if (mutation.type === 'childList') {
              mutation.addedNodes.forEach((node) => {
                if (node.nodeType === Node.ELEMENT_NODE) {
                  // Check if new content contains Vietnamese text
                  const vietnameseElements = node.querySelectorAll ?
                    Array.from(node.querySelectorAll('*')).filter(el =>
                      el.textContent && this.isVietnamese(el.textContent)
                    ) : [];

                  if (vietnameseElements.length > 0) {
                    hasNewContent = true;
                  }
                }
              });
            }
          });

          if (hasNewContent) {
            setTimeout(() => {
              this.translateDynamicContentWithGoogle();
            }, 500); // Small delay to ensure content is fully rendered
          }
        }
      });

      // Start observing
      this.contentObserver.observe(document.body, {
        childList: true,
        subtree: true
      });
    },

    // Google Translate Integration (legacy method - keeping for fallback)
    async translateToEnglishWithGoogle() {
      console.log('🌐 Starting Google Translate...');
      this.translating = true;

      try {
        // Collect all Vietnamese text content
        const textToTranslate = this.collectVietnameseContent();
        console.log('📝 Collected text for translation:', textToTranslate);

        if (textToTranslate.length === 0) {
          console.log('⚠️ No Vietnamese content found to translate');
          return;
        }

        // Translate using Google Translate API
        const translations = await this.callGoogleTranslateAPI(textToTranslate);
        console.log('✅ Received translations:', translations);

        // Apply translations to the page
        this.applyGoogleTranslations(translations);

      } catch (error) {
        console.error('❌ Google Translate failed:', error);
        // Fallback to manual translation
        console.log('🔄 Falling back to manual translation...');
        this.translateToEnglish();
      } finally {
        this.translating = false;
      }
    },

    collectVietnameseContent() {
      const textElements = [];

      // Collect text from specific selectors
      const selectors = [
        // Navigation
        '.nav-menu a',
        '.btn-call',

        // Hero section
        '.hero-subtitle',
        '.hero-title',
        '.hero-description',
        '.btn-primary',

        // Services
        '.service-type',
        '.service-name',
        '.service-description',
        '.service-price .price-label',

        // About section
        '.about-title',
        '.about-content',
        '.about-text',

        // Booking form
        '.booking-form h3',
        '.booking-form label',
        '.booking-form option',
        '.booking-form button',
        'input[placeholder]',
        'textarea[placeholder]',

        // Contact section
        '.contact-title',
        '.contact-info label',

        // Important notes
        '.map-header h3',
        '.map-header p',

        // Gallery
        '.gallery h2',
        '.gallery p'
      ];

      selectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
          const text = element.textContent?.trim();
          if (text && this.isVietnamese(text)) {
            textElements.push({
              element: element,
              originalText: text,
              selector: selector
            });
          }

          // Also check placeholders
          if (element.placeholder && this.isVietnamese(element.placeholder)) {
            textElements.push({
              element: element,
              originalText: element.placeholder,
              selector: selector,
              isPlaceholder: true
            });
          }
        });
      });

      return textElements;
    },

    isVietnamese(text) {
      // Enhanced Vietnamese detection
      const vietnameseChars = /[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ]/i;
      const vietnameseWords = /\b(và|của|tại|với|cho|từ|đến|trong|về|như|hoặc|nhưng|nếu|khi|nơi|tất cả|nhiều|ít|lớn|nhỏ|mới|cũ|tốt|xấu|đẹp|nhanh|chậm|cao|thấp|dịch vụ|liên hệ|thông tin|đặt|ngay|chúng tôi|phong nha|valley|glamping|khám phá|trải nghiệm|tham quan|du lịch|thiên nhiên|hang động|sông|núi|rừng|biển|hồ|cây|hoa|động vật|chim|cá|khách sạn|nhà hàng|quán ăn|cà phê|trà|nước|bia|rượu|người lớn|trẻ em|tuổi|ngày|tháng|năm|giờ|phút|giây|sáng|trưa|tối|hôm nay|ngày mai|hôm qua)\b/i;

      // Skip if text is too short or contains mostly numbers/symbols
      if (text.length < 3) return false;
      if (/^[\d\s\-\+\(\)\.,:;!?]*$/.test(text)) return false;

      // Skip common English words that might appear in Vietnamese context
      const englishWords = /\b(the|and|or|but|if|when|where|all|many|few|big|small|new|old|good|bad|beautiful|fast|slow|high|low|today|tomorrow|yesterday|hour|minute|day|week|month|year|home|about|services|gallery|contact|call|book|now|phone|email|address|website|facebook|instagram|youtube|twitter|linkedin|google|translate|loading|error|success|warning|info|debug|test|demo|sample|example|lorem|ipsum|dolor|sit|amet|consectetur|adipiscing|elit|sed|do|eiusmod|tempor|incididunt|ut|labore|et|dolore|magna|aliqua)\b/i;

      if (englishWords.test(text)) return false;

      return vietnameseChars.test(text) || vietnameseWords.test(text);
    },

    async callGoogleTranslateAPI(textElements) {
      // Extract unique texts to translate
      const uniqueTexts = [...new Set(textElements.map(item => item.originalText))];

      console.log('🔄 Translating texts:', uniqueTexts);

      // Use Google Translate API (you'll need to set up API key)
      const translations = {};

      try {
        // Method 1: Use Google Translate API (requires API key)
        if (window.google && window.google.translate) {
          console.log('🌐 Using Google Translate Widget API');
          // This would use the existing Google Translate widget
          return await this.useGoogleTranslateWidget(textElements);
        }

        // Method 2: Use a translation service API
        for (const text of uniqueTexts) {
          try {
            const translatedText = await this.translateSingleText(text);
            translations[text] = translatedText;
          } catch (error) {
            console.warn(`Failed to translate: "${text}"`, error);
            translations[text] = text; // Keep original if translation fails
          }
        }

      } catch (error) {
        console.error('Translation API error:', error);
        throw error;
      }

      return translations;
    },

    async translateSingleText(text) {
      // For demo purposes, we'll use a simple translation service
      // In production, you'd use Google Translate API with your API key

      try {
        // Example using a free translation API (replace with your preferred service)
        const response = await fetch(`https://api.mymemory.translated.net/get?q=${encodeURIComponent(text)}&langpair=vi|en`);
        const data = await response.json();

        if (data.responseStatus === 200) {
          return data.responseData.translatedText;
        } else {
          throw new Error('Translation service error');
        }
      } catch (error) {
        console.warn('Free translation service failed, using fallback');
        // Fallback to manual dictionary
        return this.getManualTranslation(text);
      }
    },

    getManualTranslation(text) {
      // Fallback manual translations for common phrases
      const manualDict = {
        'Trang Chủ': 'Home',
        'Về Phong Nha': 'About Us',
        'Dịch Vụ': 'Services',
        'Thư viện': 'Gallery',
        'Liên hệ': 'Contact',
        'Gọi ngay': 'Call',
        'Đặt ngay': 'Book Now',
        'Đặt dịch vụ': 'Book Service',
        'Họ và tên': 'Full Name',
        'Số điện thoại': 'Phone Number',
        'Dịch vụ của chúng tôi': 'Our Services'
      };

      return manualDict[text] || text;
    },

    applyGoogleTranslations(translations) {
      let translatedCount = 0;

      // Apply translations to collected elements
      Object.keys(translations).forEach(originalText => {
        const translatedText = translations[originalText];

        if (translatedText && translatedText !== originalText) {
          // Find all elements with this exact text
          const walker = document.createTreeWalker(
            document.body,
            NodeFilter.SHOW_TEXT,
            null,
            false
          );

          const textNodes = [];
          let node;

          while (node = walker.nextNode()) {
            if (node.textContent.trim() === originalText.trim()) {
              textNodes.push(node);
            }
          }

          textNodes.forEach(textNode => {
            const element = textNode.parentElement;

            // Skip if already translated or in excluded areas
            if (!element.classList.contains('google-translated') &&
                !element.closest('.language-switcher') &&
                !element.closest('.nav-menu')) {

              textNode.textContent = translatedText;

              // Add subtle visual indicators
              element.classList.add('google-translated');
              // Remove debug border - keep only class for hover effect
              element.style.transition = 'all 0.3s ease';

              translatedCount++;
            }
          });
        }
      });
    },

    // Service Translation Methods
    getTranslatedServiceType(type) {
      const serviceTypeTranslations = {
        'TOUR': this.currentLanguage === 'en' ? 'Tour' : 'Tour',
        'ACCOMMODATION': this.currentLanguage === 'en' ? 'Accommodation' : 'Lưu trú',
        'ACTIVITY': this.currentLanguage === 'en' ? 'Activity' : 'Hoạt động',
        'TRANSPORT': this.currentLanguage === 'en' ? 'Transport' : 'Vận chuyển',
        'FOOD': this.currentLanguage === 'en' ? 'Food & Beverage' : 'Ăn uống',
        'ADVENTURE': this.currentLanguage === 'en' ? 'Adventure' : 'Phiêu lưu',
        'RELAXATION': this.currentLanguage === 'en' ? 'Relaxation' : 'Thư giãn',
        'CULTURAL': this.currentLanguage === 'en' ? 'Cultural' : 'Văn hóa',
        'NATURE': this.currentLanguage === 'en' ? 'Nature' : 'Thiên nhiên'
      };
      return serviceTypeTranslations[type] || this.formatServiceType(type);
    },

    getTranslatedServiceName(name) {
      if (this.currentLanguage === 'en') {
        // Vietnamese to English service name translations
        const nameTranslations = {
          'Tour hang động Phong Nha': 'Phong Nha Cave Tour',
          'Kayak sông Chày': 'Chay River Kayaking',
          'Trekking rừng quốc gia': 'National Park Trekking',
          'Xe đạp thám hiểm': 'Cycling Adventure',
          'Cắm trại qua đêm': 'Overnight Camping',
          'Tour thuyền sông': 'River Boat Tour',
          'Thăm làng địa phương': 'Local Village Visit',
          'Chèo thúng chai': 'Basket Boat Ride',
          'Tham quan hang Thiên Đường': 'Paradise Cave Tour',
          'Go Kart': 'Go Kart Racing'
        };
        return nameTranslations[name] || name;
      }
      return name;
    },

    getTranslatedServiceDescription(description) {
      if (this.currentLanguage === 'en') {
        // Vietnamese to English description translations
        if (description.includes('khám phá')) {
          return description.replace(/khám phá/g, 'explore');
        }
        if (description.includes('trải nghiệm')) {
          return description.replace(/trải nghiệm/g, 'experience');
        }
        if (description.includes('tham quan')) {
          return description.replace(/tham quan/g, 'visit');
        }
        // Add more specific translations as needed
        return description;
      }
      return description;
    },

    translateToEnglish() {
      console.log('🌐 Translating to English...');

      const translations = {
        // Navigation
        'Trang Chủ': 'Home',
        'Về Phong Nha': 'About Us',
        'Dịch Vụ': 'Services',
        'Thư viện': 'Gallery',
        'Liên hệ': 'Contact',
        'Gọi ngay': 'Call',
        'Menu': 'Menu',

        // Hero Section
        'PHONG NHA - VALLEY GLAMPING': 'PHONG NHA - VALLEY GLAMPING',
        'MỘT THIÊN NHIÊN XANH VÀ TÂM HỒN': 'GREEN NATURE AND SOUL',
        'Khơi gợi cảm hứng về vẻ đẹp nguyên sơ của thiên nhiên, nơi tâm hồn bạn tìm thấy sự bình yên, tâm trí tìm thấy sự tĩnh lặng và trái tim tìm thấy ý nghĩa thực sự của cuộc sống.': 'Discover the pristine beauty of nature, where your soul finds peace, your mind finds tranquility and your heart finds the true meaning of life.',
        'Experience nature-integrated glamping beside Chày River. Connect with nature and foster positive energy in our tranquil retreat.': 'Khơi gợi cảm hứng về vẻ đẹp nguyên sơ của thiên nhiên, nơi tâm hồn bạn tìm thấy sự bình yên, tâm trí tìm thấy sự tĩnh lặng và trái tim tìm thấy ý nghĩa thực sự của cuộc sống.',
        'ĐẶT NGAY': 'BOOK NOW',

        // About Section
        'Về chúng tôi': 'About Us',
        'THUNG LŨNG XANH': 'GREEN VALLEY',
        'Xem thêm': 'View More',

        // Services Section
        'Dịch vụ của chúng tôi': 'Our Services',
        'Đặt ngay': 'Book Now',
        'Lưu trú': 'Accommodation',
        'Hoạt động': 'Activity',
        'Vận chuyển': 'Transport',
        'Ăn uống': 'Food & Beverage',
        'Phiêu lưu': 'Adventure',
        'Thư giãn': 'Relaxation',
        'Văn hóa': 'Cultural',
        'Thiên nhiên': 'Nature',

        // Booking Form
        'Đặt dịch vụ': 'Book Service',
        'Dịch vụ': 'Service',
        'Chọn dịch vụ': 'Select service',
        'Họ và tên': 'Full Name',
        'Số điện thoại': 'Phone Number',
        'Ngày đặt dịch vụ': 'Service Date',
        'Ngày nhận phòng': 'Check-in Date',
        'Ngày trả phòng': 'Check-out Date',
        'Người lớn': 'Adults',
        'Trẻ em (6-11 tuổi)': 'Children (6-11 years)',
        'Trẻ em (Dưới 6 tuổi)': 'Children (Under 6 years)',
        'Lưu ý cho chúng tôi': 'Special Notes',
        'Đang xử lý...': 'Processing...',

        // Important Notes
        'Lưu ý quan trọng': 'Important Notes',
        'Quy khách cần lưu ý những thông tin dưới đây': 'Guests need to note the following information',

        // Contact Section
        'Thông tin liên hệ': 'Contact Information',
        'Địa chỉ': 'Address',
        'Điện thoại': 'Phone',
        'Email': 'Email',

        // Gallery
        'Thư viện ảnh': 'Photo Gallery',

        // Common words
        'và': 'and',
        'của': 'of',
        'tại': 'at',
        'với': 'with',
        'cho': 'for',
        'từ': 'from',
        'đến': 'to',
        'trong': 'in',
        'về': 'about'
      };

      this.applyTranslations(translations);
    },

    restoreVietnamese() {
      // Stop content observer
      if (this.contentObserver) {
        this.contentObserver.disconnect();
        this.contentObserver = null;
      }

      // Remove Google Translate indicators
      const translatedElements = document.querySelectorAll('.google-translated');
      translatedElements.forEach(element => {
        element.classList.remove('google-translated');
        element.style.borderBottom = '';
      });

      // Force page reload to restore original Vietnamese content
      // This ensures all dynamic content is restored properly
      window.location.reload();
    },

    restoreVietnameseManual() {
      console.log('🌐 Restoring Vietnamese manually...');

      const reverseTranslations = {
        // Navigation
        'Home': 'Trang Chủ',
        'About Us': 'Về Phong Nha',
        'Services': 'Dịch Vụ',
        'Gallery': 'Thư viện',
        'Contact': 'Liên hệ',
        'Call': 'Gọi ngay',
        'Menu': 'Menu',

        // Hero Section
        'PHONG NHA - VALLEY GLAMPING': 'PHONG NHA - VALLEY GLAMPING',
        'GREEN NATURE AND SOUL': 'MỘT THIÊN NHIÊN XANH VÀ TÂM HỒN',
        'Discover the pristine beauty of nature, where your soul finds peace, your mind finds tranquility and your heart finds the true meaning of life.': 'Khơi gợi cảm hứng về vẻ đẹp nguyên sơ của thiên nhiên, nơi tâm hồn bạn tìm thấy sự bình yên, tâm trí tìm thấy sự tĩnh lặng và trái tim tìm thấy ý nghĩa thực sự của cuộc sống.',
        'Experience nature-integrated glamping beside Chày River. Connect with nature and foster positive energy in our tranquil retreat.': 'Khơi gợi cảm hứng về vẻ đẹp nguyên sơ của thiên nhiên, nơi tâm hồn bạn tìm thấy sự bình yên, tâm trí tìm thấy sự tĩnh lặng và trái tim tìm thấy ý nghĩa thực sự của cuộc sống.',
        'BOOK NOW': 'ĐẶT NGAY',

        // About Section
        'About Us': 'Về chúng tôi',
        'GREEN VALLEY': 'THUNG LŨNG XANH',
        'View More': 'Xem thêm',

        // Services Section
        'Our Services': 'Dịch vụ của chúng tôi',
        'Book Now': 'Đặt ngay',
        'Accommodation': 'Lưu trú',
        'Activity': 'Hoạt động',
        'Transport': 'Vận chuyển',
        'Food & Beverage': 'Ăn uống',
        'Adventure': 'Phiêu lưu',
        'Relaxation': 'Thư giãn',
        'Cultural': 'Văn hóa',
        'Nature': 'Thiên nhiên',

        // Booking Form
        'Book Service': 'Đặt dịch vụ',
        'Service': 'Dịch vụ',
        'Select service': 'Chọn dịch vụ',
        'Full Name': 'Họ và tên',
        'Phone Number': 'Số điện thoại',
        'Service Date': 'Ngày đặt dịch vụ',
        'Check-in Date': 'Ngày nhận phòng',
        'Check-out Date': 'Ngày trả phòng',
        'Adults': 'Người lớn',
        'Children (6-11 years)': 'Trẻ em (6-11 tuổi)',
        'Children (Under 6 years)': 'Trẻ em (Dưới 6 tuổi)',
        'Special Notes': 'Lưu ý cho chúng tôi',
        'Processing...': 'Đang xử lý...',

        // Important Notes
        'Important Notes': 'Lưu ý quan trọng',
        'Guests need to note the following information': 'Quy khách cần lưu ý những thông tin dưới đây',

        // Contact Section
        'Contact Information': 'Thông tin liên hệ',
        'Address': 'Địa chỉ',
        'Phone': 'Điện thoại',
        'Email': 'Email',

        // Gallery
        'Photo Gallery': 'Thư viện ảnh',

        // Common words
        'and': 'và',
        'of': 'của',
        'at': 'tại',
        'with': 'với',
        'for': 'cho',
        'from': 'từ',
        'to': 'đến',
        'in': 'trong',
        'about': 'về'
      };

      this.applyTranslations(reverseTranslations);
    },

    applyTranslations(translations) {
      Object.keys(translations).forEach(originalText => {
        const translatedText = translations[originalText];

        // Find all text nodes and replace
        const walker = document.createTreeWalker(
          document.body,
          NodeFilter.SHOW_TEXT,
          null,
          false
        );

        const textNodes = [];
        let node;

        while (node = walker.nextNode()) {
          if (node.textContent.trim() === originalText.trim()) {
            textNodes.push(node);
          }
        }

        textNodes.forEach(textNode => {
          textNode.textContent = translatedText;
        });
      });
    },

    // Settings management
    async loadSettings() {
      try {
        const response = await fetch('/api/v1/settings/');

        if (response.ok) {
          const data = await response.json();
          this.settings = data.settings;
          console.log('✅ Settings loaded:', this.settings);
        } else {
          console.error('❌ Failed to load settings:', response.status);
        }
      } catch (error) {
        console.error('❌ Error loading settings:', error);
      }
    }

  }
};
</script>

<style scoped>
/* Import Poppins Font */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap');

/* Global Poppins Font Application */
* {
  font-family: 'Poppins', sans-serif;
}

/* Body margin for fixed header */
#main-app {
  padding-top: 40px; /* Ultra reduction to eliminate all gaps */
}

/* Header Styles */
.header {
  position: fixed;
  top: -15;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  z-index: 1000;
  padding: 0; /* No padding */
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  font-family: 'Poppins', sans-serif;
}

.header .container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1400px; /* Increased max-width */
  margin: 0 auto;
  padding: 0 20px 0 40px; /* More padding on left, less on right */
}

.nav-brand {
  flex: 0 0 auto;
  margin-right: 50px; /* Increased space between logo and nav */
  margin-left: -80px; /* Move logo much more to the left */
}

.logo {
  height: 135px; /* Increased to 1.5x from 90px */
  transition: transform 0.3s ease;
  display: flex;
  align-items: center;
}

.logo:hover {
  transform: scale(1.05);
}

.logo-img {
  height: 100%;
  width: auto;
  max-width: 200px;
  object-fit: contain;
}

/* Logo-svg styling moved to global CSS */

.nav-menu {
  flex: 1;
  display: flex;
  justify-content: center;
  font-family: 'Poppins', sans-serif;
}

.nav-menu ul {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 40px; /* Increased gap */
  align-items: center;
}

.nav-menu li a {
  text-decoration: none;
  color: #333;
  font-weight: 600;
  font-size: 14px; /* Reduced back to standard size */
  font-family: 'Poppins', sans-serif !important;
  transition: all 0.3s ease;
  padding: 8px 12px;
  border-radius: 8px;
  position: relative;
}

.nav-menu li a:hover {
  color: #3F9772;
  background: rgba(63, 151, 114, 0.1);
  transform: translateY(-1px);
}

.nav-menu li a.active {
  color: #3F9772;
  background: rgba(63, 151, 114, 0.15);
  font-weight: 700;
}

.btn-call {
  background: linear-gradient(135deg, #3F9772, #2d7a5a);
  color: white !important;
  padding: 12px 24px; /* Increased padding */
  border-radius: 25px;
  font-weight: 600;
  font-size: 14px; /* Reduced to match nav items */
  font-family: 'Poppins', sans-serif !important;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(63, 151, 114, 0.3);
}

.btn-call:hover {
  background: linear-gradient(135deg, #2d7a5a, #1e5a3f);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(63, 151, 114, 0.4);
}

/* Google Translate Styles - Subtle indicators */
.google-translated {
  position: relative;
  transition: all 0.3s ease;
}

/* Subtle translation indicator - only show on hover */
.google-translated:hover::after {
  content: '🌐';
  position: absolute;
  top: -8px;
  right: -8px;
  font-size: 8px;
  background: #4285f4;
  color: white;
  border-radius: 50%;
  width: 12px;
  height: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  opacity: 0.8;
}

/* Language Switcher Styles */
.language-switcher {
  display: flex;
  gap: 8px;
  margin-left: 30px;
  font-family: 'Poppins', sans-serif;
  position: relative;
}

.lang-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 10px 16px; /* Increased padding */
  background: transparent;
  border: 2px solid rgba(63, 151, 114, 0.3);
  color: #333;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px; /* Increased font size */
  font-weight: 600;
  font-family: 'Poppins', sans-serif;
}

.lang-btn:hover {
  background: rgba(63, 151, 114, 0.1);
  border-color: rgba(63, 151, 114, 0.6);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(63, 151, 114, 0.2);
}

.lang-btn.active {
  background: rgba(63, 151, 114, 0.15);
  border-color: #3F9772;
  color: #3F9772;
  font-weight: 700;
  box-shadow: 0 4px 15px rgba(63, 151, 114, 0.3);
}

.flag-emoji {
  font-size: 16px; /* Increased flag size */
}

.google-icon {
  font-size: 10px;
  margin-left: 3px;
}

.translation-loading {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(63, 151, 114, 0.3);
  border-radius: 20px;
  color: #3F9772;
  font-size: 12px;
  font-weight: 600;
  font-family: 'Poppins', sans-serif;
  box-shadow: 0 4px 15px rgba(63, 151, 114, 0.2);
  z-index: 1000;
  white-space: nowrap;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(63, 151, 114, 0.3);
  border-top: 2px solid #3F9772;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.lang-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Translation Progress Indicator - Removed for cleaner UI */

/* Hamburger Menu */
.hamburger {
  display: none;
  flex-direction: column;
  cursor: pointer;
  gap: 6px;
  order: -2; /* Move to the left of language switcher */
  margin-right: 20px;
}

.hamburger span {
  width: 37px; /* Increased from 25px (1.5x) */
  height: 4px; /* Increased from 3px */
  background: #333;
  transition: all 0.3s ease;
  border-radius: 2px;
}

/* Mobile responsive */
@media (max-width: 768px) {
  #main-app {
    padding-top: 30px; /* Ultra mobile reduction */
  }

  .header {
    padding: 12px 0;
  }

  .header .container {
    padding: 0 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .logo {
    height: 65px; /* Larger on mobile but still appropriate */
  }

  .logo-img {
    height: 100%;
    width: auto;
    max-width: 150px;
    object-fit: contain;
  }

  .nav-brand {
    margin-right: 15px;
    margin-left: -10px; /* Still move logo left but less on mobile */
  }

  .nav-menu {
    position: fixed;
    top: 80px;
    left: -100%;
    width: 100%;
    height: calc(100vh - 80px);
    background: white;
    flex-direction: column;
    justify-content: flex-start;
    padding-top: 50px;
    transition: left 0.3s ease;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  }

  .nav-menu.active {
    left: 0;
  }

  .nav-menu ul {
    flex-direction: column;
    gap: 25px;
    text-align: center;
  }

  .nav-menu li a {
    font-size: 16px; /* Reduced from 18px */
    padding: 15px 20px;
    display: block;
    font-weight: 600;
    font-family: 'Poppins', sans-serif !important;
  }

  .btn-call {
    padding: 15px 30px;
    font-size: 16px; /* Reduced from 18px */
    font-family: 'Poppins', sans-serif !important;
    margin-top: 10px;
  }

  .hamburger {
    display: flex;
    order: -2; /* Move to leftmost position */
  }

  /* Mobile header layout */
  .header .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .nav-brand {
    order: 0; /* Logo in center */
    flex: 1;
    display: flex;
    justify-content: center;
    margin: 0;
  }

  .language-switcher {
    order: 1; /* Language switcher on right */
    margin-left: 0;
  }

  .hamburger.active span:nth-child(1) {
    transform: rotate(45deg) translate(7px, 7px);
  }

  .hamburger.active span:nth-child(2) {
    opacity: 0;
  }

  .hamburger.active span:nth-child(3) {
    transform: rotate(-45deg) translate(9px, -8px);
  }

  .language-switcher {
    margin-left: 15px;
    order: -1;
    gap: 6px;
  }

  .lang-btn {
    padding: 8px 12px;
    font-size: 12px;
  }

  .flag-emoji {
    font-size: 14px;
  }
}

/* Services Carousel Styles */
.services-carousel-container {
  position: relative;
  max-width: 1200px;
  margin: 0 auto;
}

.services-carousel-wrapper {
  overflow: hidden;
  border-radius: 12px;
}

.services-grid {
  display: flex;
  transition: transform 0.5s ease;
  width: 100%;
}

.service-slide {
  flex: 0 0 33.333%;
  padding: 0 15px;
}

.service-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: auto;
  display: flex;
  flex-direction: column;
}

.service-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.service-image {
  height: 200px;
  background-size: cover;
  background-position: center;
  position: relative;
  border-radius: 12px 12px 0 0;
}

.service-status {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  min-width: auto;
  white-space: nowrap;
}

.service-status.active {
  background: #28a745;
  color: white;
}

.service-status.inactive {
  background: #6c757d;
  color: white;
}

.service-content {
  padding: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.service-type {
  font-size: 0.8rem;
  color: #3F9772;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 8px;
  font-weight: 600;
}

.service-name {
  font-size: 1.3rem;
  font-weight: 700;
  color: #333;
  margin: 0 0 10px 0;
  line-height: 1.3;
}

.service-description {
  font-size: 0.9rem;
  color: #666;
  line-height: 1.6;
  margin: 0 0 15px 0;
  flex: 1;
}

.service-price {
  margin: 10px 0;
}

.price-adult {
  font-size: 1.2rem;
  font-weight: 700;
  color: #3F9772;
}

.price-child {
  font-size: 0.9rem;
  color: #888;
  margin-left: 10px;
}

.service-hours {
  font-size: 0.85rem;
  color: #888;
  margin: 10px 0 20px 0;
  display: flex;
  align-items: center;
  gap: 5px;
}

.service-actions {
  margin-top: auto;
}

.btn-book {
  background: #3F9772;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 0.9rem;
}

.btn-book:hover:not(:disabled) {
  background: #B8A055;
  transform: translateY(-2px);
}

.btn-book:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.carousel-nav-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(196, 169, 98, 0.9);
  border: none;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
}

.carousel-nav-btn i {
  font-size: 1.2rem;
  font-family: "Font Awesome 6 Free" !important;
  font-weight: 900 !important;
}

.carousel-nav-btn:hover:not(:disabled) {
  background: #C4A962;
  transform: translateY(-50%) scale(1.1);
}

.carousel-nav-btn:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.carousel-nav-left {
  left: -25px;
}

.carousel-nav-right {
  right: -25px;
}

.carousel-indicators {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 30px;
}

.indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: none;
  background: #ddd;
  cursor: pointer;
  transition: all 0.3s ease;
}

.indicator.active {
  background: #C4A962;
  transform: scale(1.2);
}

.indicator:hover {
  background: #C4A962;
}

/* Responsive */
@media (max-width: 1024px) {
  .service-slide {
    flex: 0 0 50%;
  }
}

@media (max-width: 768px) {
  .service-slide {
    flex: 0 0 100%;
  }

  .carousel-nav-btn {
    display: none;
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .carousel-indicators {
  display: none;
  }

  .carousel-nav-left {
    left: -20px;
  }

  .carousel-nav-right {
    right: -20px;
  }
}

/* Gallery Styles */
.gallery {
  padding: 80px 0;
  background: #f8f9fa;
}

/* Gallery Grid - Flexbox Layout (asymmetric, 7 images) */
.gallery-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin: 40px auto 0;
  height: 700px; /* Increased height for asymmetric layout */
  max-width: 800px; /* Limit width for better proportions */
  width: 100%;
}

/* Row containers */
.gallery-row {
  display: flex;
  gap: 12px;
  flex: 1;
  min-height: 0; /* Allow flex items to shrink */
}

/* Row 1+2: Large image (3 rows) + Small image (1 row) */
.gallery-row:nth-child(1) {
  flex: 2; /* Takes triple height to simulate 3 rows */
  height: 450px; /* Increased height for asymmetric layout */
}

.gallery-row:nth-child(1) .gallery-item:nth-child(1) {
  flex: 3; /* Large image takes 3x more width (simulating 3 rows) */
}

.gallery-row:nth-child(1) .gallery-item:nth-child(2) {
  flex: 1; /* Small image takes 1x width (simulating 1 row) */
}

/* Row 3+4: Large image spans 2 rows + 4 small images */
.gallery-row:nth-child(2) {
  flex: 2; /* Takes double height for rows 3+4 */
  height: 320px; /* Height for rows 3+4 */
}

.gallery-row:nth-child(2) .gallery-item:nth-child(1) {
  flex: 1; /* Large image takes left side, full height */
}

.gallery-row:nth-child(2) .gallery-item-group {
  flex: 1; /* Right side for 4 small images */
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.gallery-row:nth-child(2) .gallery-item-group .gallery-sub-row {
  flex: 1; /* Each sub-row takes equal height */
  display: flex;
  gap: 12px;
}

.gallery-row:nth-child(2) .gallery-item-group .gallery-sub-row .gallery-item {
  flex: 1; /* Each small image takes equal width in sub-row */
}

/* Hide extra images beyond 7 for this layout */
.gallery-item:nth-child(n+8) {
  display: none;
}

.gallery-item {
  position: relative;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  min-height: 0;
  min-width: 0;
}

.gallery-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.gallery-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.gallery-item:hover img {
  transform: scale(1.05);
}

.gallery-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: white;
  padding: 10px;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.gallery-item:hover .gallery-overlay {
  transform: translateY(0);
}

.gallery-overlay h4 {
  margin: 0 0 4px 0;
  font-size: 0.9rem;
  font-weight: 600;
  line-height: 1.2;
}

.gallery-overlay p {
  margin: 0;
  font-size: 0.75rem;
  opacity: 0.9;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Gallery Section Header */
.section-header-content {
  position: relative !important;
  display: flex !important;
  justify-content: flex-end !important;
  align-items: center !important;
  width: 100% !important;
  min-height: 80px !important;
}

.section-header-text {
  position: absolute !important;
  left: 50% !important;
  top: 50% !important;
  transform: translate(-50%, -50%) !important;
  text-align: center !important;
  width: auto !important;
}

.section-header-action {
  flex-shrink: 0 !important;
  z-index: 10 !important;
}

.view-more-link {
  display: inline-flex !important;
  align-items: center !important;
  gap: 8px !important;
  color: #C4A962 !important;
  text-decoration: none !important;
  font-size: 1rem !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
  padding: 8px 16px !important;
  border-radius: 25px !important;
  border: 2px solid #C4A962 !important;
  background: transparent !important;
}

.view-more-link:hover {
  background: #C4A962 !important;
  color: white !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 15px rgba(196, 169, 98, 0.3) !important;
}

.view-more-link i {
  font-size: 0.9rem !important;
  transition: transform 0.3s ease !important;
}

.view-more-link:hover i {
  transform: translateX(3px) !important;
}

.gallery-load-more {
  text-align: center;
  margin-top: 40px;
}

.btn-load-more {
  background: #C4A962;
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-load-more:hover:not(:disabled) {
  background: #B8A055;
  transform: translateY(-2px);
}

.btn-load-more:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Loading and Error States */
.loading-state,
.error-state,
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.loading-state p {
  font-size: 1.1rem;
  margin: 0;
}

.error-state p {
  color: #e53e3e;
  margin-bottom: 20px;
}

.btn-retry {
  background: #e53e3e;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
}

.btn-retry:hover {
  background: #c53030;
}

.empty-state p {
  font-size: 1.1rem;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .gallery-grid {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: repeat(4, 1fr);
    height: 400px;
  }

  /* Mobile layout: 2 columns, simpler grid */
  .gallery-item:nth-child(1) {
    grid-column: 1 / 3;
    grid-row: 1;
  }

  .gallery-item:nth-child(2) {
    grid-column: 1;
    grid-row: 2;
  }

  .gallery-item:nth-child(3) {
    grid-column: 2;
    grid-row: 2;
  }

  .gallery-item:nth-child(4) {
    grid-column: 1;
    grid-row: 3;
  }

  .gallery-item:nth-child(5) {
    grid-column: 2;
    grid-row: 3;
  }

  .gallery-item:nth-child(6) {
    grid-column: 1;
    grid-row: 4;
  }

  .gallery-item:nth-child(7) {
    grid-column: 2;
    grid-row: 4;
  }

  .gallery-overlay {
    padding: 15px;
  }

  .gallery-overlay h4 {
    font-size: 1rem;
  }

  .gallery-overlay p {
    font-size: 0.85rem;
  }
}

@media (max-width: 480px) {
  .gallery-grid {
    grid-template-columns: 1fr;
    grid-template-rows: repeat(7, 1fr);
    height: auto;
  }

  /* Mobile: Single column layout */
  .gallery-item:nth-child(n) {
    grid-column: 1;
    grid-row: auto;
    height: 200px;
  }

  .gallery {
    padding: 60px 0;
  }
}

/* About Section Styles */
.about {
  padding: 80px 0;
  background-color: #f8f9fa;
}

/* 2-column layout: image container + text */
.about-layout {
  display: grid;
  grid-template-columns: 1.2fr 0.8fr;
  gap: 40px;
  max-width: 1200px;
  margin: 0 auto;
  align-items: center;
  min-height: 450px;
}

/* Image container with overlay */
.about-image-container {
  position: relative;
  height: 400px;
  border-radius: 20px;
  overflow: visible; /* Allow small image to overflow */
}

.about-large-img {
  width: 100%;
  height: 120%;
  object-fit: cover;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

/* Small image overlay on top-left corner */
.about-small-overlay {
  position: absolute;
  top: 30px;
  left: -100px;
  z-index: 2;
}

.about-small-img {
  width: 180px;
  height: 180px;
  object-fit: cover;
  border-radius: 20px;
  box-shadow: 0 12px 35px rgba(0,0,0,0.25);
  border: 4px solid white;
}

/* Text content section */
.about-text-section {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.about-text-box {
  padding: 40px;
  width: 100%;
  max-width: 400px;
  position: relative;
}

.about-subtitle {
  font-size: 0.85rem;
  color: #B8860B;
  margin: 0 0 10px 0;
  text-transform: uppercase;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.about-title {
  font-size: 2rem;
  font-weight: 700;
  color: #333;
  margin: 0 0 20px 0;
  line-height: 1.2;
}

.about-description {
  font-size: 0.95rem;
  color: #666;
  line-height: 1.7;
  margin: 0 0 25px 0;
}

.about-btn {
  background: #B8860B;
  color: white;
  border: none;
  padding: 14px 28px;
  border-radius: 30px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.about-btn:hover {
  background: #9A7209;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(184, 134, 11, 0.4);
}

.about-content-text {
  position: absolute;
  top: 50px;
  right: 50px;
  max-width: 350px;
  z-index: 2;
}

.about-label {
  font-size: 0.8rem;
  color: #333;
  margin: 0 0 8px 0;
  text-transform: uppercase;
  letter-spacing: 1px;
  text-shadow: 0 1px 2px rgba(255,255,255,0.8);
}

.about-title {
  font-size: 1.8rem;
  font-weight: 700;
  color: #333;
  margin: 0 0 15px 0;
  line-height: 1.2;
  text-transform: uppercase;
  text-shadow: 0 1px 2px rgba(255,255,255,0.8);
}

.about-description {
  font-size: 0.9rem;
  line-height: 1.6;
  color: #444;
  margin: 0 0 20px 0;
  text-shadow: 0 1px 2px rgba(255,255,255,0.8);
}

.btn-about-more {
  display: inline-block;
  padding: 8px 20px;
  background-color: rgba(136, 136, 136, 0.9);
  color: white;
  text-decoration: none;
  border-radius: 25px;
  font-size: 0.8rem;
  font-weight: 500;
  transition: all 0.3s ease;
  backdrop-filter: blur(2px);
}

.btn-about-more:hover {
  background-color: rgba(102, 102, 102, 0.9);
  transform: translateY(-2px);
}

/* Lightbox Navigation Styles */
.lightbox-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.lightbox-content {
  position: relative;
  max-width: 90%;
  max-height: 90%;
  display: flex;
  align-items: center;
}

.lightbox-image-container {
  position: relative;
}

.lightbox-image-container img {
  max-width: 100%;
  max-height: 80vh;
  display: block;
  border-radius: 5px;
}

.lightbox-info {
  position: absolute;
  bottom: -60px;
  left: 0;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 10px 15px;
  border-radius: 0 0 5px 5px;
}

.lightbox-info h3 {
  margin: 0 0 5px 0;
  font-size: 1.2rem;
}

.lightbox-info p {
  margin: 0;
  font-size: 0.9rem;
  opacity: 0.8;
}

.lightbox-close {
  position: absolute;
  top: -40px;
  right: 0;
  background: none;
  border: none;
  color: white;
  font-size: 30px;
  cursor: pointer;
}

.lightbox-nav {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  font-size: 24px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin: 0 20px;
  transition: background 0.3s;
}

.lightbox-nav:hover {
  background: rgba(255, 255, 255, 0.4);
}

/* Map Section Styles */
.map-section {
  padding: 60px 0;
  background-color: #f9f9f9;
}

.map-container {
  margin-bottom: 30px;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  background-color: #fff;
  border: 2px solid rgb(63, 151, 114);
}

.map-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  background-color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 1px solid rgb(63, 151, 114);
}

.map-header:hover {
  background-color: #f8f9fa;
}

.map-header-content h3 {
  margin: 0 0 5px 0;
  font-size: 1.2rem;
  color: #333;
}

.map-header-content p {
  margin: 0;
  font-size: 0.9rem;
  color: #666;
}

.map-toggle-icon {
  width: 30px !important;
  height: 30px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: transform 0.3s ease !important;
  font-size: 1.2rem !important;
  color: white !important;
}

.map-toggle-icon.expanded {
  transform: rotate(180deg) !important;
}

.map-toggle-icon i {
  display: block !important;
  font-size: 1.2rem !important;
  font-family: "Font Awesome 6 Free" !important;
  font-weight: 900 !important;
}

/* FontAwesome Fix for all icons */
i.fas {
  font-family: "Font Awesome 6 Free" !important;
  font-weight: 900 !important;
  font-style: normal !important;
  display: inline-block !important;
}

i.fas::before {
  font-family: "Font Awesome 6 Free" !important;
  font-weight: 900 !important;
}

.map-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.5s ease;
}

.map-content.expanded {
  max-height: 1000px;
}

.map-iframe-container {
  padding: 20px;
}

.policy-content, .notes-content {
  padding: 20px 25px;
  max-height: 250px;
  overflow-y: auto;
  border: 1px solid #eee;
  border-radius: 8px;
  white-space: pre-wrap; /* Preserve line breaks and spaces */
  word-wrap: break-word; /* Break long words if needed */
}

/* Custom scrollbar styling */
.policy-content::-webkit-scrollbar, .notes-content::-webkit-scrollbar {
  width: 10px;
}

.policy-content::-webkit-scrollbar-track, .notes-content::-webkit-scrollbar-track {
  background: #f8f8f8;
  border-radius: 5px;
  margin: 5px;
}

.policy-content::-webkit-scrollbar-thumb, .notes-content::-webkit-scrollbar-thumb {
  background: #b8860b;
  border-radius: 5px;
  border: 1px solid #a0751f;
}

.policy-content::-webkit-scrollbar-thumb:hover, .notes-content::-webkit-scrollbar-thumb:hover {
  background: #9a7209;
}

/* Force scrollbar to always show */
.policy-content, .notes-content {
  scrollbar-width: thin;
  scrollbar-color: #b8860b #f8f8f8;
}

.policy-content h4, .notes-content h4 {
  margin-top: 0;
  color: #333;
}

.policy-content ul, .notes-content ul {
  padding-left: 20px;
}

.policy-content li, .notes-content li {
  margin-bottom: 10px;
  color: #555;
}

/* Price calculation styles removed */

@media (max-width: 768px) {
  .about-layout {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .about-image-container {
    height: 320px;
  }

  .about-small-overlay {
    top: -20px;
    left: -20px;
  }

  .about-small-img {
    width: 140px;
    height: 140px;
  }

  .about-text-section {
    justify-content: center;
  }

  .about-text-box {
    max-width: 100%;
    padding: 30px;
  }

  .about-title {
    font-size: 1.7rem;
  }

  .about-description {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .about-small-img {
    width: 140px;
    height: 180px;
  }

  .about-text-box {
    padding: 15px;
  }

  .about-title {
    font-size: 1.4rem;
  }

  .about-content-text {
    top: 30px;
    right: 20px;
    max-width: 220px;
  }

  .about-title {
    font-size: 1.4rem;
  }
  .about-subtitle {
    margin: 18 0 20px 0;
  }

  .about-description {
    font-size: 0.8rem;
    line-height: 1.5;
  }

  .btn-about-more {
    padding: 6px 16px;
    font-size: 0.75rem;
  }
}

/* Hero Carousel Styles */
.hero-bg-carousel {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.hero-bg-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 1s ease-in-out;
}

.hero-bg-slide.active {
  opacity: 1;
}

.hero-navigation {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  display: flex;
  justify-content: space-between;
  padding: 0 30px;
  z-index: 10;
}

.hero-nav-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.hero-nav-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: scale(1.1);
}

.hero-nav-btn:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.hero-nav-btn i {
  font-size: 18px;
  font-family: "Font Awesome 6 Free" !important;
  font-weight: 900 !important;
}

.hero-indicators {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
  z-index: 10;
}

.hero-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.5);
  background: transparent;
  cursor: pointer;
  transition: all 0.3s ease;
}

.hero-indicator.active {
  background: white;
  border-color: white;
}

.hero-indicator:hover {
  border-color: white;
  background: rgba(255, 255, 255, 0.7);
}

/* Mobile responsive */
@media (max-width: 768px) {
  .hero-navigation {
    padding: 0 15px;
  }

  .hero-nav-btn {
    width: 40px;
    height: 40px;
  }

  .hero-nav-btn i {
    font-size: 14px;
  }

  .hero-indicators {
    bottom: 20px;
  }

  .hero-indicator {
    width: 10px;
    height: 10px;
  }
}

/* Zalo Contact Button */
.zalo-contact-btn {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 60px;
  height: 60px;
  background: #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  z-index: 1000;
  text-decoration: none;
  border: 2px solid #f0f0f0;
}

.zalo-contact-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.25);
  background: #f8f9fa;
}

.zalo-icon {
  width: 40px;
  height: 40px;
  /* Remove filter to show original SVG colors */
}

/* Mobile responsive */
@media (max-width: 768px) {
  .zalo-contact-btn {
    width: 50px;
    height: 50px;
    bottom: 15px;
    right: 15px;
  }

  .zalo-icon {
    width: 28px;
    height: 28px;
  }
}

/* Social Links Styles */
.social-links {
  margin-top: 30px;
}

.social-links h3 {
  color: var(--accent-gold);
  font-size: 1.2rem;
  margin-bottom: 15px;
}

.social-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.social-link {
  color: var(--accent-gold);
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.social-link:hover {
  color: var(--white);
}

/* Social media specific colors */
.social-link.instagram {
  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
  color: white;
  padding: 12px 20px;
  border-radius: 25px;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: transform 0.3s ease;
}

.social-link.facebook {
  background: #1877f2;
  color: white;
  padding: 12px 20px;
  border-radius: 25px;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: transform 0.3s ease;
}

.social-link.youtube {
  background: #ff0000;
  color: white;
  padding: 12px 20px;
  border-radius: 25px;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: transform 0.3s ease;
}

.social-link.instagram:hover,
.social-link.facebook:hover,
.social-link.youtube:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}
</style>
