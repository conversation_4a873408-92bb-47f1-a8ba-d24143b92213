<template>
  <div id="contact-app">
    <!-- Header -->
    <header class="header">
      <div class="container">
        <div class="nav-brand">
          <a href="/" class="logo-link">
            <div class="logo">
              <img
                src="/logo/logo-ngang-08.png"
                alt="Phong Nha Valley"
                class="logo-img"
              />
            </div>
          </a>
        </div>
        <nav class="nav-menu" id="navMenu">
          <ul>
            <li><a href="/">Trang Chủ</a></li>
            <li><a href="/about.html">Về Phong Nha</a></li>
            <li><a href="/services.html">Dịch Vụ</a></li>
            <li><a href="/gallery.html">Thư Viện</a></li>
            <li><a href="/menu.html">Menu</a></li>
            <li><a href="/contact.html" class="active"><PERSON><PERSON><PERSON></a></li>
            <li><a href="#" class="btn-call" @click="handleCallClick">G<PERSON><PERSON></a></li>
          </ul>
        </nav>

        <!-- Language Switcher -->
        <div class="language-switcher">
          <button
            class="lang-btn"
            :class="{ active: currentLanguage === 'vi' }"
            @click="switchLanguage('vi')"
            :disabled="translating"
            title="Tiếng Việt"
          >
            <span class="flag-emoji">🇻🇳</span>
            VI
          </button>
          <button
            class="lang-btn"
            :class="{ active: currentLanguage === 'en' }"
            @click="switchLanguage('en')"
            :disabled="translating"
            title="English - Powered by Google Translate"
          >
            <span class="flag-emoji">🇬🇧</span>
            EN
            <span v-if="currentLanguage === 'en'" class="google-icon">🌐</span>
          </button>
          <div v-if="translating" class="translation-loading">
            <span class="loading-spinner"></span>
            <span>Translating...</span>
          </div>
        </div>
        <div class="hamburger" id="hamburger" @click="toggleMenu">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>
    </header>

    <!-- Hero Section -->
    <section class="hero contact-hero" ref="contactHero">
      <div class="hero-overlay"></div>
      <div class="container">
        <div class="hero-content">
          <h1 class="hero-title">Contact Us</h1>
          <p class="hero-subtitle">PHONG NHA VALLEY GLAMPING</p>
        </div>
      </div>
    </section>

    <!-- Contact & Booking Form Section -->
    <section id="contact-form" class="contact-section">
      <div class="container">
        <div class="contact-content">
          <!-- Contact Information -->
          <div class="contact-info">
            <h2>Get In Touch - UPDATED</h2>
            <div class="contact-details">
              <div class="contact-item">
                <div class="contact-icon">
                  <i class="fas fa-phone"></i>
                </div>
                <div class="contact-text">
                  <h3>Phone</h3>
                  <p>{{ contactInfo.phone }}</p>
                  <button class="btn-call" @click="makeCall">
                    <i class="fas fa-phone-alt"></i> Call Now
                  </button>
                </div>
              </div>

              <div class="contact-item">
                <div class="contact-icon">
                  <i class="fas fa-envelope"></i>
                </div>
                <div class="contact-text">
                  <h3>Email</h3>
                  <p>{{ contactInfo.email }}</p>
                  <button @click="openGmail" class="btn-email">
                    <i class="fas fa-envelope"></i> Send Email
                  </button>
                </div>
              </div>

              <div class="contact-item">
                <div class="contact-icon">
                  <i class="fas fa-map-marker-alt"></i>
                </div>
                <div class="contact-text">
                  <h3>Address</h3>
                  <p v-html="formatAddress(contactInfo.address)"></p>
                </div>
              </div>

              <div class="contact-item">
                <div class="contact-icon">
                  <i class="fas fa-clock"></i>
                </div>
                <div class="contact-text">
                  <h3>Opening Hours</h3>
                  <p>{{ contactInfo.hours }}</p>
                </div>
              </div>
            </div>

            <div class="social-links">
              <h3>Follow Us</h3>
              <div class="social-buttons">
                <a href="#" class="social-link instagram">
                  <i class="fab fa-instagram"></i> Instagram
                </a>
                <a href="#" class="social-link facebook">
                  <i class="fab fa-facebook"></i> Facebook
                </a>
                <a href="https://zalo.me/0354672345" target="_blank" class="social-link zalo">
                  <i class="fas fa-comments"></i> Zalo
                </a>
              </div>
            </div>
          </div>

          <!-- Booking Form -->
          <div class="booking-form">
            <h3>Đặt dịch vụ</h3>
            <form @submit.prevent="handleBookingSubmit" :class="{ loading: bookingLoading }">
              <div class="form-group">
                <label for="service">Dịch vụ</label>
                <select v-model="bookingForm.service" id="service" name="service" required>
                  <option value="">Chọn dịch vụ</option>
                  <option 
                    v-for="service in activeServices" 
                    :key="service.id" 
                    :value="service.id"
                  >
                    {{ service.name }} - {{ formatPrice(service.price) }}
                  </option>
                </select>
              </div>

              <!-- Customer Information -->
              <div class="form-row">
                <div class="form-group">
                  <label for="customer_name">Họ và tên *</label>
                  <input
                    v-model="bookingForm.customer_name"
                    type="text"
                    id="customer_name"
                    name="customer_name"
                    required
                    placeholder="Enter your full name"
                  >
                </div>
                <div class="form-group">
                  <label for="customer_phone">Số điện thoại *</label>
                  <input
                    v-model="bookingForm.customer_phone"
                    type="tel"
                    id="customer_phone"
                    name="customer_phone"
                    required
                    placeholder="0123456789"
                  >
                </div>
              </div>

              <!-- Booking Date -->
              <div class="form-group">
                <label for="booking_date">Ngày đặt dịch vụ *</label>
                <input
                  v-model="bookingForm.booking_date"
                  type="date"
                  id="booking_date"
                  name="booking_date"
                  required
                  :min="todayDate"
                >
              </div>

              <!-- Guest Information -->
              <div class="form-row">
                <div class="form-group">
                  <label for="adults">Người lớn *</label>
                  <input
                    v-model.number="bookingForm.adults"
                    type="number"
                    id="adults"
                    name="adults"
                    min="1"
                    required
                  >
                </div>
                <div class="form-group">
                  <label for="children_6_to_11">Trẻ em (6-11 tuổi)</label>
                  <input
                    v-model.number="bookingForm.children_6_to_11"
                    type="number"
                    id="children_6_to_11"
                    name="children_6_to_11"
                    min="0"
                  >
                </div>
              </div>

              <!-- Special Notes -->
              <div class="form-group">
                <label for="special_notes">Lưu ý cho chúng tôi</label>
                <textarea
                  v-model="bookingForm.special_notes"
                  id="special_notes"
                  name="special_notes"
                  rows="4"
                  placeholder="Any special requirements or questions..."
                ></textarea>
              </div>

              <button type="submit" class="btn-primary" :disabled="bookingLoading">
                {{ bookingLoading ? 'Processing...' : 'SEND MESSAGE' }}
              </button>
            </form>
          </div>
        </div>
      </div>
    </section>

    <!-- Map Section -->
    <section class="map-section">
      <!-- UPDATED: Map only, no headers - 20250714 -->
      <div class="map-embed-full">
        <iframe
          src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3826.8967891234567!2d106.2677!3d17.5833!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMTfCsDM0JzU5LjkiTiAxMDbCsDE2JzAzLjciRQ!5e0!3m2!1sen!2s!4v1234567890123!5m2!1sen!2s"
          width="100%"
          height="500"
          style="border:0;"
          allowfullscreen=""
          loading="lazy"
          referrerpolicy="no-referrer-when-downgrade">
        </iframe>
      </div>
    </section>

    <!-- Notification -->
    <div v-if="notification.show" class="notification" :class="notification.type">
      <div class="notification-content">
        <span>{{ notification.message }}</span>
        <button @click="hideNotification" class="notification-close">&times;</button>
      </div>
    </div>
  </div>
</template>

<script>
import apiService from '../utils/api.js';

export default {
  name: 'ContactApp',
  data() {
    return {
      // Language
      currentLanguage: 'vi',
      translating: false,

      // Hero background
      heroBackground: '/assets/images/hero-bg.jpg',

      // Contact information (will be loaded from Settings API)
      contactInfo: {
        phone: '',
        email: '',
        address: '',
        city: '',
        hours: ''
      },

      // Services data
      activeServices: [],
      servicesLoading: false,

      // Booking form data
      bookingForm: {
        service: '',
        customer_name: '',
        customer_phone: '',
        booking_date: '',
        adults: 2,
        children_under_6: 0,
        children_6_to_11: 0,
        special_notes: ''
      },
      bookingLoading: false,



      // Notification
      notification: {
        show: false,
        message: '',
        type: 'success'
      },


    };
  },

  computed: {
    todayDate() {
      const today = new Date();
      return today.toISOString().split('T')[0];
    }
  },

  async mounted() {
    console.log('ContactApp mounted, initializing...');
    console.log('🔍 DEBUG ContactApp: Language switcher data:', {
      currentLanguage: this.currentLanguage,
      translating: this.translating
    });

    // Check if language switcher is in DOM
    this.$nextTick(() => {
      const langSwitcher = document.querySelector('.language-switcher');
      console.log('🔍 DEBUG ContactApp: Language switcher in DOM:', !!langSwitcher);
      if (langSwitcher) {
        console.log('🔍 DEBUG ContactApp: Language switcher HTML:', langSwitcher.outerHTML);
      }
    });

    await this.loadServices();
    await this.loadContactInfo();
    await this.loadHeroBackground();
  },

  methods: {
    // Language switcher methods
    switchLanguage(lang) {
      console.log('🔍 DEBUG ContactApp: switchLanguage called with:', lang);
      if (this.translating || this.currentLanguage === lang) {
        console.log('🔍 DEBUG ContactApp: Switch blocked - translating:', this.translating, 'currentLanguage:', this.currentLanguage);
        return;
      }

      console.log('🔍 DEBUG ContactApp: Starting language switch to:', lang);
      this.translating = true;
      this.currentLanguage = lang;

      if (lang === 'en') {
        this.translateToEnglish();
      } else {
        this.restoreVietnamese();
      }
    },

    translateToEnglish() {
      const translations = {
        // Navigation
        'Home': 'Home',
        'About Us': 'About Us',
        'Services': 'Services',
        'Gallery': 'Gallery',
        'Menu': 'Menu',
        'Contact': 'Contact',
        'Call': 'Call',

        // Contact page specific
        'Liên hệ với chúng tôi': 'Contact Us',
        'Thông tin liên hệ': 'Contact Information',
        'Đặt dịch vụ': 'Book Service',
        'Địa điểm và bản đồ': 'Location & Map',

        // Form labels
        'Dịch vụ': 'Service',
        'Chọn dịch vụ': 'Select service',
        'Họ và tên': 'Full Name',
        'Số điện thoại': 'Phone Number',
        'Ngày đặt dịch vụ': 'Service Date',
        'Người lớn': 'Adults',
        'Trẻ em (6-11 tuổi)': 'Children (6-11 years)',
        'Trẻ em (Dưới 6 tuổi)': 'Children (Under 6 years)',
        'Ghi chú đặc biệt': 'Special Notes',
        'Đặt ngay': 'Book Now',
        'Đang xử lý...': 'Processing...'
      };

      this.applyTranslations(translations);

      setTimeout(() => {
        this.translating = false;
      }, 1000);
    },

    restoreVietnamese() {
      const reverseTranslations = {
        // Navigation
        'Home': 'Trang Chủ',
        'About Us': 'Về Phong Nha',
        'Services': 'Dịch Vụ',
        'Gallery': 'Thư viện',
        'Menu': 'Menu',
        'Contact': 'Liên hệ',
        'Call': 'Gọi ngay',

        // Contact page specific
        'Contact Us': 'Liên hệ với chúng tôi',
        'Contact Information': 'Thông tin liên hệ',
        'Book Service': 'Đặt dịch vụ',
        'Location & Map': 'Địa điểm và bản đồ',

        // Form labels
        'Service': 'Dịch vụ',
        'Select service': 'Chọn dịch vụ',
        'Full Name': 'Họ và tên',
        'Phone Number': 'Số điện thoại',
        'Service Date': 'Ngày đặt dịch vụ',
        'Adults': 'Người lớn',
        'Children (6-11 years)': 'Trẻ em (6-11 tuổi)',
        'Children (Under 6 years)': 'Trẻ em (Dưới 6 tuổi)',
        'Special Notes': 'Ghi chú đặc biệt',
        'Book Now': 'Đặt ngay',
        'Processing...': 'Đang xử lý...'
      };

      this.applyTranslations(reverseTranslations);

      setTimeout(() => {
        this.translating = false;
      }, 1000);
    },

    applyTranslations(translations) {
      Object.keys(translations).forEach(originalText => {
        const translatedText = translations[originalText];

        const walker = document.createTreeWalker(
          document.body,
          NodeFilter.SHOW_TEXT,
          null,
          false
        );

        const textNodes = [];
        let node;

        while (node = walker.nextNode()) {
          if (node.textContent.trim() === originalText.trim()) {
            textNodes.push(node);
          }
        }

        textNodes.forEach(textNode => {
          textNode.textContent = translatedText;
        });
      });
    },

    handleCallClick() {
      if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
        window.location.href = 'tel:+84941214444';
      } else {
        window.location.href = '/contact.html';
      }
    },

    toggleMenu() {
      const navMenu = document.getElementById('navMenu');
      const hamburger = document.getElementById('hamburger');

      if (navMenu && hamburger) {
        navMenu.classList.toggle('active');
        hamburger.classList.toggle('active');
      }
    },

    // Load services
    async loadServices() {
      try {
        this.servicesLoading = true;
        const services = await apiService.getServices();

        if (services && Array.isArray(services)) {
          this.activeServices = services.filter(service => service.is_active);
          console.log('Active services loaded:', this.activeServices.length);
        }
      } catch (error) {
        console.error('Failed to load services:', error);
        this.showNotification('Failed to load services', 'error');
      } finally {
        this.servicesLoading = false;
      }
    },

    // Load contact information from Settings API
    async loadContactInfo() {
      try {
        const response = await apiService.get('/settings/');
        console.log('Settings API response:', response);

        if (response && response.settings) {
          const settings = response.settings;

          // Update contact info with API data
          this.contactInfo = {
            phone: settings.contact.phone || this.contactInfo.phone,
            email: settings.contact.email || this.contactInfo.email,
            address: settings.contact.address || this.contactInfo.address,
            city: 'Quảng Bình Province', // Static since not in API
            hours: settings.business.opening_hours || this.contactInfo.hours
          };

          console.log('Contact info updated:', this.contactInfo);
        }
      } catch (error) {
        console.error('Failed to load contact info from Settings API:', error);
        // Keep default values if API fails
      }
    },

    // Load hero background
    async loadHeroBackground() {
      try {
        console.log('Loading hero image for Contact page...');
        const response = await apiService.get('/content/homepage/hero');

        // Apply background image if available
        if (response.image_url && this.$refs.contactHero) {
          this.applyHeroBackground(response.image_url);
        }
      } catch (error) {
        console.error('Failed to load hero background:', error);
      }
    },

    applyHeroBackground(imageUrl) {
      if (this.$refs.contactHero) {
        this.$refs.contactHero.style.backgroundImage = `linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('${imageUrl}')`;
        this.$refs.contactHero.style.backgroundSize = 'cover';
        this.$refs.contactHero.style.backgroundPosition = 'center';
        this.$refs.contactHero.style.backgroundAttachment = 'fixed';
      }
    },

    // Handle booking form submission
    async handleBookingSubmit() {
      if (!this.validateForm()) {
        return;
      }

      this.bookingLoading = true;

      try {
        // Convert date to ISO datetime format for backend
        const bookingDate = new Date(this.bookingForm.booking_date + 'T00:00:00.000Z');

        const bookingData = {
          service_id: this.bookingForm.service,
          customer_name: this.bookingForm.customer_name,
          customer_phone: this.bookingForm.customer_phone,
          booking_date: bookingDate.toISOString(),
          adults: this.bookingForm.adults,
          children_6_to_11: this.bookingForm.children_6_to_11,
          children_under_6: this.bookingForm.children_under_6,
          special_notes: this.bookingForm.special_notes
        };

        // Add check-in/check-out dates for accommodation
        const selectedService = this.activeServices.find(s => s.id === this.bookingForm.service);
        if (selectedService && selectedService.type === 'ACCOMMODATION') {
          bookingData.check_in_date = bookingDate.toISOString();
          // Add one day for check-out
          const checkOutDate = new Date(bookingDate);
          checkOutDate.setDate(checkOutDate.getDate() + 1);
          bookingData.check_out_date = checkOutDate.toISOString();
        }

        console.log('📤 Submitting booking data:', bookingData);
        await apiService.createBooking(bookingData);

        this.showNotification('Booking created successfully! We will contact you soon.', 'success');
        this.resetBookingForm();
      } catch (error) {
        console.error('Booking error:', error);
        this.showNotification(error.message || 'Failed to create booking. Please try again.', 'error');
      } finally {
        this.bookingLoading = false;
      }
    },

    // Validate form
    validateForm() {
      if (!this.bookingForm.service) {
        this.showNotification('Please select a service', 'error');
        return false;
      }

      if (!this.bookingForm.customer_name.trim()) {
        this.showNotification('Please enter your full name', 'error');
        return false;
      }

      if (!this.bookingForm.customer_phone.trim()) {
        this.showNotification('Please enter your phone number', 'error');
        return false;
      }

      if (!this.bookingForm.booking_date) {
        this.showNotification('Please select a booking date', 'error');
        return false;
      }

      if (!this.bookingForm.adults || this.bookingForm.adults < 1) {
        this.showNotification('Please specify number of adults', 'error');
        return false;
      }

      return true;
    },

    // Reset booking form
    resetBookingForm() {
      this.bookingForm = {
        service: '',
        customer_name: '',
        customer_phone: '',
        booking_date: '',
        adults: 2,
        children_under_6: 0,
        children_6_to_11: 0,
        special_notes: ''
      };
    },

    // Format price
    formatPrice(price) {
      if (!price) return 'Contact for price';
      return new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
      }).format(price);
    },

    // Format address with line breaks
    formatAddress(address) {
      if (!address) return '';
      return address.replace(/\n/g, '<br>');
    },

    // Make phone call
    makeCall() {
      if (this.isMobile()) {
        window.location.href = `tel:${this.contactInfo.phone}`;
      } else {
        this.scrollToContactForm();
      }
    },

    // Open Gmail with contact email
    openGmail() {
      const email = this.contactInfo.email;
      const subject = encodeURIComponent('Inquiry from Phong Nha Valley Website');
      const body = encodeURIComponent('Hello,\n\nI would like to inquire about your services.\n\nBest regards,');

      // Open Gmail compose with pre-filled recipient, subject, and body
      const gmailUrl = `https://mail.google.com/mail/?view=cm&fs=1&to=${email}&su=${subject}&body=${body}`;
      window.open(gmailUrl, '_blank');
    },

    // Check if mobile device
    isMobile() {
      return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    },

    // Scroll to contact form
    scrollToContactForm() {
      const contactForm = document.getElementById('contact-form');
      if (contactForm) {
        const elementPosition = contactForm.getBoundingClientRect().top;
        const offsetPosition = elementPosition + window.pageYOffset - 80;

        window.scrollTo({
          top: offsetPosition,
          behavior: 'smooth'
        });
      }
    },



    // Show notification
    showNotification(message, type = 'success') {
      this.notification = {
        show: true,
        message,
        type
      };

      setTimeout(() => {
        this.hideNotification();
      }, 5000);
    },

    // Hide notification
    hideNotification() {
      this.notification.show = false;
    }
  }
};
</script>

<style scoped>
/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'SVN-Alluring', serif;
  line-height: 1.6;
  color: #333;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Header styles moved to global CSS */

.nav-brand {
  flex: 0 0 auto;
  margin-right: 60px; /* More space between logo and nav */
  margin-left: -120px; /* Move logo even more to the left */
}

.nav-brand .logo {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 8px 0;
}

.logo-link {
  text-decoration: none;
  color: inherit;
}

/* Logo-svg styling moved to global CSS */

/* Logo image styling */
.logo-img {
  height: 100%;
  width: auto;
  max-width: 200px;
  object-fit: contain;
}

.logo h1 {
  font-family: 'SVN-Megante', serif;
  font-size: 1.8rem;
  font-weight: normal;
  color: #2d2d2d;
  margin: 0;
  line-height: 1.2;
}

.logo span {
  font-family: 'SVN-Alluring', serif;
  font-size: 0.7rem;
  color: #C4A962;
  text-transform: uppercase;
  letter-spacing: 2px;
  display: block;
  margin-top: -2px;
}

/* Navigation styles moved to global CSS */

/* Contact page specific navigation spacing */
.nav-menu {
  flex: 1;
  display: flex;
  justify-content: center;
  margin-right: 40px; /* Space before language switcher */
}

.nav-menu ul {
  display: flex;
  list-style: none;
  align-items: center;
  gap: 32px; /* Optimized gap between nav items */
  margin: 0;
  padding: 0;
}

.nav-menu a {
  color: #2d3748;
  text-decoration: none;
  font-family: 'SVN-Alluring', serif;
  font-weight: 500;
  font-size: 1.05rem; /* Slightly smaller for better proportion */
  transition: all 0.3s ease;
  position: relative;
  padding: 10px 16px; /* Adjusted padding for better balance */
  border-radius: 8px;
  white-space: nowrap;
}

.nav-menu a:hover {
  color: #3F9772;
  background: rgba(63, 151, 114, 0.08);
  transform: translateY(-1px);
}

.nav-menu a.active {
  color: #3F9772;
  background: rgba(63, 151, 114, 0.12);
  font-weight: 600;
}

.btn-call {
  background: #3F9772 !important;
  color: white !important;
  font-size: 1rem !important; /* Consistent with nav items */
  padding: 10px 20px !important; /* Better proportion */
  border-radius: 25px !important;
  font-weight: 600 !important;
  box-shadow: 0 3px 12px rgba(63, 151, 114, 0.25) !important;
  border: 2px solid transparent !important;
  transition: all 0.3s ease !important;
}

.btn-call:hover {
  background: white !important;
  color: #3F9772 !important;
  border-color: #3F9772 !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 5px 18px rgba(63, 151, 114, 0.35) !important;
}

/* Language switcher positioning */
.language-switcher {
  flex: 0 0 auto;
  margin-left: auto; /* Push to the right */
}

.language-switcher .lang-btn {
  font-size: 0.9rem; /* Slightly smaller for better proportion */
  padding: 8px 14px; /* Adjusted padding */
  font-weight: 500;
}

.nav-menu a::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: #3F9772;
  transition: width 0.3s ease;
}

.nav-menu a:hover::after {
  width: 100%;
}

/* btn-call styling moved to global CSS */

.hamburger {
  display: none;
  flex-direction: column;
  cursor: pointer;
  gap: 4px;
}

.hamburger span {
  width: 25px;
  height: 3px;
  background: #2d3748;
  transition: 0.3s;
  border-radius: 2px;
}

/* Mobile Navigation */
@media (max-width: 768px) {
  .header {
    min-height: 80px;
    padding: 15px 0;
  }

  .header.scrolled {
    min-height: 70px;
    padding: 12px 0;
  }

  /* Logo-svg mobile styling moved to global CSS */

  .nav-brand {
    margin-right: 15px;
    margin-left: -15px; /* Still move logo left but less on mobile */
  }

  .nav-menu {
    margin-right: 10px; /* Less margin on mobile */
  }

  .nav-menu ul {
    gap: 18px; /* Optimized gap on mobile */
  }

  .nav-menu a {
    font-size: 0.95rem; /* Slightly smaller on mobile */
    padding: 8px 12px; /* Adjusted mobile padding */
  }

  .btn-call {
    font-size: 0.9rem !important; /* Smaller on mobile */
    padding: 8px 16px !important; /* Compact mobile padding */
  }

  .language-switcher {
    margin-left: 10px; /* Less margin on mobile */
  }

  .language-switcher .lang-btn {
    font-size: 0.85rem; /* Smaller on mobile */
    padding: 6px 10px; /* Compact mobile padding */
  }

  .hamburger {
    display: flex;
  }

  .nav-menu {
    position: fixed;
    top: 80px;
    left: -100%;
    width: 100%;
    height: calc(100vh - 80px);
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(15px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transition: left 0.3s ease;
    padding: 50px 0;
  }

  .nav-menu.active {
    left: 0;
  }

  .nav-menu ul {
    flex-direction: column;
    gap: 30px;
    text-align: center;
  }

  .nav-menu a {
    font-family: 'SVN-Alluring', serif;
    font-size: 1.3rem;
    padding: 15px 25px;
    font-weight: 500;
  }

  /* btn-call mobile styling moved to global CSS */

  .hamburger {
    display: flex;
  }

  .hamburger.active span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
  }

  .hamburger.active span:nth-child(2) {
    opacity: 0;
  }

  .hamburger.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
  }
}

/* Hero Section */
.contact-hero {
  height: 60vh;
  min-height: 500px;
  margin-top: 55px;
  background: linear-gradient(135deg, #2d2d2d 0%, #1a1a1a 100%);
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  display: flex;
  align-items: center;
  position: relative;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1;
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  color: white;
}

.hero-title {
  font-family: 'SVN-Megante', serif;
  font-size: 3.5rem;
  font-weight: normal;
  margin-bottom: 1.5rem;
  color: white;
}

.hero-subtitle {
  font-family: 'SVN-Alluring', serif;
  font-size: 1.2rem;
  font-weight: normal;
  margin-bottom: 2rem;
  color: #C4A962;
  letter-spacing: 2px;
  text-transform: uppercase;
}

.hero-description {
  font-family: 'Poppins', sans-serif;
  font-size: 1.1rem;
  line-height: 1.8;
  opacity: 0.9;
  max-width: 600px;
  margin: 0 auto;
}

/* Contact Section */
.contact-section {
  font-family: 'Poppins', sans-serif  ; 
  padding: 100px 0;
  background: #2d2d2d;
  color: #fff;
}

.contact-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: start;
}

.contact-info h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 40px;
  color: #C4A962;
}

.contact-details {
  margin-bottom: 40px;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 30px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  transition: all 0.3s ease;
}

.contact-item:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-2px);
}

.contact-icon {
  width: 50px;
  height: 50px;
  background: #C4A962;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  flex-shrink: 0;
}

.contact-icon i {
  font-size: 1.2rem;
  color: white;
}

.contact-text h3 {
  color: #C4A962;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 8px;
}

.contact-text p {
  font-family: 'Poppins', sans-serif;
  color: #ccc;
  line-height: 1.6;
  margin-bottom: 10px;
}

.btn-call, .btn-email {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: #C4A962;
  color: white;
  text-decoration: none;
  border: none;
  border-radius: 5px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-call:hover, .btn-email:hover {
  background: #B8A055;
  transform: translateY(-1px);
}

/* Social Links */
.social-links h3 {
  color: #C4A962;
  font-size: 1.3rem;
  margin-bottom: 20px;
}

.social-buttons {
  display: flex;
  gap: 15px;
}

.social-link {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  text-decoration: none;
  border-radius: 25px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.social-link.instagram {
  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
  color: white;
}

.social-link.facebook {
  background: #1877f2;
  color: white;
}

.social-link:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

/* Booking Form */
.booking-form {
  background: rgba(255, 255, 255, 0.05);
  padding: 40px;
  border-radius: 15px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.booking-form h3 {
  font-size: 1.8rem;
  margin-bottom: 30px;
  color: #C4A962;
  text-align: center;
}

.form-group {
  margin-bottom: 20px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-group label {
  font-family: 'Poppins', sans-serif;
  display: block;
  margin-bottom: 8px;
  color: #C4A962;
  font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
  font-family: 'Poppins', sans-serif;
  width: 100%;
  padding: 12px 15px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: white;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #C4A962;
  background: rgba(255, 255, 255, 0.15);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
  color: rgba(255, 255, 255, 0.6);
}



.btn-primary {
  width: 100%;
  padding: 15px;
  background: linear-gradient(135deg, #C4A962 0%, #B8A055 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(196, 169, 98, 0.3);
}

.btn-primary:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.form.loading {
  opacity: 0.7;
  pointer-events: none;
}

/* Map Section */
.map-section {
  padding: 0;
  background: #f8f9fa;
}



.map-container {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 40px;
  align-items: start;
}



.map-embed-full {
  width: 100%;
}

.map-wrapper {
  background: white;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.map-wrapper.collapsed {
  height: 60px;
}

.map-toggle {
  padding: 20px;
  background: #C4A962;
  color: white;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  transition: all 0.3s ease;
}

.map-toggle:hover {
  background: #B8A055;
}

.map-embed {
  height: 400px;
}

.map-embed iframe {
  width: 100%;
  height: 100%;
  border: none;
}

.location-info {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.location-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 25px;
  padding-bottom: 25px;
  border-bottom: 1px solid #eee;
}

.location-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.location-item i {
  width: 40px;
  height: 40px;
  background: #C4A962;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  flex-shrink: 0;
}

.location-item h4 {
  color: #333;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 5px;
}

.location-item p {
  color: #666;
  line-height: 1.6;
}

/* Notification */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  max-width: 400px;
  padding: 15px 20px;
  border-radius: 8px;
  color: white;
  font-weight: 500;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  animation: slideIn 0.3s ease;
}

.notification.success {
  background: #28a745;
}

.notification.error {
  background: #dc3545;
}

.notification-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.notification-close {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  margin-left: 15px;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .contact-hero {
    height: 50vh;
    min-height: 400px;
    margin-top: 70px;
  }

  .hero-title {
    font-size: 2.5rem;
    margin-bottom: 1.2rem;
  }

  .hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 1.5rem;
  }

  .hero-description {
    font-size: 1rem;
    line-height: 1.6;
    max-width: 100%;
    padding: 0 10px;
  }

  .contact-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .contact-info h2 {
    font-size: 2rem;
    text-align: center;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .map-container {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .map-header h2 {
    font-size: 2rem;
  }

  .social-buttons {
    flex-direction: column;
    align-items: center;
  }

  .btn-call, .btn-email {
    font-size: 0.9rem;
    padding: 6px 14px;
  }

  .contact-item {
    flex-direction: column;
    text-align: center;
  }

  .contact-icon {
    margin: 0 auto 15px auto;
  }
}

@media (max-width: 480px) {
  .contact-hero {
    height: 45vh;
    min-height: 350px;
    margin-top: 65px;
  }

  .hero-title {
    font-size: 2rem;
    margin-bottom: 1rem;
    letter-spacing: 0.5px;
  }

  .hero-subtitle {
    font-size: 1.1rem;
    margin-bottom: 1.2rem;
  }

  .hero-description {
    font-size: 0.95rem;
    line-height: 1.5;
    padding: 0 5px;
  }

  .contact-section {
    padding: 60px 0;
  }

  .booking-form {
    padding: 30px 20px;
  }

  .map-section {
    padding: 60px 0;
  }

  .location-info {
    padding: 20px;
  }
}
</style>
