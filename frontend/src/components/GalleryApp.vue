<template>
  <div id="gallery-app">
    <!-- Header with Language Switcher -->
    <header class="header">
      <div class="container">
        <div class="nav-brand">
          <a href="/" class="logo-link">
            <div class="logo">
              <img
                src="/logo/logo-ngang-08.png"
                alt="Phong Nha Valley"
                class="logo-img"
              />
            </div>
          </a>
        </div>

        <nav class="nav-menu" id="navMenu">
          <ul>
            <li><a href="/">Trang Chủ</a></li>
            <li><a href="/about.html">Về Phong Nha</a></li>
            <li><a href="/services.html">Dịch Vụ</a></li>
            <li><a href="/gallery.html" class="active">Thư Viện</a></li>
            <li><a href="/menu.html">Menu</a></li>
            <li><a href="/contact.html"><PERSON><PERSON><PERSON></a></li>
            <li><a href="#" class="btn-call" @click="handleCallClick">Gọi Ngay</a></li>
          </ul>
        </nav>

        <!-- Language Switcher with Google Translate -->
        <div class="language-switcher">
          <button
            class="lang-btn"
            :class="{ active: currentLanguage === 'vi' }"
            @click="switchLanguage('vi')"
            :disabled="translating"
            title="Tiếng Việt"
          >
            <span class="flag-emoji">🇻🇳</span>
            VI
          </button>
          <button
            class="lang-btn"
            :class="{ active: currentLanguage === 'en' }"
            @click="switchLanguage('en')"
            :disabled="translating"
            title="English - Powered by Google Translate"
          >
            <span class="flag-emoji">🇬🇧</span>
            EN
            <span v-if="currentLanguage === 'en'" class="google-icon">🌐</span>
          </button>
          <div v-if="translating" class="translation-loading">
            <span class="loading-spinner"></span>
            <span>Translating...</span>
          </div>
        </div>

        <div class="hamburger" id="hamburger" @click="toggleMenu">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>
    </header>

    <!-- Hero Section -->
    <section class="gallery-hero" ref="galleryHero">
      <div class="hero-overlay"></div>
      <div class="container">
        <div class="hero-content">
          <h1 class="hero-title">Thư Viện Ảnh</h1>
          <p class="hero-subtitle">PHONG NHA VALLEY GLAMPING</p>
        </div>
      </div>
    </section>

    <!-- Gallery Content Section -->
    <section class="gallery-content">
      <div class="container">
        <div class="section-header">
          <h2>Bộ Sưu Tập Hình Ảnh</h2>
          <p>Những khoảnh khắc đẹp nhất tại Phong Nha Valley</p>
        </div>

        <!-- Loading State -->
        <div v-if="loading" class="loading-state">
          <div class="loading-spinner"></div>
          <p>Đang tải hình ảnh...</p>
        </div>

        <!-- Error State -->
        <div v-else-if="error" class="error-state">
          <i class="fas fa-exclamation-triangle"></i>
          <p>{{ error }}</p>
          <button @click="loadGalleryImages" class="btn-retry">Thử lại</button>
        </div>

        <!-- Gallery Grid -->
        <div v-else class="gallery-grid">
          <div 
            v-for="(image, index) in galleryImages" 
            :key="image.id"
            class="gallery-item"
            @click="openLightbox(index)"
          >
            <div class="gallery-image-container">
              <img 
                :src="image.image_url" 
                :alt="image.alt_text || image.title"
                class="gallery-image"
                loading="lazy"
              >
              <div class="gallery-overlay">
                <i class="fas fa-search-plus"></i>
                <h3 v-if="image.title">{{ image.title }}</h3>
              </div>
            </div>
          </div>
        </div>

        <!-- Empty State -->
        <div v-if="!loading && !error && galleryImages.length === 0" class="empty-state">
          <i class="fas fa-images"></i>
          <h3>Chưa có hình ảnh</h3>
          <p>Bộ sưu tập hình ảnh sẽ được cập nhật sớm.</p>
        </div>

        <!-- Pagination -->
        <div v-if="pagination && pagination.total_pages > 1" class="pagination">
          <button 
            @click="changePage(pagination.current_page - 1)"
            :disabled="!pagination.has_previous"
            class="pagination-btn"
          >
            <i class="fas fa-chevron-left"></i>
            Trước
          </button>
          
          <div class="pagination-info">
            Trang {{ pagination.current_page }} / {{ pagination.total_pages }}
          </div>
          
          <button 
            @click="changePage(pagination.current_page + 1)"
            :disabled="!pagination.has_next"
            class="pagination-btn"
          >
            Sau
            <i class="fas fa-chevron-right"></i>
          </button>
        </div>
      </div>
    </section>

    <!-- Lightbox Modal -->
    <div v-if="lightboxImage !== null" class="lightbox-modal" @click="closeLightbox">
      <div class="lightbox-content" @click.stop>
        <button class="lightbox-close" @click="closeLightbox">
          <i class="fas fa-times"></i>
        </button>
        
        <!-- Navigation Buttons -->
        <button
          v-if="galleryImages.length > 1"
          class="lightbox-nav lightbox-prev"
          @click="previousImage"
          :title="`Ảnh trước (${lightboxImage}/${galleryImages.length})`"
        >
          <i class="fas fa-chevron-left"></i>
        </button>

        <button
          v-if="galleryImages.length > 1"
          class="lightbox-nav lightbox-next"
          @click="nextImage"
          :title="`Ảnh tiếp theo (${lightboxImage + 2}/${galleryImages.length})`"
        >
          <i class="fas fa-chevron-right"></i>
        </button>

        <!-- Image Container -->
        <div class="lightbox-image-container">
          <img
            :src="galleryImages[lightboxImage].image_url"
            :alt="galleryImages[lightboxImage].alt_text || galleryImages[lightboxImage].title"
            class="lightbox-image"
            @click="nextImage"
            :title="galleryImages[lightboxImage].title || 'Click để xem ảnh tiếp theo'"
          >

          <!-- Image Caption -->
          <div v-if="galleryImages[lightboxImage].title" class="lightbox-caption">
            <h3>{{ galleryImages[lightboxImage].title }}</h3>
          </div>

          <!-- Navigation Dots -->
          <div v-if="galleryImages.length > 1" class="lightbox-dots">
            <button
              v-for="(image, index) in galleryImages"
              :key="image.id"
              class="lightbox-dot"
              :class="{ active: index === lightboxImage }"
              @click="goToImage(index)"
              :title="`Ảnh ${index + 1}: ${image.title || 'Không có tiêu đề'}`"
            ></button>
          </div>
        </div>
        
        <div v-if="galleryImages.length > 1" class="lightbox-counter">
          {{ lightboxImage + 1 }} / {{ galleryImages.length }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import apiService from '../utils/api.js';

export default {
  name: 'GalleryApp',
  data() {
    return {
      // Gallery data
      galleryImages: [],
      loading: true,
      error: null,
      
      // Pagination
      pagination: null,
      currentPage: 1,
      itemsPerPage: 12,
      
      // Lightbox
      lightboxImage: null,
      
      // Hero data
      heroData: {},

      // Language
      currentLanguage: 'vi',
      translating: false
    };
  },
  
  async mounted() {
    await this.loadHeroImage();
    await this.loadGalleryImages();
    this.setupKeyboardNavigation();
  },
  
  beforeUnmount() {
    this.removeKeyboardNavigation();
  },
  
  methods: {
    // Header methods
    toggleMenu() {
      const navMenu = document.getElementById('navMenu');
      const hamburger = document.getElementById('hamburger');
      navMenu.classList.toggle('active');
      hamburger.classList.toggle('active');
    },

    handleCallClick() {
      // Check if device supports phone calls (mobile)
      if (/Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
        window.location.href = 'tel:+84123456789';
      } else {
        // For desktop, show notification or copy number
        alert('Số điện thoại: +84 123 456 789');
      }
    },

    // Language switching methods
    async switchLanguage(lang) {
      if (this.translating || this.currentLanguage === lang) return;

      console.log(`🔄 Switching language to: ${lang}`);
      this.translating = true;

      try {
        if (lang === 'vi') {
          // Switch back to Vietnamese
          this.currentLanguage = 'vi';
          location.reload(); // Simple reload to restore original Vietnamese
        } else if (lang === 'en') {
          // Switch to English using Google Translate
          this.currentLanguage = 'en';
          await this.translateToEnglish();
        }
      } catch (error) {
        console.error('Translation error:', error);
        alert('Lỗi khi chuyển đổi ngôn ngữ. Vui lòng thử lại.');
      } finally {
        this.translating = false;
      }
    },

    async translateToEnglish() {
      console.log('🌐 Starting English translation...');

      // Get all text elements to translate
      const elementsToTranslate = this.getElementsToTranslate();

      if (elementsToTranslate.length === 0) {
        console.log('No elements found to translate');
        return;
      }

      // Translate in batches
      const batchSize = 10;
      for (let i = 0; i < elementsToTranslate.length; i += batchSize) {
        const batch = elementsToTranslate.slice(i, i + batchSize);
        await this.translateBatch(batch);

        // Small delay between batches
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      console.log('✅ Translation completed');
    },

    getElementsToTranslate() {
      const elements = [];
      const walker = document.createTreeWalker(
        document.body,
        NodeFilter.SHOW_TEXT,
        {
          acceptNode: (node) => {
            const text = node.textContent?.trim();
            if (text &&
                text.length > 2 &&
                this.isVietnamese(text) &&
                !node.parentElement.closest('.language-switcher') &&
                !node.parentElement.closest('.nav-menu')) {
              return NodeFilter.FILTER_ACCEPT;
            }
            return NodeFilter.FILTER_REJECT;
          }
        }
      );

      let node;
      while (node = walker.nextNode()) {
        elements.push(node);
      }

      return elements;
    },

    async translateBatch(textNodes) {
      const textsToTranslate = textNodes.map(node => node.textContent.trim());
      const combinedText = textsToTranslate.join(' | ');

      try {
        const translatedText = await this.callGoogleTranslate(combinedText);
        const translatedParts = translatedText.split(' | ');

        textNodes.forEach((node, index) => {
          if (translatedParts[index]) {
            node.textContent = translatedParts[index];
            node.parentElement.classList.add('google-translated');
          }
        });
      } catch (error) {
        console.error('Batch translation error:', error);
      }
    },

    async callGoogleTranslate(text) {
      const url = `https://translate.googleapis.com/translate_a/single?client=gtx&sl=vi&tl=en&dt=t&q=${encodeURIComponent(text)}`;

      try {
        const response = await fetch(url);
        const data = await response.json();

        if (data && data[0]) {
          return data[0].map(item => item[0]).join('');
        }

        throw new Error('Invalid translation response');
      } catch (error) {
        console.error('Google Translate API error:', error);
        throw error;
      }
    },

    isVietnamese(text) {
      const vietnameseChars = /[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ]/i;
      const vietnameseWords = /\b(và|của|trong|với|cho|từ|đến|về|trên|dưới|này|đó|những|các|một|hai|ba|tại|theo|như|để|khi|nếu|hoặc|nhưng|mà|rằng|đã|sẽ|đang|được|có|là|không|chỉ|cũng|thì|hay|vì|do|nên|phải|cần|muốn|thích|biết|hiểu|nói|làm|đi|đến|về|ra|vào|lên|xuống|qua|sang|tới|đây|đó|nào|gì|ai|đâu|sao|thế|thì|rồi|mới|cũ|lớn|nhỏ|cao|thấp|dài|ngắn|rộng|hẹp|nhanh|chậm|mạnh|yếu|tốt|xấu|đẹp|xấu|sạch|bẩn|nóng|lạnh|ấm|mát|sáng|tối|đỏ|xanh|vàng|trắng|đen|hôm|ngày|tháng|năm|tuần|giờ|phút|giây)/i;

      return vietnameseChars.test(text) || vietnameseWords.test(text);
    },

    async loadHeroImage() {
      try {
        console.log('Loading hero image for Gallery page...');
        const response = await apiService.get('/content/homepage/hero');
        this.heroData = response;
        
        // Apply background image if available
        if (response.image_url && this.$refs.galleryHero) {
          this.applyHeroBackground(response.image_url);
        }
      } catch (error) {
        console.error('Failed to load hero image:', error);
      }
    },
    
    applyHeroBackground(imageUrl) {
      if (this.$refs.galleryHero) {
        this.$refs.galleryHero.style.backgroundImage = `url('${imageUrl}')`;
      }
    },
    
    async loadGalleryImages() {
      this.loading = true;
      this.error = null;
      
      try {
        console.log('🖼️ Loading gallery images for Gallery page...');
        const response = await apiService.getGalleryImages(this.currentPage, this.itemsPerPage);
        console.log('📡 Gallery API Response:', response);
        
        this.galleryImages = response.gallery || [];
        this.pagination = response.pagination || null;
        
        console.log(`✅ Loaded ${this.galleryImages.length} gallery images`);
      } catch (error) {
        console.error('❌ Failed to load gallery images:', error);
        this.error = 'Không thể tải hình ảnh. Vui lòng thử lại sau.';
        this.galleryImages = [];
      } finally {
        this.loading = false;
      }
    },
    
    async changePage(page) {
      if (page < 1 || (this.pagination && page > this.pagination.total_pages)) {
        return;
      }
      
      this.currentPage = page;
      await this.loadGalleryImages();
      
      // Scroll to top of gallery
      const galleryContent = document.querySelector('.gallery-content');
      if (galleryContent) {
        galleryContent.scrollIntoView({ behavior: 'smooth' });
      }
    },
    
    openLightbox(index) {
      this.lightboxImage = index;
      document.body.style.overflow = 'hidden';
    },
    
    closeLightbox() {
      this.lightboxImage = null;
      document.body.style.overflow = '';
    },
    
    nextImage() {
      if (this.lightboxImage < this.galleryImages.length - 1) {
        this.lightboxImage++;
      } else {
        this.lightboxImage = 0; // Loop to first image
      }
    },

    previousImage() {
      if (this.lightboxImage > 0) {
        this.lightboxImage--;
      } else {
        this.lightboxImage = this.galleryImages.length - 1; // Loop to last image
      }
    },

    goToImage(index) {
      if (index >= 0 && index < this.galleryImages.length) {
        this.lightboxImage = index;
      }
    },
    
    setupKeyboardNavigation() {
      this.handleKeydown = (e) => {
        if (this.lightboxImage !== null) {
          switch (e.key) {
            case 'Escape':
              this.closeLightbox();
              break;
            case 'ArrowLeft':
              this.previousImage();
              break;
            case 'ArrowRight':
              this.nextImage();
              break;
            case ' ': // Spacebar
              e.preventDefault();
              this.nextImage();
              break;
          }
        }
      };

      // Touch/Swipe support
      this.setupTouchNavigation();

      document.addEventListener('keydown', this.handleKeydown);
    },

    setupTouchNavigation() {
      let startX = 0;
      let startY = 0;

      this.handleTouchStart = (e) => {
        if (this.lightboxImage !== null) {
          startX = e.touches[0].clientX;
          startY = e.touches[0].clientY;
        }
      };

      this.handleTouchEnd = (e) => {
        if (this.lightboxImage !== null) {
          const endX = e.changedTouches[0].clientX;
          const endY = e.changedTouches[0].clientY;
          const diffX = startX - endX;
          const diffY = startY - endY;

          // Check if horizontal swipe is more significant than vertical
          if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
            if (diffX > 0) {
              // Swipe left - next image
              this.nextImage();
            } else {
              // Swipe right - previous image
              this.previousImage();
            }
          }
        }
      };

      document.addEventListener('touchstart', this.handleTouchStart);
      document.addEventListener('touchend', this.handleTouchEnd);
    },
    
    removeKeyboardNavigation() {
      if (this.handleKeydown) {
        document.removeEventListener('keydown', this.handleKeydown);
      }
      if (this.handleTouchStart) {
        document.removeEventListener('touchstart', this.handleTouchStart);
      }
      if (this.handleTouchEnd) {
        document.removeEventListener('touchend', this.handleTouchEnd);
      }
    }
  }
};
</script>

<style scoped>
/* Logo image styling */
.logo-img {
  height: 100%;
  width: auto;
  max-width: 200px;
  object-fit: contain;
}

/* Gallery Hero Section */
.gallery-hero {
  height: 60vh;
  background: linear-gradient(135deg, #2d2d2d 0%, #1a1a1a 100%);
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  display: flex;
  align-items: center;
  position: relative;
  margin-top: 0;
  padding-top: 80px;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  color: #fff;
  max-width: 800px;
  margin: 0 auto;
}

.hero-title {
  font-family: 'SVN-Megante', serif;
  font-size: 4rem;
  font-weight: normal;
  margin-bottom: 20px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.hero-subtitle {
  font-family: 'SVN-Alluring', serif;
  font-size: 1.2rem;
  color: #C4A962;
  letter-spacing: 3px;
  text-transform: uppercase;
  margin-bottom: 30px;
}

.hero-description {
  font-family: 'Poppins', sans-serif;
  font-size: 1.1rem;
  line-height: 1.8;
  opacity: 0.9;
  max-width: 600px;
  margin: 0 auto;
}

/* Gallery Content Section */
.gallery-content {
  padding: 100px 0;
  background: #f8f9fa;
}

.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-header h2 {
  font-family: 'SVN-Megante', serif;
  font-size: 2.5rem;
  color: #2d2d2d;
  margin-bottom: 15px;
}

.section-header p {
  font-family: 'SVN-Alluring', serif;
  font-size: 1.1rem;
  color: #666;
}

/* Loading, Error, Empty States */
.loading-state,
.error-state,
.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #C4A962;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-state i,
.empty-state i {
  font-size: 3rem;
  color: #C4A962;
  margin-bottom: 20px;
}

.btn-retry {
  background: #C4A962;
  color: #fff;
  border: none;
  padding: 12px 30px;
  border-radius: 25px;
  cursor: pointer;
  font-size: 1rem;
  margin-top: 20px;
  transition: all 0.3s ease;
}

.btn-retry:hover {
  background: #B8A055;
  transform: translateY(-2px);
}

/* Gallery Grid */
.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 30px;
  margin-bottom: 60px;
}

.gallery-item {
  cursor: pointer;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  background: #fff;
}

.gallery-item:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.gallery-image-container {
  position: relative;
  height: 250px;
  overflow: hidden;
}

.gallery-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.gallery-item:hover .gallery-image {
  transform: scale(1.1);
}

.gallery-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  color: #fff;
}

.gallery-item:hover .gallery-overlay {
  opacity: 1;
}

.gallery-overlay i {
  font-size: 2rem;
  margin-bottom: 10px;
  color: #C4A962;
}

.gallery-overlay h3 {
  font-size: 1.1rem;
  text-align: center;
  margin: 0;
  padding: 0 15px;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  margin-top: 40px;
}

.pagination-btn {
  background: #C4A962;
  color: #fff;
  border: none;
  padding: 12px 20px;
  border-radius: 25px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.pagination-btn:hover:not(:disabled) {
  background: #B8A055;
  transform: translateY(-2px);
}

.pagination-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}

.pagination-info {
  font-weight: 600;
  color: #2d2d2d;
}

/* Lightbox Modal */
.lightbox-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.lightbox-content {
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.lightbox-close {
  position: absolute;
  top: -50px;
  right: 0;
  background: none;
  border: none;
  color: #fff;
  font-size: 2rem;
  cursor: pointer;
  z-index: 10001;
  padding: 10px;
  transition: color 0.3s ease;
}

.lightbox-close:hover {
  color: #C4A962;
}

.lightbox-nav {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(196, 169, 98, 0.9);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: #fff;
  font-size: 1.8rem;
  cursor: pointer;
  padding: 18px;
  border-radius: 50%;
  transition: all 0.3s ease;
  z-index: 10001;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.lightbox-nav:hover {
  background: rgba(196, 169, 98, 1);
  border-color: rgba(255, 255, 255, 0.6);
  transform: translateY(-50%) scale(1.15);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
}

.lightbox-nav:active {
  transform: translateY(-50%) scale(1.05);
}

.lightbox-prev {
  left: 20px;
}

.lightbox-next {
  right: 20px;
}

.lightbox-image-container {
  text-align: center;
}

.lightbox-image {
  max-width: 100%;
  max-height: 80vh;
  object-fit: contain;
  border-radius: 10px;
}

.lightbox-caption {
  margin-top: 20px;
  color: #fff;
  text-align: center;
}

.lightbox-caption h3 {
  font-size: 1.2rem;
  margin: 0;
}

.lightbox-counter {
  position: absolute;
  bottom: -50px;
  left: 50%;
  transform: translateX(-50%);
  color: #fff;
  font-size: 1rem;
  background: rgba(0, 0, 0, 0.5);
  padding: 8px 16px;
  border-radius: 20px;
}

/* Navigation Dots */
.lightbox-dots {
  position: absolute;
  bottom: -80px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 8px;
  background: rgba(0, 0, 0, 0.5);
  padding: 10px 15px;
  border-radius: 25px;
  max-width: 80vw;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.lightbox-dots::-webkit-scrollbar {
  display: none;
}

.lightbox-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.5);
  background: transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.lightbox-dot:hover {
  border-color: rgba(196, 169, 98, 0.8);
  background: rgba(196, 169, 98, 0.3);
  transform: scale(1.2);
}

.lightbox-dot.active {
  background: #C4A962;
  border-color: #C4A962;
  transform: scale(1.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-subtitle {
    font-size: 1rem;
  }
  
  .hero-description {
    font-size: 1rem;
  }
  
  .gallery-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
  }
  
  .gallery-image-container {
    height: 200px;
  }
  
  .lightbox-nav {
    font-size: 1.5rem;
    padding: 15px;
    width: 50px;
    height: 50px;
    left: 10px;
  }

  .lightbox-next {
    right: 10px;
  }

  .lightbox-close {
    top: 10px;
    right: 10px;
  }

  .lightbox-counter {
    bottom: 10px;
  }

  .lightbox-dots {
    bottom: -60px;
    padding: 8px 12px;
  }

  .lightbox-dot {
    width: 10px;
    height: 10px;
  }
}

@media (max-width: 480px) {
  .gallery-grid {
    grid-template-columns: 1fr;
  }
  
  .pagination {
    flex-direction: column;
    gap: 15px;
  }
  
  .pagination-btn {
    width: 100%;
    justify-content: center;
  }
}

/* Header styles removed - using global CSS from style.css */

/* Mobile navigation styles removed - using global CSS from style.css */
</style>
