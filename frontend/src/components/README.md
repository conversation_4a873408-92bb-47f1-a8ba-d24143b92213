# Frontend Components

This directory is reserved for future component-based development.

## Planned Components

### Common Components
- `Header` - Navigation and branding
- `Footer` - Site footer with links and contact info
- `Modal` - Reusable modal dialogs
- `Button` - Standardized button components
- `Form` - Form components and validation

### Page-Specific Components
- `Hero` - Landing page hero section
- `ServiceCard` - Service display cards
- `Gallery` - Image gallery component
- `BookingForm` - Booking form component
- `ContactInfo` - Contact information display

### Admin Components
- `Sidebar` - Admin navigation sidebar
- `Dashboard` - Dashboard widgets and stats
- `DataTable` - Reusable data tables
- `ServiceManager` - Service management interface
- `BookingManager` - Booking management interface

## Future Development

When implementing component-based architecture:

1. Create individual component files (HTML templates, CSS modules, JS modules)
2. Implement a build system to combine components
3. Add component documentation and examples
4. Consider using a frontend framework (React, Vue, or vanilla JS modules)

## Current Status

Currently using traditional HTML/CSS/JS structure. Components directory is prepared for future modularization.
