<template>
  <div id="about-app">
    <!-- Header -->
    <header class="header">
      <div class="container">
        <div class="nav-brand">
          <a href="/" class="logo-link">
            <div class="logo">
              <img
                src="/logo/logo-ngang-08.png"
                alt="Phong Nha Valley"
                class="logo-img"
              />
            </div>
          </a>
        </div>
        <nav class="nav-menu" id="navMenu">
          <ul>
            <li><a href="/">Trang Chủ</a></li>
            <li><a href="/about.html" class="active">Về Phong Nha</a></li>
            <li><a href="/services.html">Dịch Vụ</a></li>
            <li><a href="/gallery.html">Th<PERSON> Viện</a></li>
            <li><a href="/menu.html">Menu</a></li>
            <li><a href="/contact.html"><PERSON><PERSON><PERSON></a></li>
            <li><a href="#" class="btn-call" @click="handleCallClick">G<PERSON><PERSON></a></li>
          </ul>
        </nav>

        <!-- Language Switcher -->
        <div class="language-switcher">
          <button
            class="lang-btn"
            :class="{ active: currentLanguage === 'vi' }"
            @click="switchLanguage('vi')"
            title="Tiếng Việt"
          >
            <span class="flag-emoji">🇻🇳</span>
            <span class="lang-text">VI</span>
          </button>
          <button
            class="lang-btn"
            :class="{ active: currentLanguage === 'en' }"
            @click="switchLanguage('en')"
            title="English"
          >
            <span class="flag-emoji">🇬🇧</span>
            <span class="lang-text">EN</span>
          </button>
        </div>

        <div class="hamburger" id="hamburger" @click="toggleMenu">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>
    </header>

    <!-- Hero Section -->
    <section class="about-hero" ref="aboutHero">
      <div class="hero-content">
        <h1 class="hero-title">Thung Lũng Xanh</h1>
      </div>
    </section>

    <!-- About Content Section -->
    <section class="about-content">
      <div class="container">
        <!-- Welcome Message -->
        <div class="welcome-message">
          <p>Chào mừng bạn đến với Phong Nha Valley - nơi thiên nhiên chạm đến tâm hồn!</p>
        </div>

        <!-- Main Title -->
        <div class="about-main-title">
          <h1>VỀ CHÚNG TÔI</h1>
        </div>

        <!-- First Row: History + Mission/Vision + Image 1 -->
        <div class="about-section first-row-section">
          <div class="left-content">
            <!-- History Section -->
            <div class="history-content">
              <h2>Lịch sử hình thành</h2>
              <p>
                Phong Nha Valley là dự án du lịch thực nghiệm sản phẩm du
                lịch "Trải nghiệm thiên nhiên kết hợp lưu trú cắm trại Trằm Mé -
                Chày Lập Glamping". Từ một khu đất hoang sơ bên cạnh dòng
                sông Chày thơ mộng, một điểm đến độc đáo được hình thành
                từ ước mơ hòa và gắn gối với thiên nhiên của khách sở thể
                tạm gác lại những bộn bề cuộc sống và hòa mình vào vẻ
                đẹp của thiên nhiên.
              </p>
            </div>

            <!-- Mission and Vision Section -->
            <div class="mission-vision-content">
              <div class="mission-content">
                <h3>Sứ mệnh</h3>
                <p>
                  Sứ mệnh của chúng tôi là
                  mang đến cho du khách
                  những trải nghiệm lưu trú
                  độc đáo và vui chơi đẳng nhớ,
                  nối mọi người có thể kết nối
                  với thiên nhiên, gần kết với
                  gia đình và bạn bè, đồng thời
                  tạo tạo năng lượng tích cực
                  cho hành trình tiếp theo.
                </p>
              </div>
              <div class="vision-content">
                <h3>Tầm nhìn</h3>
                <p>
                  Phát triển Phong Nha Valley
                  trở thành một khu du lịch trải
                  nghiệm - lưu trú, mang đến
                  những trải nghiệm vui chơi,
                  giải trí chất lượng cao và bền
                  vững.
                </p>
              </div>
            </div>
          </div>

          <div class="about-image-container">
            <img
              v-if="aboutData.large_image"
              :src="aboutData.large_image"
              :alt="aboutData.title || 'About Phong Nha Valley'"
              class="about-large-image"
              @error="handleImageError"
            />
            <div v-else class="image-placeholder">
              <div class="image-box">Loading...</div>
            </div>
          </div>
        </div>

        <!-- Second Row: Image 2 + Core Values -->
        <div class="about-section second-row-section">
          <div class="about-image-container">
            <img
              v-if="aboutData.large_image2"
              :src="aboutData.large_image2"
              :alt="aboutData.title || 'About Phong Nha Valley'"
              class="about-large-image"
              @error="handleImageError"
            />
            <div v-else class="image-placeholder">
              <div class="image-box">Loading Image 2...</div>
            </div>
          </div>

          <div class="values-content">
            <h2>Giá trị cốt lõi</h2>

            <div class="value-item">
              <h4>Cân bằng thiên nhiên</h4>
              <p>
                Chúng tôi mang đến cho cùy khách những trải nghiệm thú vị,
                nơi bạn được hòa mình vào thiên nhiên trong lành, khám phá
                và tận hưởng những khoảnh khắc bình yên khó quên. Đồng
                thời, chúng tôi cam kết thực đây du lịch bền vững, giảm thiểu
                tối đa tác động tiêu cực đến môi trường.
              </p>
            </div>

            <div class="value-item">
              <h4>Trải nghiệm dịch vụ</h4>
              <p>
                Chúng tôi tạo ra một không gian vui chơi đa dạng, phù hợp với
                mọi lứa tuổi, nơi mọi khách hàng đều trân nghiệm vui và
                trở thành ký niệm đáng nhớ.
              </p>
            </div>

            <div class="value-item">
              <h4>Chất lượng dịch vụ</h4>
              <p>
                Chúng tôi cam kết mang đến chất lượng dịch vụ tốt nhất, với cơ
                sở vật chất hiện đại và đội ngũ nhân viên phục vụ chuyên
                nghiệp, tận tâm, đảm bảo cho bạn những trải nghiệm tuyệt
                vời.
              </p>
            </div>

            <div class="value-item">
              <h4>Cộng đồng</h4>
              <p>
                Chúng tôi xây dựng một cộng đồng đánh cho những người yêu
                thích thiên nhiên, nơi mọi người có thể kết nối, chia sẻ và cùng nhau tạo nên những kỷ niệm đẹp. Đồng thời,
                chúng tôi thúc đẩy văn hóa cộng đồng, tạo cơ hội việc làm và
                đóng góp tích cực cho sự phát triển bền vững của địa phương.
              </p>
            </div>
          </div>
        </div>

        <!-- Closing Message -->
        <div class="closing-message">
          <p>Đến với Phong Nha Valley, bạn không chỉ có những trải nghiệm đáng nhớ mà còn gặp được những tâm hồn bình yên và những kỷ niệm khó quên. Hãy cùng chúng tôi khám phá, tận hưởng những khoảnh khắc tuyệt vời nhất giữa thiên nhiên hoang sơ.</p>
        </div>
      </div>
    </section>

    <!-- Services Section -->
    <section class="services-section">
      <div class="container">
        <h2 class="section-title">DỊCH VỤ KHÁC</h2>

        <div v-if="servicesLoading" class="loading-state">
          <p>Đang tải dịch vụ...</p>
        </div>

        <div v-else class="services-carousel">
          <!-- Navigation Arrows -->
          <button
            class="carousel-nav prev"
            @click="prevSlide"
            :disabled="currentSlide === 0"
          >
            <i class="fas fa-chevron-left"></i>
          </button>

          <button
            class="carousel-nav next"
            @click="nextSlide"
            :disabled="currentSlide >= maxSlide"
          >
            <i class="fas fa-chevron-right"></i>
          </button>

          <!-- Services Container -->
          <div class="services-container">
            <div
              class="services-track"
              :style="{ transform: `translateX(-${currentSlide * slideWidth}%)` }"
            >
              <div
                v-for="service in services"
                :key="service.id"
                class="service-slide"
              >
                <div class="service-card" @click="goToServicePage(service)">
                  <div
                    class="service-image"
                    :style="getServiceImageStyle(service)"
                  >
                    <div
                      class="service-status-badge"
                      :class="service.is_active ? 'available' : 'unavailable'"
                    >
                      {{ service.is_active ? 'AVAILABLE' : 'UNAVAILABLE' }}
                    </div>
                  </div>

                  <div class="service-content">
                    <div class="service-category">
                      {{ service.type === 'ACCOMMODATION' ? 'ACCOMMODATION' : 'EXPERIENCE' }}
                    </div>

                    <h3 class="service-title">{{ service.name }}</h3>

                    <p class="service-description">{{ service.description || 'Tạo hình ảnh test' }}</p>

                    <div class="service-pricing">
                      <div class="main-price">{{ formatPrice(service.price) }}</div>
                      <div v-if="getChildPrice(service)" class="child-price">
                        Child: {{ formatPrice(getChildPrice(service)) }}
                      </div>
                    </div>

                    <div v-if="service.duration" class="service-time">
                      {{ service.duration }}
                    </div>

                    <button
                      class="btn-book-now"
                      @click="bookService(service)"
                    >
                      Đặt Ngay
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Dots Indicator -->
          <div class="carousel-dots">
            <button
              v-for="(dot, index) in totalDots"
              :key="index"
              class="dot"
              :class="{ active: index === currentSlide }"
              @click="goToSlide(index)"
            ></button>
          </div>
        </div>
      </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact">
      <div class="container">
        <div class="contact-content">
          <div class="contact-info">
            <h2>Liên Hệ Chúng Tôi</h2>
            <div class="contact-details">
              <div class="contact-item">
                <h3>Điện Thoại</h3>
                <p>{{ contactInfo.phone || '123456789' }}</p>
              </div>
              <div class="contact-item">
                <h3>Email</h3>
                <p>{{ contactInfo.email || '<EMAIL>' }}</p>
              </div>
              <div class="contact-item">
                <h3>Địa Chỉ</h3>
                <p>{{ contactInfo.address || 'Test Address Line 1' }}<br>{{ contactInfo.city || 'Quảng Bình' }}</p>
              </div>
              <div class="contact-item">
                <h3>Giờ Hoạt Động</h3>
                <p>{{ contactInfo.hours || '6:30 – 21:00' }}</p>
              </div>
            </div>
            <div class="social-links">
              <h3>Thêm Thông Tin Về Chúng Tôi</h3>
              <div class="social-buttons">
                <a v-if="settings.social_media.instagram" :href="settings.social_media.instagram" target="_blank" class="social-link instagram">Instagram</a>
                <a v-if="settings.social_media.facebook" :href="settings.social_media.facebook" target="_blank" class="social-link facebook">Facebook</a>
                <a v-if="settings.social_media.youtube" :href="settings.social_media.youtube" target="_blank" class="social-link youtube">YouTube</a>
              </div>
            </div>
          </div>

          <div id="booking-form" class="booking-form">
            <h3>Đặt Dịch Vụ</h3>
            <form @submit.prevent="handleBookingSubmit" :class="{ loading: bookingLoading }">
              <div class="form-group">
                <label for="service">Dịch Vụ</label>
                <select v-model="bookingForm.service" id="service" name="service" required>
                  <option value="">Chọn Dịch Vụ</option>
                  <option
                    v-for="service in activeServices"
                    :key="service.id"
                    :value="service.id"
                  >
                    {{ service.name }} - {{ formatPrice(service.price) }}
                  </option>
                </select>
              </div>

              <!-- Customer Information -->
              <div class="form-row">
                <div class="form-group">
                  <label for="customer_name">Họ Và Tên *</label>
                  <input
                    v-model="bookingForm.customer_name"
                    type="text"
                    id="customer_name"
                    name="customer_name"
                    required
                    placeholder="Enter your full name"
                  >
                </div>
                <div class="form-group">
                  <label for="customer_phone">Số Điện Thoại *</label>
                  <input
                    v-model="bookingForm.customer_phone"
                    type="tel"
                    id="customer_phone"
                    name="customer_phone"
                    required
                    placeholder="0123456789"
                  >
                </div>
              </div>

              <!-- Booking Date -->
              <div class="form-group">
                <label for="booking_date">Ngày Đặt Dịch Vụ *</label>
                <input
                  v-model="bookingForm.booking_date"
                  type="date"
                  id="booking_date"
                  name="booking_date"
                  required
                  :min="todayDate"
                >
              </div>

              <!-- Guest Information -->
              <div class="form-row">
                <div class="form-group">
                  <label for="adults">Người lớn *</label>
                  <input
                    v-model.number="bookingForm.adults"
                    type="number"
                    id="adults"
                    name="adults"
                    min="1"
                    required
                  >
                </div>
                <div class="form-group">
                  <label for="children_6_to_11">Trẻ em (6-11 tuổi)</label>
                  <input
                    v-model.number="bookingForm.children_6_to_11"
                    type="number"
                    id="children_6_to_11"
                    name="children_6_to_11"
                    min="0"
                  >
                </div>
              </div>

              <div class="form-group">
                <label for="children_under_6">Trẻ em (Dưới 6 tuổi)</label>
                <input
                  v-model.number="bookingForm.children_under_6"
                  type="number"
                  id="children_under_6"
                  name="children_under_6"
                  min="0"
                >
              </div>

              <div class="form-group">
                <label for="special_notes">Lưu ý cho chúng tôi</label>
                <textarea
                  v-model="bookingForm.special_notes"
                  id="special_notes"
                  name="special_notes"
                  rows="3"
                  placeholder="Yêu cầu đặc biệt hoặc ghi chú..."
                ></textarea>
              </div>

              <button type="submit" class="btn-book" :disabled="bookingLoading">
                {{ bookingLoading ? 'Processing...' : 'Book Now' }}
              </button>
            </form>
          </div>
        </div>
      </div>
    </section>

    <!-- Hidden Google Translate Element -->
    <div id="google_translate_element" style="display: none;"></div>

  </div>
</template>

<script>
import apiService from '../utils/api.js';
import { formatPrice, getServiceImage, showNotification } from '../utils/helpers.js';

export default {
  name: 'AboutApp',
  data() {
    return {
      // Hero data
      heroData: {
        image_url: null
      },

      // Services data
      services: [],
      servicesLoading: false,

      // Contact info
      contactInfo: {
        phone: '',
        email: '',
        address: '',
        city: '',
        hours: ''
      },

      // Settings data
      settings: {
        general: {
          site_title: ''
        },
        contact: {
          email: '',
          phone: '',
          address: ''
        },
        social_media: {
          instagram: '',
          facebook: '',
          youtube: ''
        },
        business: {
          opening_hours: '',
          google_maps_embed: ''
        }
      },

      // Booking form
      bookingForm: {
        service: '',
        customer_name: '',
        customer_phone: '',
        booking_date: '',
        adults: 2,
        children_6_to_11: 0,
        children_under_6: 0,
        special_notes: ''
      },
      bookingLoading: false,

      // About data from API
      aboutData: {
        title: '',
        subtitle: '',
        content: '',
        large_image: '',
        large_image2: '',
        small_image: ''
      },

      // Static images (fallback)
      aboutImages: {
        main: 'https://images.unsplash.com/photo-1504280390367-361c6d9f38f4?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
        nature: 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
      },

      // Today's date for date input minimum
      todayDate: new Date().toISOString().split('T')[0],

      // Carousel data
      currentSlide: 0,
      slidesPerView: 3, // Show 3 services at once
      slideWidth: 33.333, // 100% / 3 slides

      // Language switcher
      currentLanguage: 'vi',
      googleTranslateLoaded: false,

    };
  },

  computed: {
    featuredServices() {
      // Show first 3 active services
      return this.services.filter(service => service.is_active).slice(0, 3);
    },

    activeServices() {
      // All active services for booking dropdown
      return this.services.filter(service => service.is_active);
    },

    maxSlide() {
      // Maximum slide index
      return Math.max(0, this.services.length - this.slidesPerView);
    },

    totalDots() {
      // Total number of dots for pagination
      return Math.ceil(this.services.length / this.slidesPerView);
    }
  },
  
  async mounted() {
    await this.loadAboutData();
    await this.loadHeroImage();
    await this.loadServices();
    await this.loadContactInfo();
    await this.loadSettings();
    this.updateCarouselSettings();
    window.addEventListener('resize', this.updateCarouselSettings);
  },

  beforeUnmount() {
    window.removeEventListener('resize', this.updateCarouselSettings);
  },
  
  methods: {
    formatPrice,
    showNotification,

    handleCallClick() {
      if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
        window.location.href = 'tel:+84941214444';
      } else {
        window.location.href = '/contact.html';
      }
    },

    toggleMenu() {
      const navMenu = document.getElementById('navMenu');
      const hamburger = document.getElementById('hamburger');

      if (navMenu && hamburger) {
        navMenu.classList.toggle('active');
        hamburger.classList.toggle('active');
      }
    },

    async loadAboutData() {
      try {
        console.log('Loading about data for About page...');
        const response = await apiService.get('/content/homepage/about');
        const metadata = response.metadata ? JSON.parse(response.metadata) : {};

        this.aboutData = {
          title: response.title || 'About Phong Nha Valley',
          subtitle: response.subtitle || 'XIN CHÀO!',
          content: response.content || 'Phong Nha Valley is an experimental tourism project offering nature camping and glamping experiences at Trằm Mé – Chày Lập.',
          large_image: response.image_url || metadata.large_image || '/assets/images/about-valley-large.jpg',
          large_image2: metadata.large_image2 || '/assets/images/about-valley-large2.jpg',
          small_image: metadata.small_image || '/assets/images/about-valley-small.jpg'
        };

        console.log('About data loaded on About page:', this.aboutData);
      } catch (error) {
        console.error('Failed to load about data:', error);
        // Use fallback data
        this.aboutData = {
          title: 'About Phong Nha Valley',
          subtitle: 'XIN CHÀO!',
          content: 'Phong Nha Valley is an experimental tourism project offering nature camping and glamping experiences at Trằm Mé – Chày Lập.',
          large_image: '/assets/images/about-valley-large.jpg',
          large_image2: '/assets/images/about-valley-large2.jpg',
          small_image: '/assets/images/about-valley-small.jpg'
        };
      }
    },

    async loadHeroImage() {
      try {
        console.log('Loading hero image for About page...');
        const response = await apiService.get('/content/homepage/hero');

        console.log('Hero data received:', response);
        this.heroData = response;

        // Apply background image if available
        if (response.image_url && this.$refs.aboutHero) {
          this.applyHeroBackground(response.image_url);
        }
      } catch (error) {
        console.error('Failed to load hero image:', error);
      }
    },

    applyHeroBackground(imageUrl) {
      console.log('Applying hero background image:', imageUrl);
      const heroSection = this.$refs.aboutHero;

      if (heroSection) {
        // Create background style with overlay
        const backgroundStyle = `
          linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4)),
          url('${imageUrl}') center/cover no-repeat
        `;

        console.log('Setting background style:', backgroundStyle);
        heroSection.style.background = backgroundStyle;
      }
    },

    handleImageError(event) {
      // Suppress console spam
      const src = event.target.src;
      if (!window.loggedImageErrors) window.loggedImageErrors = new Set();
      if (!window.loggedImageErrors.has(src)) {
        console.warn('About image failed to load:', src);
        window.loggedImageErrors.add(src);
      }

      // Prevent infinite loop
      if (src.includes('placeholder') ||
          src.includes('data:image') ||
          event.target.dataset.errorHandled === 'true') {
        event.target.style.display = 'none';
        event.target.style.visibility = 'hidden';
        return;
      }

      // Mark as handled and use data URI
      event.target.dataset.errorHandled = 'true';
      event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmMGYwIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIG5vdCBhdmFpbGFibGU8L3RleHQ+PC9zdmc+';
    },

    getChildPrice(service) {
      // Calculate child price (50% of adult price)
      if (service.price) {
        return Math.floor(service.price * 0.5);
      }
      return null;
    },

    bookService(service) {
      // Set the selected service in booking form
      this.bookingForm.service = service.id;

      // Scroll to booking form
      this.$nextTick(() => {
        const bookingSection = document.getElementById('booking-form');
        if (bookingSection) {
          bookingSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      });
    },

    goToServicePage(service) {
      console.log('🎯 About page: navigating to service:', service.id);
      window.location.href = `/service.html?id=${service.id}`;
    },

    async loadServices() {
      this.servicesLoading = true;
      try {
        this.services = await apiService.getServices();
      } catch (error) {
        console.error('Failed to load services:', error);
        this.showNotification('Không thể tải dịch vụ', 'error');
      } finally {
        this.servicesLoading = false;
      }
    },

    async loadContactInfo() {
      try {
        const response = await apiService.get('/settings/');
        const settingsData = response;

        if (settingsData && settingsData.settings) {
          const settings = settingsData.settings;
          this.contactInfo = {
            phone: settings.contact.phone || '123456789',
            email: settings.contact.email || '<EMAIL>',
            address: settings.contact.address || 'Test Address Line 1',
            city: 'Quảng Bình', // Static value since not in API
            hours: settings.business.opening_hours || '6:30 – 21:00'
          };
        }
      } catch (error) {
        console.error('Failed to load contact info:', error);
        // Use default values if API fails
        this.contactInfo = {
          phone: '123456789',
          email: '<EMAIL>',
          address: 'Test Address Line 1',
          city: 'Quảng Bình',
          hours: '6:30 – 21:00'
        };
      }
    },
    
    getServiceImageStyle(service) {
      let imageUrl = null;

      if (service.images && service.images.length > 0) {
        imageUrl = service.images[0];
      } else if (service.image) {
        imageUrl = service.image;
      }

      if (imageUrl) {
        if (imageUrl.startsWith('http') || imageUrl.startsWith('/assets/')) {
          return {
            backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.3)), url('${imageUrl}')`
          };
        } else {
          return {
            backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.3)), url('/assets/uploads/${imageUrl}')`
          };
        }
      } else {
        const fallbackImage = getServiceImage(service.type);
        return {
          backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.3)), url('/assets/images/${fallbackImage}')`
        };
      }
    },
    
    async handleBookingSubmit() {
      // Validate required fields
      if (!this.bookingForm.service || !this.bookingForm.customer_name ||
          !this.bookingForm.customer_phone || !this.bookingForm.booking_date ||
          !this.bookingForm.adults) {
        this.showNotification('Please fill in all required fields', 'error');
        return;
      }

      this.bookingLoading = true;

      try {
        // Check availability first
        const availability = await apiService.checkAvailability(
          this.bookingForm.service,
          this.bookingForm.booking_date
        );

        if (availability && !availability.available) {
          this.showNotification('Selected service is not available for this date', 'error');
          return;
        }

        const bookingData = {
          service_id: this.bookingForm.service,
          customer_name: this.bookingForm.customer_name,
          customer_phone: this.bookingForm.customer_phone,
          booking_date: this.bookingForm.booking_date,
          adults: this.bookingForm.adults,
          children_6_to_11: this.bookingForm.children_6_to_11,
          children_under_6: this.bookingForm.children_under_6,
          special_notes: this.bookingForm.special_notes
        };

        // Add check-in/check-out dates for accommodation
        const selectedService = this.services.find(s => s.id === this.bookingForm.service);
        if (selectedService && selectedService.type === 'ACCOMMODATION') {
          bookingData.check_in_date = this.bookingForm.booking_date;
          // Add one day for check-out
          const checkOutDate = new Date(this.bookingForm.booking_date);
          checkOutDate.setDate(checkOutDate.getDate() + 1);
          bookingData.check_out_date = checkOutDate.toISOString().split('T')[0];
        }

        await apiService.createBooking(bookingData);
        this.showNotification('Booking created successfully!', 'success');
        this.resetBookingForm();
      } catch (error) {
        this.showNotification(error.message || 'Failed to create booking', 'error');
      } finally {
        this.bookingLoading = false;
      }
    },

    resetBookingForm() {
      this.bookingForm = {
        service: '',
        customer_name: '',
        customer_phone: '',
        booking_date: '',
        adults: 2,
        children_6_to_11: 0,
        children_under_6: 0,
        special_notes: ''
      };
    },

    // Carousel methods
    prevSlide() {
      if (this.currentSlide > 0) {
        this.currentSlide--;
      }
    },

    nextSlide() {
      if (this.currentSlide < this.maxSlide) {
        this.currentSlide++;
      }
    },

    goToSlide(index) {
      this.currentSlide = Math.min(index, this.maxSlide);
    },

    viewServiceDetails(service) {
      // Navigate to homepage services section with service highlighted
      window.location.href = `/#services?highlight=${service.id}`;
    },

    updateCarouselSettings() {
      const width = window.innerWidth;
      if (width < 768) {
        // Mobile: 1 slide
        this.slidesPerView = 1;
        this.slideWidth = 100;
      } else if (width < 1024) {
        // Tablet: 2 slides
        this.slidesPerView = 2;
        this.slideWidth = 50;
      } else {
        // Desktop: 3 slides
        this.slidesPerView = 3;
        this.slideWidth = 33.333;
      }

      // Reset slide if current position is invalid
      if (this.currentSlide > this.maxSlide) {
        this.currentSlide = this.maxSlide;
      }
    },

    // Language switcher methods
    async loadGoogleTranslate() {
      if (this.googleTranslateLoaded) return;

      return new Promise((resolve) => {
        // Add Google Translate script
        const script = document.createElement('script');
        script.src = 'https://translate.google.com/translate_a/element.js?cb=googleTranslateElementInit';
        script.async = true;
        document.head.appendChild(script);

        // Initialize Google Translate
        window.googleTranslateElementInit = () => {
          new window.google.translate.TranslateElement({
            pageLanguage: 'vi',
            includedLanguages: 'vi,en',
            layout: window.google.translate.TranslateElement.InlineLayout.SIMPLE,
            autoDisplay: false
          }, 'google_translate_element');

          this.googleTranslateLoaded = true;
          resolve();
        };
      });
    },

    async switchLanguage(lang) {
      this.currentLanguage = lang;

      if (!this.googleTranslateLoaded) {
        await this.loadGoogleTranslate();
      }

      // Wait a bit for Google Translate to initialize
      setTimeout(() => {
        if (lang === 'vi') {
          // Switch to Vietnamese (original)
          this.setGoogleTranslateLanguage('vi');
        } else if (lang === 'en') {
          // Switch to English
          this.setGoogleTranslateLanguage('en');
        }
      }, 500);
    },

    setGoogleTranslateLanguage(lang) {
      const selectElement = document.querySelector('.goog-te-combo');
      if (selectElement) {
        selectElement.value = lang;
        selectElement.dispatchEvent(new Event('change'));
      } else {
        // If select element not found, try alternative method
        const iframe = document.querySelector('.goog-te-menu-frame');
        if (iframe) {
          const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
          const langOption = iframeDoc.querySelector(`[value="${lang}"]`);
          if (langOption) {
            langOption.click();
          }
        }
      }
    },

    // Settings management
    async loadSettings() {
      try {
        const response = await fetch('/api/v1/settings/');

        if (response.ok) {
          const data = await response.json();
          this.settings = data.settings;
          console.log('✅ Settings loaded in About page:', this.settings);
        } else {
          console.error('❌ Failed to load settings:', response.status);
        }
      } catch (error) {
        console.error('❌ Error loading settings:', error);
      }
    }
  }
};
</script>

<style scoped>
/* Base styles */
#about-app {
  font-family: 'SVN-Alluring', serif;
  width: 100%;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Logo styling */
.logo-img {
  height: 100%;
  width: auto;
  max-width: 200px;
  object-fit: contain;
}

/* Hero Section */
.about-hero {
  /* Default fallback background - will be overridden by JavaScript */
  background: linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4)),
              url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80') center/cover;
  height: 60vh;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: white;
  margin-top: 0; /* Remove margin-top, use body padding instead */
}

.hero-content {
  max-width: 800px;
  padding: 0 20px;
}

.hero-title {
  font-family: 'SVN-Megante', serif;
  font-size: 3.5rem;
  font-weight: normal;
  margin-bottom: 20px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.hero-subtitle {
  font-family: 'SVN-Alluring', serif;
  font-size: 1.3rem;
  font-weight: normal;
  opacity: 0.9;
  margin: 0;
}

/* About Content Section */
.about-content {
  padding: 80px 0;
  background: white;
  width: 100%;
  overflow-x: hidden;
  display: flex;
  justify-content: center;
}

.about-content .container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  width: 100%;
}

/* Welcome Message */
.welcome-message {
  text-align: center;
  margin-bottom: 40px;
}

.welcome-message p {
  font-size: 1.3rem;
  font-style: italic;
  color: #3F9772;
  font-weight: 500;
  margin: 0;
  padding: 20px;
  font-family: 'svn-alluring', serif;
}

/* Closing Message */
.closing-message {
  text-align: center;
  margin-top: 60px;
  margin-bottom: 20px;
}

.closing-message p {
  font-size: 1.2rem;
  font-style: italic;
  color: #3F9772;
  font-weight: 400;
  margin: 0;
  padding: 20px;
  font-family: 'svn-alluring', serif;
  line-height: 1.8;
  max-width: 800px;
  margin: 0 auto;
  opacity: 0.8;
}

.about-main-title {
  text-align: center;
  margin-bottom: 60px;
  width: 100%;
}

.about-main-title h1 {
  font-family: 'SVN-Megante', serif;
  font-size: 2.5rem;
  font-weight: normal;
  color: #2d3748;
  letter-spacing: 2px;
  margin: 0;
}

.about-section {
  margin-bottom: 80px;
  width: 100%;
}

.about-section:last-child {
  margin-bottom: 0;
}

/* First Row Section */
.first-row-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
  max-width: 100%;
  margin-bottom: 60px;
}

.left-content {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.history-content h2 {
  font-family: 'SVN-Megante', serif;
  font-size: 1.8rem;
  color: #2d3748;
  margin-bottom: 20px;
  font-weight: normal;
}

.history-content p {
  font-family: 'Poppins', sans-serif;
  color: #4a5568;
  line-height: 1.8;
  margin-bottom: 15px;
  text-align: justify;
}

.about-text-content h2 {
  font-family: 'SVN-Megante', serif;
  font-size: 1.8rem;
  font-weight: normal;
  color: #2d3748;
  margin-bottom: 20px;
}

.about-text-content p {
  font-family: 'Poppins', sans-serif;
  font-size: 1rem;
  line-height: 1.7;
  color: #4a5568;
  margin-bottom: 15px;
  text-align: justify;
}

/* Mission and Vision Content */
.mission-vision-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
}

/* Second Row Section */
.second-row-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;
  max-width: 100%;
}

.mission-content h3,
.vision-content h3 {
  font-family: 'SVN-Megante', serif;
  font-size: 1.4rem;
  font-weight: normal;
  color: #2d3748;
  margin-bottom: 15px;
}

.mission-content p,
.vision-content p {
  font-family: 'Poppins', sans-serif;
  font-size: 1rem;
  line-height: 1.7;
  color: #4a5568;
  text-align: justify;
}

/* Values Content */
.values-content h2 {
  font-family: 'SVN-Megante', serif;
  font-size: 1.8rem;
  color: #2d3748;
  margin-bottom: 30px;
  font-weight: normal;
}

.value-item {
  margin-bottom: 25px;
}

.value-item:last-child {
  margin-bottom: 0;
}

.value-item h4 {
  font-family: 'Poppins', sans-serif;
  font-size: 1.1rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 8px;
}

.value-item p {
  font-family: 'Poppins', sans-serif;
  font-size: 0.95rem;
  line-height: 1.6;
  color: #4a5568;
  text-align: justify;
}

/* Image Container */
.about-image-container {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f7f7f7;
  border-radius: 12px;
  min-height: 300px;
  width: 100%;
  overflow: hidden;
}

.about-large-image {
  width: 100%;
  height: 100%;
  min-height: 300px;
  object-fit: cover;
  border-radius: 12px;
  transition: transform 0.3s ease;
}

.about-large-image:hover {
  transform: scale(1.02);
}

.image-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

/* Image Placeholder */
.about-image-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f7f7f7;
  border-radius: 12px;
  min-height: 300px;
  width: 100%;
}

.image-box {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 200px;
  height: 150px;
  border: 2px solid #ddd;
  border-radius: 8px;
  background: white;
  color: #666;
  font-size: 1.2rem;
  font-weight: 500;
}

/* Services Section */
.services-section {
  padding: 80px 0;
  background: #f8f9fa;
}

.section-title {
  font-family: 'SVN-Megante', serif;
  text-align: center;
  font-size: 2.5rem;
  color: #2d3748;
  margin-bottom: 60px;
  font-weight: 600;
  letter-spacing: 2px;
}

.services-carousel {
  position: relative;
  max-width: 1200px;
  margin: 0 auto;
}

.carousel-nav {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(196, 169, 98, 0.9);
  border: none;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
}

.carousel-nav:hover:not(:disabled) {
  background: #C4A962;
  transform: translateY(-50%) scale(1.1);
}

.carousel-nav:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.carousel-nav.prev {
  left: -25px;
}

.carousel-nav.next {
  right: -25px;
}

.services-container {
  overflow: hidden;
  border-radius: 12px;
}

.services-track {
  display: flex;
  transition: transform 0.5s ease;
}

.service-slide {
  flex: 0 0 33.333%;
  padding: 0 15px;
}

.service-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: auto;
  display: flex;
  flex-direction: column;
}

.service-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.service-image {
  height: 200px;
  background-size: cover;
  background-position: center;
  position: relative;
  border-radius: 12px 12px 0 0;
}

.service-status-badge {
  font-family: 'Poppins', sans-serif;
  position: absolute;
  top: 15px;
  right: 15px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.service-status-badge.available {
  background: #10B981;
  color: white;
}

.service-status-badge.unavailable {
  background: #EF4444;
  color: white;
}

.service-content {
  font-family: 'Poppins', sans-serif;
  padding: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.service-category {
  font-size: 0.75rem;
  color: #3F9772;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 8px;
}

.service-title {
  font-size: 1.3rem;
  color: #2d3748;
  font-weight: 600;
  margin-bottom: 10px;
  line-height: 1.3;
}

.service-description {
  font-family: 'Poppins', sans-serif;
  color: #718096;
  font-size: 0.9rem;
  line-height: 1.5;
  margin-bottom: 15px;
  flex: 1;
}

.service-pricing {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.main-price {
  font-size: 1.2rem;
  color: #3F9772;
  font-weight: 600;
}

.child-price {
  font-size: 0.9rem;
  color: #718096;
}

.service-time {
  font-size: 0.85rem;
  color: #718096;
  margin-bottom: 20px;
  padding: 8px 0;
  border-top: 1px solid #E2E8F0;
  border-bottom: 1px solid #E2E8F0;
}

.btn-book-now {
  background: #3F9772;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 0.9rem;
}

.btn-book-now:hover {
  background: #1d485d;
  transform: translateY(-2px);
}

/* Carousel Dots */
.carousel-dots {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 30px;
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: none;
  background: #ddd;
  cursor: pointer;
  transition: all 0.3s ease;
}

.dot.active {
  background: #C4A962;
  transform: scale(1.2);
}

.dot:hover {
  background: #C4A962;
}

/* Contact Section */
.contact {
  padding: 100px 0;
  background: var(--dark-blue);
  color: var(--white);
}

.contact-content {
  font-family: 'Poppins', sans-serif;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
}

.contact-info h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 40px;
  color: var(--accent-gold);
}

.contact-details {
  margin-bottom: 40px;
}

.contact-item {
  margin-bottom: 30px;
}

.contact-item h3 {
  color: var(--accent-gold);
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 10px;
}

.contact-item p {
  font-family: 'Poppins', sans-serif;
  color: #ccc;
  line-height: 1.6;
}

.social-links {
  margin-top: 30px;
}

.social-links h3 {
  color: var(--accent-gold);
  font-size: 1.2rem;
  margin-bottom: 15px;
}

.social-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.social-link {
  color: var(--accent-gold);
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.social-link:hover {
  color: var(--white);
}

/* Social media specific colors */
.social-link.instagram {
  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
  color: white;
  padding: 12px 20px;
  border-radius: 25px;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: transform 0.3s ease;
}

.social-link.facebook {
  background: #1877f2;
  color: white;
  padding: 12px 20px;
  border-radius: 25px;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: transform 0.3s ease;
}

.social-link.youtube {
  background: #ff0000;
  color: white;
  padding: 12px 20px;
  border-radius: 25px;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: transform 0.3s ease;
}

.social-link.zalo {
  background: #0068ff;
  color: white;
  padding: 12px 20px;
  border-radius: 25px;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: transform 0.3s ease;
}

.social-link.instagram:hover,
.social-link.facebook:hover,
.social-link.youtube:hover,
.social-link.zalo:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

/* Booking Form */
.booking-form {
  background: rgba(255, 255, 255, 0.1);
  padding: 40px;
  border-radius: 15px;
  backdrop-filter: blur(10px);
}

.booking-form h3 {
  color: var(--accent-gold);
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 30px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  font-family: 'Poppins', sans-serif;
  display: block;
  color: #C4A962;
  font-weight: 600;
  margin-bottom: 8px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.form-group input,
.form-group select,
.form-group textarea {
  font-family: 'Poppins', sans-serif;
  width: 100%;
  padding: 12px 15px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #C4A962;
  background: rgba(255, 255, 255, 0.15);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

/* Select dropdown styling */
.form-group select {
  background-color: rgba(255, 255, 255, 0.9);
  color: #2d2d2d;
  font-weight: 500;
}

.form-group select:focus {
  background-color: rgba(255, 255, 255, 0.95);
  color: #2d2d2d;
}

.form-group select option {
  background-color: #fff;
  color: #2d2d2d;
  padding: 8px 12px;
  font-weight: 500;
}

.form-group select option:hover,
.form-group select option:focus {
  background-color: #C4A962;
  color: #fff;
}

.form-group select option:checked {
  background-color: #C4A962;
  color: #fff;
}



.btn-book {
  width: 100%;
  background: #C4A962;
  color: #fff;
  padding: 12px;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-book:hover {
  background: #B8A055;
}

.btn-book:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.loading-state {
  text-align: center;
  padding: 60px 20px;
  color: #718096;
  font-family: 'Poppins', sans-serif;
}

/* Responsive Design */
@media (max-width: 1024px) {


  .first-row-section {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .second-row-section {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .mission-vision-content {
    grid-template-columns: 1fr;
    gap: 25px;
  }
}

@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.1rem;
  }

  .welcome-message p {
    font-size: 1.1rem;
    padding: 15px;
  }

  .closing-message p {
    font-size: 1.0rem;
    padding: 15px;
    line-height: 1.6;
  }



  .about-main-title h1 {
    font-size: 2rem;
  }

  .first-row-section {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .second-row-section {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .mission-vision-content {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .left-content {
    gap: 30px;
  }

  .about-text-content h2 {
    font-size: 1.5rem;
  }

  .mission-content h3,
  .vision-content h3 {
    font-size: 1.2rem;
  }

  .about-image-container,
  .about-image-placeholder {
    min-height: 200px;
  }

  .about-large-image {
    min-height: 200px;
  }

  .image-box {
    width: 150px;
    height: 100px;
    font-size: 1rem;
  }

  .contact-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .section-header h2 {
    font-size: 2rem;
  }

  .contact-info h2 {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .about-hero {
    height: 50vh;
    margin-top: 70px;
  }

  .hero-title {
    font-size: 2rem;
  }

  .about-content,
  .services-section,
  .contact {
    padding: 60px 0;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .booking-form {
    padding: 30px 20px;
  }

  /* Carousel responsive */
  .service-slide {
    flex: 0 0 100%;
  }

  .carousel-nav {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .carousel-nav.prev {
    left: -20px;
  }

  .carousel-nav.next {
    right: -20px;
  }

  .section-title {
    font-size: 2rem;
  }

  .service-image {
    height: 180px;
  }

  .service-content {
    padding: 15px;
  }

  .service-title {
    font-size: 1.1rem;
  }

  .main-price {
    font-size: 1.1rem;
  }
}

/* Tablet styles */
@media (min-width: 768px) and (max-width: 1023px) {
  .service-slide {
    flex: 0 0 50%;
  }

  .service-image {
    height: 190px;
  }
}

.about-text h2 {
  font-size: 1.8rem;
}

.about-main-image {
  height: 250px;
}

/* Language Switcher */
.language-switcher {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 20px;
}

.lang-btn {
  background: none;
  border: 2px solid transparent;
  border-radius: 6px;
  padding: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.lang-btn:hover {
  border-color: var(--primary-green);
  background: rgba(63, 151, 114, 0.1);
}

.lang-btn.active {
  border-color: var(--primary-green);
  background: rgba(63, 151, 114, 0.15);
}

.flag-emoji {
  font-size: 16px;
  line-height: 1;
}

.lang-text {
  font-size: 11px;
  font-weight: 600;
  color: var(--dark-blue);
  font-family: 'Poppins', sans-serif;
}

/* Hide Google Translate element */
#google_translate_element {
  display: none !important;
}

.goog-te-banner-frame {
  display: none !important;
}

.goog-te-menu-frame {
  display: none !important;
}

body {
  top: 0 !important;
}

/* Mobile responsive for language switcher */
@media (max-width: 768px) {
  .language-switcher {
    margin-left: 10px;
    gap: 6px;
  }

  .flag-icon {
    width: 20px;
    height: 15px;
  }
}

</style>
