<template>
  <div id="admin-app" class="admin-layout">
    <!-- Loading Screen -->
    <div v-if="isInitializing" class="admin-loading">
      <div class="loading-container">
        <div class="loading-spinner"></div>
        <p>Checking authentication...</p>
      </div>
    </div>

    <!-- Login Screen -->
    <div v-else-if="!isAuthenticated" class="admin-login">
      <div class="login-container">
        <div class="login-header">
          <h1>Admin Login</h1>
          <p>Phong Nha Valley Admin Panel</p>
        </div>

        <form @submit.prevent="handleLogin" class="login-form">
          <div class="form-group">
            <label for="email">Email</label>
            <input
              type="email"
              id="email"
              v-model="loginForm.email"
              required
              placeholder="<EMAIL>"
              :disabled="loginLoading"
            />
          </div>

          <div class="form-group">
            <label for="password">Password</label>
            <input
              type="password"
              id="password"
              v-model="loginForm.password"
              required
              placeholder="Enter your password"
              :disabled="loginLoading"
            />
          </div>

          <button type="submit" class="btn-login" :disabled="loginLoading">
            {{ loginLoading ? 'Logging in...' : 'Login' }}
          </button>

          <div v-if="loginError" class="error-message">
            {{ loginError }}
          </div>
        </form>
      </div>
    </div>

    <!-- Change Password Modal -->
    <div v-if="showChangePasswordModal" class="admin-change-password-overlay" @click="closeChangePasswordModal">
      <div class="admin-change-password-modal" @click.stop>
        <!-- Header -->
        <div class="admin-change-password-header">
          <div class="admin-header-content">
            <div class="admin-header-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 1L3 5V11C3 16.55 6.84 21.74 12 23C17.16 21.74 21 16.55 21 11V5L12 1Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M9 12L11 14L15 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <div class="admin-header-text">
              <h3>Change Password</h3>
              <p>Update your admin account security</p>
            </div>
          </div>
          <button class="admin-close-button" @click="closeChangePasswordModal">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </button>
        </div>

        <!-- Content -->
        <div class="admin-change-password-content">
          <form @submit.prevent="handleChangePassword" class="admin-password-form">
            <!-- Current Password -->
            <div class="admin-form-field">
              <label for="current_password" class="admin-field-label">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <rect x="3" y="11" width="18" height="11" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                  <circle cx="12" cy="16" r="1" fill="currentColor"/>
                  <path d="M7 11V7a5 5 0 0 1 10 0v4" stroke="currentColor" stroke-width="2"/>
                </svg>
                Current Password
              </label>
              <input
                type="password"
                id="current_password"
                v-model="changePasswordForm.currentPassword"
                required
                placeholder="Enter your current password"
                :disabled="changePasswordLoading"
                class="admin-field-input"
              />
            </div>

            <!-- New Password -->
            <div class="admin-form-field">
              <label for="new_password" class="admin-field-label">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <rect x="3" y="11" width="18" height="11" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                  <circle cx="12" cy="16" r="1" fill="currentColor"/>
                  <path d="M7 11V7a5 5 0 0 1 10 0v4" stroke="currentColor" stroke-width="2"/>
                </svg>
                New Password
              </label>
              <input
                type="password"
                id="new_password"
                v-model="changePasswordForm.newPassword"
                required
                placeholder="Enter new password (minimum 6 characters)"
                minlength="6"
                :disabled="changePasswordLoading"
                class="admin-field-input"
              />
              <div class="admin-password-hint">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                  <path d="M12 6v6l4 2" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                Password must be at least 6 characters long
              </div>
            </div>

            <!-- Confirm Password -->
            <div class="admin-form-field">
              <label for="confirm_password" class="admin-field-label">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M9 12L11 14L15 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <rect x="3" y="11" width="18" height="11" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                  <path d="M7 11V7a5 5 0 0 1 10 0v4" stroke="currentColor" stroke-width="2"/>
                </svg>
                Confirm New Password
              </label>
              <input
                type="password"
                id="confirm_password"
                v-model="changePasswordForm.confirmPassword"
                required
                placeholder="Confirm your new password"
                :disabled="changePasswordLoading"
                class="admin-field-input"
              />
            </div>

            <!-- Error Message -->
            <div v-if="changePasswordError" class="admin-error-alert">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                <line x1="15" y1="9" x2="9" y2="15" stroke="currentColor" stroke-width="2"/>
                <line x1="9" y1="9" x2="15" y2="15" stroke="currentColor" stroke-width="2"/>
              </svg>
              {{ changePasswordError }}
            </div>

            <!-- Actions -->
            <div class="admin-form-actions">
              <button
                type="button"
                @click="closeChangePasswordModal"
                :disabled="changePasswordLoading"
                class="admin-btn-secondary"
              >
                Cancel
              </button>
              <button
                type="submit"
                :disabled="changePasswordLoading"
                class="admin-btn-primary-action"
              >
                <svg v-if="changePasswordLoading" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="admin-loading-icon">
                  <path d="M21 12a9 9 0 11-6.219-8.56" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                </svg>
                {{ changePasswordLoading ? 'Changing Password...' : 'Change Password' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- User Management Modal -->
    <div v-if="showUserModal" class="admin-change-password-overlay" @click="closeUserModal">
      <div class="admin-change-password-modal" @click.stop>
        <!-- Header -->
        <div class="admin-change-password-header">
          <div class="admin-header-content">
            <div class="admin-header-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <circle cx="9" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                <line v-if="!editingUser" x1="19" y1="8" x2="19" y2="14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <line v-if="!editingUser" x1="22" y1="11" x2="16" y2="11" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <div class="admin-header-text">
              <h3>{{ editingUser ? 'Edit User' : 'Create New User' }}</h3>
              <p>{{ editingUser ? 'Update user information and permissions' : 'Add a new user to the system' }}</p>
            </div>
          </div>
          <button class="admin-close-button" @click="closeUserModal">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </button>
        </div>

        <!-- Content -->
        <div class="admin-change-password-content">
          <form @submit.prevent="handleUserSubmit" class="admin-password-form">
            <!-- Email -->
            <div class="admin-form-field">
              <label for="user_email" class="admin-field-label">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z" stroke="currentColor" stroke-width="2"/>
                  <polyline points="22,6 12,13 2,6" stroke="currentColor" stroke-width="2"/>
                </svg>
                Email Address
              </label>
              <input
                type="email"
                id="user_email"
                v-model="userForm.email"
                required
                placeholder="Enter email address"
                :disabled="userFormLoading"
                class="admin-field-input"
              />
            </div>

            <!-- Password (only for create or when changing) -->
            <div v-if="!editingUser || userForm.changePassword" class="admin-form-field">
              <label for="user_password" class="admin-field-label">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <rect x="3" y="11" width="18" height="11" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                  <circle cx="12" cy="16" r="1" fill="currentColor"/>
                  <path d="M7 11V7a5 5 0 0 1 10 0v4" stroke="currentColor" stroke-width="2"/>
                </svg>
                {{ editingUser ? 'New Password' : 'Password' }}
              </label>
              <input
                type="password"
                id="user_password"
                v-model="userForm.password"
                :required="!editingUser"
                placeholder="Enter password (minimum 6 characters)"
                minlength="6"
                :disabled="userFormLoading"
                class="admin-field-input"
              />
            </div>

            <!-- Change Password Toggle (only for edit) -->
            <div v-if="editingUser" class="admin-form-field">
              <label class="checkbox-label">
                <input
                  type="checkbox"
                  v-model="userForm.changePassword"
                  :disabled="userFormLoading"
                />
                Change Password
              </label>
            </div>

            <!-- First Name -->
            <div class="admin-form-field">
              <label for="user_first_name" class="admin-field-label">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                </svg>
                First Name
              </label>
              <input
                type="text"
                id="user_first_name"
                v-model="userForm.first_name"
                placeholder="Enter first name"
                :disabled="userFormLoading"
                class="admin-field-input"
              />
            </div>

            <!-- Last Name -->
            <div class="admin-form-field">
              <label for="user_last_name" class="admin-field-label">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                </svg>
                Last Name
              </label>
              <input
                type="text"
                id="user_last_name"
                v-model="userForm.last_name"
                placeholder="Enter last name"
                :disabled="userFormLoading"
                class="admin-field-input"
              />
            </div>

            <!-- Phone -->
            <div class="admin-form-field">
              <label for="user_phone" class="admin-field-label">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z" stroke="currentColor" stroke-width="2"/>
                </svg>
                Phone Number
              </label>
              <input
                type="tel"
                id="user_phone"
                v-model="userForm.phone"
                placeholder="Enter phone number"
                :disabled="userFormLoading"
                class="admin-field-input"
              />
            </div>

            <!-- Role -->
            <div class="admin-form-field">
              <label for="user_role" class="admin-field-label">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <path d="M21 12c.552 0 1-.448 1-1V8c0-.552-.448-1-1-1h-3V4c0-.552-.448-1-1-1H7c-.552 0-1 .448-1 1v3H3c-.552 0-1 .448-1 1v3c0 .552.448 1 1 1h3v3c0 .552.448 1 1 1h10c.552 0 1-.448 1-1v-3h3z" stroke="currentColor" stroke-width="2"/>
                </svg>
                Role
              </label>
              <select
                id="user_role"
                v-model="userForm.role"
                required
                :disabled="userFormLoading"
                class="admin-field-input"
              >
                <option value="">Select role</option>
                <option value="user">User</option>
                <option value="booking_manager">Booking Manager</option>
                <option value="admin">Admin</option>
              </select>
            </div>

            <!-- Error Message -->
            <div v-if="userFormError" class="admin-error-alert">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                <line x1="15" y1="9" x2="9" y2="15" stroke="currentColor" stroke-width="2"/>
                <line x1="9" y1="9" x2="15" y2="15" stroke="currentColor" stroke-width="2"/>
              </svg>
              {{ userFormError }}
            </div>

            <!-- Actions -->
            <div class="admin-form-actions">
              <button
                type="button"
                @click="closeUserModal"
                :disabled="userFormLoading"
                class="admin-btn-secondary"
              >
                Cancel
              </button>
              <button
                type="submit"
                :disabled="userFormLoading"
                class="admin-btn-primary-action"
              >
                <svg v-if="userFormLoading" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="admin-loading-icon">
                  <path d="M21 12a9 9 0 11-6.219-8.56" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                </svg>
                {{ userFormLoading ? (editingUser ? 'Updating...' : 'Creating...') : (editingUser ? 'Update User' : 'Create User') }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- Admin Panel (only shown when authenticated) -->
    <div v-else-if="isAuthenticated" class="admin-authenticated">
      <!-- Sidebar -->
      <aside class="sidebar">
      <div class="sidebar-header">
        <h1>Admin Panel</h1>
        <p>Phong Nha Valley</p>
      </div>
      <nav class="sidebar-nav">
        <ul>
          <li>
            <a 
              href="#" 
              :class="{ active: activeSection === 'dashboard' }" 
              @click.prevent="setActiveSection('dashboard')"
            >
              Dashboard
            </a>
          </li>
          <li>
            <a 
              href="#" 
              :class="{ active: activeSection === 'services' }" 
              @click.prevent="setActiveSection('services')"
            >
              Services
            </a>
          </li>
          <li>
            <a
              href="#"
              :class="{ active: activeSection === 'gallery' }"
              @click.prevent="setActiveSection('gallery')"
            >
              Gallery
            </a>
          </li>
          <li>
            <a
              href="#"
              :class="{ active: activeSection === 'menus' }"
              @click.prevent="setActiveSection('menus')"
            >
              Menu Management
            </a>
          </li>
          <li>
            <a
              href="#"
              :class="{ active: activeSection === 'homepage' }"
              @click.prevent="setActiveSection('homepage')"
            >
              Homepage
            </a>
          </li>
          <li>
            <a
              href="#"
              :class="{ active: activeSection === 'about' }"
              @click.prevent="setActiveSection('about')"
            >
              About Us
            </a>
          </li>
          <li>
            <a
              href="#"
              :class="{ active: activeSection === 'bookings' }"
              @click.prevent="setActiveSection('bookings')"
            >
              Bookings
            </a>
          </li>
          <li>
            <a 
              href="#" 
              :class="{ active: activeSection === 'users' }" 
              @click.prevent="setActiveSection('users')"
            >
              Users
            </a>
          </li>
          <li>
            <a 
              href="#" 
              :class="{ active: activeSection === 'settings' }" 
              @click.prevent="setActiveSection('settings')"
            >
              Settings
            </a>
          </li>
        </ul>
      </nav>
      <div class="sidebar-footer">
        <button @click="showChangePasswordModal = true" class="btn-change-password-sidebar">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect x="3" y="11" width="18" height="11" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
            <circle cx="12" cy="16" r="1" fill="currentColor"/>
            <path d="M7 11V7a5 5 0 0 1 10 0v4" stroke="currentColor" stroke-width="2"/>
          </svg>
          Change Password
        </button>
        <button @click="logout" class="btn-logout">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <polyline points="16,17 21,12 16,7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <line x1="21" y1="12" x2="9" y2="12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
          Logout
        </button>
      </div>
    </aside>

    <!-- Main Content -->
    <main class="main-content">
      <!-- Header -->
      <header class="content-header">
        <div class="header-title">
          <h2>{{ getSectionTitle() }}</h2>
        </div>
        <div class="header-actions">
          <span class="admin-name">{{ currentUser?.first_name || 'Admin' }}</span>
        </div>
      </header>

      <!-- Dashboard Section -->
      <section v-if="activeSection === 'dashboard'" class="content-section">
        <div class="dashboard-welcome">
          <h3>Welcome to Admin Dashboard</h3>
          <p>Manage your Phong Nha Valley services, bookings, and content from here.</p>
        </div>

        <div class="dashboard-stats">
          <div class="stat-card">
            <h3>Total Bookings</h3>
            <p class="stat-number">{{ dashboardStats.totalBookings }}</p>
            <p class="stat-label">Last 30 days</p>
          </div>
          <div class="stat-card">
            <h3>Active Services</h3>
            <p class="stat-number">{{ dashboardStats.activeServices }}</p>
            <p class="stat-label">Currently available</p>
          </div>
          <div class="stat-card">
            <h3>Gallery Images</h3>
            <p class="stat-number">{{ dashboardStats.galleryImages }}</p>
            <p class="stat-label">Total uploaded</p>
          </div>
          <div class="stat-card">
            <h3>Registered Users</h3>
            <p class="stat-number">{{ dashboardStats.registeredUsers }}</p>
            <p class="stat-label">Total users</p>
          </div>
        </div>
      </section>

      <!-- Services Section -->
      <section v-if="activeSection === 'services'" class="content-section">
        <div class="section-header">
          <h3>Manage Services</h3>
          <button class="btn-primary" @click="openServiceModal()">Add New Service</button>
        </div>
        <div v-if="servicesLoading" class="loading-state">
          <p>Loading services...</p>
        </div>
        <div v-else-if="servicesError" class="error-state">
          <p>{{ servicesError }}</p>
          <button @click="loadServices" class="btn-retry">Retry</button>
        </div>
        <div v-else-if="services.length === 0" class="empty-state">
          <p>No services found. Create your first service!</p>
          <button class="btn-primary" @click="openServiceModal()">Add First Service</button>
        </div>
        <div v-else class="services-grid">
          <div
            v-for="service in services"
            :key="service.id"
            class="service-admin-card"
          >
            <div class="service-admin-header">
              <h4>{{ service.name }}</h4>
              <span class="service-type-badge">{{ formatServiceType(service.type) }}</span>
            </div>
            <div class="service-admin-body">
              <p class="service-description-text">{{ service.description }}</p>
              <div class="service-price-info">
                <div class="price-item">
                  <span>Adult Price</span>
                  <strong>{{ formatPrice(service.price) }}</strong>
                </div>
                <div v-if="service.child_price" class="price-item">
                  <span>Child Price</span>
                  <strong>{{ formatPrice(service.child_price) }}</strong>
                </div>
              </div>
              <div class="service-status">
                <div 
                  class="status-indicator" 
                  :class="service.is_active ? 'active' : 'inactive'"
                ></div>
                <span>{{ service.is_active ? 'Active' : 'Inactive' }}</span>
              </div>
              <div class="service-actions">
                <button class="btn-edit" @click="editService(service.id)">Edit</button>
                <button class="btn-delete" @click="deleteService(service.id)">Delete</button>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Gallery Section -->
      <section v-if="activeSection === 'gallery'" class="content-section">
        <div class="section-header">
          <h3>Manage Gallery</h3>
          <div class="header-actions">
            <button class="btn-primary" @click="openGalleryEditModal()">Add New Image</button>
            <button class="btn-secondary" @click="openUploadModal()">Upload Images</button>
            <button class="btn-danger" @click="cleanupOrphanedImages()" :disabled="uploadLoading">
              {{ uploadLoading ? 'Cleaning...' : 'Cleanup Orphaned Images' }}
            </button>
          </div>
        </div>


        <div v-if="galleryImages.length === 0" class="empty-state">
          <p>No gallery images found. Add your first image!</p>
          <button class="btn-primary" @click="openGalleryEditModal()">Add First Image</button>
        </div>
        <div v-else class="gallery-grid">
          <div
            v-for="image in paginatedGalleryImages"
            :key="image.id"
            class="gallery-admin-item"
          >
            <img :src="image.image_url" :alt="image.alt_text" @error="handleImageError">
            <div class="gallery-overlay">
              <div class="gallery-info">
                <h4>{{ image.title }}</h4>
                <p>{{ image.description }}</p>
                <span class="status" :class="{ active: image.is_active }">
                  {{ image.is_active ? 'Active' : 'Inactive' }}
                </span>
              </div>
              <div class="gallery-actions">
                <button class="btn-edit" @click="editGalleryImage(image.id)">Edit</button>
                <button class="btn-delete" @click="deleteGalleryImage(image.id)" style="background: red; color: white; padding: 8px 12px; border: none; border-radius: 4px; cursor: pointer;">🗑️ Delete</button>
              </div>
            </div>
          </div>
        </div>

        <!-- Gallery Pagination -->
        <div v-if="galleryPagination.total_pages > 1" class="pagination-container">
          <div class="pagination-info">
            <span>Showing {{ ((galleryPagination.current_page - 1) * galleryPagination.per_page) + 1 }}-{{ Math.min(galleryPagination.current_page * galleryPagination.per_page, galleryPagination.total_count) }} of {{ galleryPagination.total_count }} items</span>
          </div>
          <div class="pagination-controls">
            <button
              class="pagination-btn"
              :disabled="!galleryPagination.has_previous"
              @click="prevGalleryPage"
            >
              ← Previous
            </button>

            <button
              v-for="page in galleryPageNumbers"
              :key="page"
              class="pagination-btn"
              :class="{ active: page === galleryCurrentPage }"
              @click="goToGalleryPage(page)"
            >
              {{ page }}
            </button>

            <button
              class="pagination-btn"
              :disabled="!galleryPagination.has_next"
              @click="nextGalleryPage"
            >
              Next →
            </button>
          </div>
        </div>
      </section>

      <!-- Menu Management Section -->
      <section v-if="activeSection === 'menus'" class="content-section">
        <div class="section-header">
          <h3>Menu Management</h3>
          <button class="btn-primary" @click="openMenuModal()">Add New Menu Item</button>
        </div>

        <!-- Category Filter -->
        <div class="menu-categories">
          <button
            class="category-btn"
            :class="{ active: currentMenuCategory === 'all' }"
            @click="filterMenusByCategory('all')"
          >
            All Categories
          </button>
          <button
            class="category-btn"
            :class="{ active: currentMenuCategory === 'restaurant' }"
            @click="filterMenusByCategory('restaurant')"
          >
            Restaurant
          </button>
          <button
            class="category-btn"
            :class="{ active: currentMenuCategory === 'accommodation' }"
            @click="filterMenusByCategory('accommodation')"
          >
            Accommodation
          </button>
          <button
            class="category-btn"
            :class="{ active: currentMenuCategory === 'afternoon_tea' }"
            @click="filterMenusByCategory('afternoon_tea')"
          >
            Afternoon Tea
          </button>
        </div>

        <div v-if="menusLoading" class="loading-state">
          <p>Loading menus...</p>
        </div>
        <div v-else-if="menusError" class="error-state">
          <p>{{ menusError }}</p>
          <button class="btn-primary" @click="loadMenus">Retry</button>
        </div>
        <div v-else-if="filteredMenus.length === 0" class="empty-state">
          <p>No menu items found. Add your first menu item!</p>
          <button class="btn-primary" @click="openMenuModal()">Add First Menu Item</button>
        </div>
        <div v-else class="menu-grid">
          <div
            v-for="menu in filteredMenus"
            :key="menu.id"
            class="menu-card"
          >
            <div class="menu-card-header">
              <div>
                <h4>{{ menu.title }}</h4>
                <div class="category-badge">{{ getCategoryDisplayName(menu.category) }}</div>
              </div>
              <span class="status-badge" :class="{ 'status-available': menu.is_active, 'status-unavailable': !menu.is_active }">
                {{ menu.is_active ? 'Active' : 'Inactive' }}
              </span>
            </div>

            <div v-if="menu.description" class="description">{{ menu.description }}</div>

            <div v-if="menu.image_url" class="menu-images">
              <img
                :src="menu.image_url"
                :alt="menu.alt_text || menu.title"
                class="menu-image-thumb"
                @error="handleImageError"
              >
            </div>
            <div v-else class="no-images">No image</div>

            <div class="menu-meta">
              <small>Category: {{ getCategoryDisplayName(menu.category) }} | Order: {{ menu.display_order }}</small>
            </div>

            <div class="menu-actions">
              <button class="btn-edit" @click="editMenu(menu.id)">Edit</button>
              <button class="btn-delete" @click="deleteMenu(menu.id, menu.title)">Delete</button>
            </div>
          </div>
        </div>
      </section>

      <!-- Homepage Content Section -->
      <section v-if="activeSection === 'homepage'" class="content-section">
        <div class="section-header">
          <h3>Homepage Content Management</h3>
          <p>Manage hero section background image and content</p>
        </div>

        <div class="homepage-content-form">
          <div class="form-card">
            <h4>Hero Section</h4>

            <div class="form-group">
              <label for="hero-title">Title</label>
              <input
                type="text"
                id="hero-title"
                v-model="heroContent.title"
                placeholder="Enter hero title"
              />
            </div>

            <div class="form-group">
              <label for="hero-subtitle">Subtitle</label>
              <input
                type="text"
                id="hero-subtitle"
                v-model="heroContent.subtitle"
                placeholder="Enter hero subtitle"
              />
            </div>

            <div class="form-group">
              <label for="hero-content">Description</label>
              <textarea
                id="hero-content"
                v-model="heroContent.content"
                rows="4"
                placeholder="Enter hero description"
              ></textarea>
            </div>

            <div class="form-group">
              <label>Background Images (Carousel)</label>
              <div class="image-upload-section">
                <!-- Current Images Display -->
                <div v-if="heroContent.images && heroContent.images.length > 0" class="current-images">
                  <div
                    v-for="(image, index) in heroContent.images"
                    :key="index"
                    class="current-image-item"
                  >
                    <img :src="image" :alt="`Hero background ${index + 1}`" />
                    <div class="image-controls">
                      <button type="button" class="btn-move" @click="moveHeroImage(index, -1)" :disabled="index === 0">↑</button>
                      <button type="button" class="btn-move" @click="moveHeroImage(index, 1)" :disabled="index === heroContent.images.length - 1">↓</button>
                      <button type="button" class="btn-remove" @click="removeHeroImage(index)">Remove</button>
                    </div>
                  </div>
                </div>

                <!-- Upload Area -->
                <div class="upload-area">
                  <input
                    type="file"
                    ref="heroImagesInput"
                    @change="handleHeroImagesUpload"
                    accept="image/*"
                    multiple
                    style="display: none"
                  />
                  <button type="button" class="btn-upload" @click="$refs.heroImagesInput.click()">
                    {{ heroContent.images && heroContent.images.length > 0 ? 'Add More Images' : 'Upload Images' }}
                  </button>
                  <p class="upload-hint">You can select multiple images for the carousel</p>
                </div>
              </div>
            </div>

            <div class="form-actions">
              <button
                type="button"
                class="btn-primary"
                @click="saveHeroContent"
                :disabled="heroContentLoading"
              >
                {{ heroContentLoading ? 'Saving...' : 'Save Changes' }}
              </button>
              <button
                type="button"
                class="btn-secondary"
                @click="loadHeroContent"
              >
                Reset
              </button>
            </div>
          </div>
        </div>

        <!-- Booking Policy Section -->
        <div class="homepage-content-form">
          <div class="form-card">
            <h4>Booking & Cancellation Policy</h4>

            <div class="form-group">
              <label for="policy-title">Title</label>
              <input
                type="text"
                id="policy-title"
                v-model="bookingPolicyContent.title"
                placeholder="Enter policy section title"
              />
            </div>

            <div class="form-group">
              <label for="policy-subtitle">Subtitle</label>
              <input
                type="text"
                id="policy-subtitle"
                v-model="bookingPolicyContent.subtitle"
                placeholder="Enter policy section subtitle"
              />
            </div>

            <div class="form-group">
              <label for="policy-content">Content</label>
              <textarea
                id="policy-content"
                v-model="bookingPolicyContent.content"
                rows="8"
                placeholder="Enter booking policy content (HTML supported)"
              ></textarea>
            </div>

            <div class="form-actions">
              <button
                type="button"
                class="btn-primary"
                @click="saveBookingPolicyContent"
                :disabled="bookingPolicyContentLoading"
              >
                {{ bookingPolicyContentLoading ? 'Saving...' : 'Save Policy' }}
              </button>
              <button
                type="button"
                class="btn-secondary"
                @click="loadBookingPolicyContent"
              >
                Reset
              </button>
            </div>
          </div>
        </div>

        <!-- Important Notes Section -->
        <div class="homepage-content-form">
          <div class="form-card">
            <h4>Important Notes</h4>

            <div class="form-group">
              <label for="notes-title">Title</label>
              <input
                type="text"
                id="notes-title"
                v-model="notesContent.title"
                placeholder="Enter notes section title"
              />
            </div>

            <div class="form-group">
              <label for="notes-subtitle">Subtitle</label>
              <input
                type="text"
                id="notes-subtitle"
                v-model="notesContent.subtitle"
                placeholder="Enter notes section subtitle"
              />
            </div>

            <div class="form-group">
              <label for="notes-content">Content</label>
              <textarea
                id="notes-content"
                v-model="notesContent.content"
                rows="6"
                placeholder="Enter important notes content (HTML supported)"
              ></textarea>
            </div>

            <div class="form-actions">
              <button
                type="button"
                class="btn-primary"
                @click="saveNotesContent"
                :disabled="notesContentLoading"
              >
                {{ notesContentLoading ? 'Saving...' : 'Save Notes' }}
              </button>
              <button
                type="button"
                class="btn-secondary"
                @click="loadNotesContent"
              >
                Reset
              </button>
            </div>
          </div>
        </div>
      </section>

      <!-- About Us Section -->
      <section v-if="activeSection === 'about'" class="content-section">
        <div class="section-header">
          <h3>About Us Content Management</h3>
          <p>Manage About Us section content and images</p>
        </div>

        <div class="about-content-container">
          <!-- Content Form -->
          <div class="about-content-form">
            <div class="form-card">
              <h4><i class="icon-edit"></i> Content Information</h4>

              <div class="form-row">
                <div class="form-group">
                  <label for="about-title">
                    <i class="icon-title"></i> Title
                  </label>
                  <input
                    type="text"
                    id="about-title"
                    v-model="aboutContent.title"
                    placeholder="Enter about title"
                    class="form-input"
                  />
                </div>

                <div class="form-group">
                  <label for="about-subtitle">
                    <i class="icon-subtitle"></i> Subtitle
                  </label>
                  <input
                    type="text"
                    id="about-subtitle"
                    v-model="aboutContent.subtitle"
                    placeholder="Enter about subtitle"
                    class="form-input"
                  />
                </div>
              </div>

              <div class="form-group">
                <label for="about-content">
                  <i class="icon-content"></i> Content
                </label>
                <textarea
                  id="about-content"
                  v-model="aboutContent.content"
                  rows="6"
                  placeholder="Enter about content"
                  class="form-textarea"
                ></textarea>
              </div>
            </div>
          </div>

          <!-- Images Section -->
          <div class="about-images-section">
            <div class="form-card">
              <h4><i class="icon-image"></i> Image Gallery</h4>

              <div class="images-grid">
                <!-- Large Image -->
                <div class="image-upload-card large-image-card">
                  <div class="image-card-header">
                    <h5>Large Image</h5>
                    <span class="image-type-badge">Main</span>
                  </div>

                  <div class="image-upload-area">
                    <input
                      type="file"
                      ref="aboutLargeImageInput"
                      @change="handleAboutLargeImageUpload"
                      accept="image/*"
                      style="display: none"
                    />

                    <div v-if="aboutContent.large_image" class="image-preview-card">
                      <div class="image-preview-container">
                        <img :src="aboutContent.large_image" alt="Large image preview" />
                        <div class="image-overlay">
                          <button type="button" @click="removeAboutLargeImage" class="remove-btn">
                            <i class="icon-trash"></i>
                          </button>
                          <button type="button" @click="$refs.aboutLargeImageInput.click()" class="edit-btn">
                            <i class="icon-edit"></i>
                          </button>
                        </div>
                      </div>
                      <div class="image-info">
                        <span class="image-name">Large Image</span>
                        <span class="image-status">Active</span>
                      </div>
                    </div>

                    <div v-else class="upload-placeholder" @click="$refs.aboutLargeImageInput.click()">
                      <div class="upload-icon">
                        <i class="icon-upload"></i>
                      </div>
                      <h6>Upload Large Image</h6>
                      <p>Click to browse or drag & drop</p>
                      <span class="upload-hint">Recommended: 800x600px</span>
                    </div>
                  </div>
                </div>

                <!-- Small Image -->
                <div class="image-upload-card small-image-card">
                  <div class="image-card-header">
                    <h5>Small Image</h5>
                    <span class="image-type-badge secondary">Accent</span>
                  </div>

                  <div class="image-upload-area">
                    <input
                      type="file"
                      ref="aboutSmallImageInput"
                      @change="handleAboutSmallImageUpload"
                      accept="image/*"
                      style="display: none"
                    />

                    <div v-if="aboutContent.small_image" class="image-preview-card">
                      <div class="image-preview-container">
                        <img :src="aboutContent.small_image" alt="Small image preview" />
                        <div class="image-overlay">
                          <button type="button" @click="removeAboutSmallImage" class="remove-btn">
                            <i class="icon-trash"></i>
                          </button>
                          <button type="button" @click="$refs.aboutSmallImageInput.click()" class="edit-btn">
                            <i class="icon-edit"></i>
                          </button>
                        </div>
                      </div>
                      <div class="image-info">
                        <span class="image-name">Small Image</span>
                        <span class="image-status">Active</span>
                      </div>
                    </div>

                    <div v-else class="upload-placeholder" @click="$refs.aboutSmallImageInput.click()">
                      <div class="upload-icon">
                        <i class="icon-upload"></i>
                      </div>
                      <h6>Upload Small Image</h6>
                      <p>Click to browse or drag & drop</p>
                      <span class="upload-hint">Recommended: 400x300px</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Large Image 2 Upload -->
              <div class="form-group">
                <div class="image-upload-section">
                  <label class="form-label">
                    <i class="icon-image"></i> Large Image 2 (for About Page)
                  </label>
                  <div class="image-upload-container">
                    <input
                      ref="aboutLargeImage2Input"
                      type="file"
                      @change="handleAboutLargeImage2Upload"
                      accept="image/*"
                      style="display: none"
                    />

                    <div v-if="aboutContent.large_image2" class="image-preview-card">
                      <div class="image-preview-container">
                        <img :src="aboutContent.large_image2" alt="Large image 2 preview" />
                        <div class="image-overlay">
                          <button type="button" @click="removeAboutLargeImage2" class="remove-btn">
                            <i class="icon-trash"></i>
                          </button>
                          <button type="button" @click="$refs.aboutLargeImage2Input.click()" class="edit-btn">
                            <i class="icon-edit"></i>
                          </button>
                        </div>
                      </div>
                      <div class="image-info">
                        <span class="image-name">Large Image 2</span>
                        <span class="image-status">Active</span>
                      </div>
                    </div>

                    <div v-else class="upload-placeholder" @click="$refs.aboutLargeImage2Input.click()">
                      <div class="upload-icon">
                        <i class="icon-upload"></i>
                      </div>
                      <h6>Upload Large Image 2</h6>
                      <p>Click to browse or drag & drop</p>
                      <span class="upload-hint">Recommended: 800x600px</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Preview Section -->
          <div class="about-preview-section">
            <div class="form-card">
              <h4><i class="icon-eye"></i> Live Preview</h4>

              <div class="preview-container">
                <div class="about-preview">
                  <div class="preview-images">
                    <div class="preview-small-image">
                      <img v-if="aboutContent.small_image" :src="aboutContent.small_image" alt="Small preview" />
                      <div v-else class="preview-placeholder">Small Image</div>
                    </div>
                    <div class="preview-large-image">
                      <img v-if="aboutContent.large_image" :src="aboutContent.large_image" alt="Large preview" />
                      <div v-else class="preview-placeholder">Large Image</div>
                    </div>
                  </div>
                  <div class="preview-content">
                    <h3 class="preview-subtitle">{{ aboutContent.subtitle || 'Subtitle' }}</h3>
                    <h2 class="preview-title">{{ aboutContent.title || 'Title' }}</h2>
                    <p class="preview-description">{{ aboutContent.content || 'Content description...' }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="about-actions">
            <button
              type="button"
              class="btn-primary save-btn"
              @click="saveAboutContent"
              :disabled="aboutContentLoading"
            >
              <i class="icon-save"></i>
              {{ aboutContentLoading ? 'Saving...' : 'Save Changes' }}
            </button>
            <button
              type="button"
              class="btn-secondary reset-btn"
              @click="loadAboutContent"
              :disabled="aboutContentLoading"
            >
              <i class="icon-refresh"></i>
              Reset
            </button>
          </div>
        </div>
      </section>

      <!-- Bookings Section -->
      <section v-if="activeSection === 'bookings'" class="content-section">
        <div class="section-header">
          <h3>Manage Bookings</h3>
          <div class="filter-controls">
            <input
              v-model="bookingDateFilter"
              type="date"
              placeholder="Filter by date"
              class="filter-input"
            >
            <select v-model="bookingStatusFilter" class="filter-select">
              <option value="">All Status</option>
              <option value="PENDING">Pending</option>
              <option value="CONFIRMED">Confirmed</option>
              <option value="COMPLETED">Completed</option>
              <option value="CANCELLED">Cancelled</option>
            </select>
            <input
              v-model="bookingSearchTerm"
              type="text"
              placeholder="Search by customer name or phone..."
              class="filter-input search-input"
            >
          </div>
        </div>

        <div class="bookings-grid">
          <div
            v-for="booking in filteredBookings"
            :key="booking.id"
            class="booking-card"
            :class="`booking-${booking.status.toLowerCase()}`"
          >
            <div class="booking-header">
              <div class="booking-id">
                <span class="id-label">Booking #</span>
                <span class="id-value">{{ booking.id.substring(0, 8) }}</span>
              </div>
              <div class="booking-status">
                <span
                  class="status-badge"
                  :class="`status-${booking.status.toLowerCase()}`"
                >
                  {{ booking.status }}
                </span>
                <span
                  class="payment-badge"
                  :class="`payment-${booking.payment_status ? booking.payment_status.toLowerCase() : 'pending'}`"
                >
                  {{ booking.payment_status || 'PENDING' }}
                </span>
              </div>
            </div>

            <div class="booking-content">
              <div class="booking-service">
                <h4>{{ getServiceName(booking.service_id) }}</h4>
                <span class="service-type">{{ getServiceType(booking.service_id) }}</span>
              </div>

              <div class="booking-customer">
                <div class="customer-info">
                  <i class="icon-user"></i>
                  <span>{{ booking.customer_name }}</span>
                </div>
                <div class="customer-phone">
                  <i class="icon-phone"></i>
                  <span>{{ booking.customer_phone }}</span>
                </div>
              </div>

              <div class="booking-dates">
                <div class="booking-date">
                  <i class="icon-calendar"></i>
                  <span>{{ formatDate(booking.booking_date) }}</span>
                </div>
                <div v-if="booking.check_in_date && booking.check_out_date" class="accommodation-dates">
                  <div class="check-dates">
                    <span class="check-in">
                      <i class="icon-login"></i>
                      Check-in: {{ formatDateTime(booking.check_in_date) }}
                    </span>
                    <span class="check-out">
                      <i class="icon-logout"></i>
                      Check-out: {{ formatDateTime(booking.check_out_date) }}
                    </span>
                  </div>
                </div>
              </div>

              <div class="booking-guests">
                <div class="guest-count">
                  <span class="adults">
                    <i class="icon-users"></i>
                    {{ booking.adults }} Adults
                  </span>
                  <span v-if="booking.children_6_to_11 > 0" class="children">
                    <i class="icon-child"></i>
                    {{ booking.children_6_to_11 }} Children (6-11)
                  </span>
                  <span v-if="booking.children_under_6 > 0" class="children">
                    <i class="icon-baby"></i>
                    {{ booking.children_under_6 }} Children (Under 6)
                  </span>
                </div>
              </div>

              <div v-if="booking.total_price" class="booking-pricing">
                <div class="price-info">
                  <span class="total-price">
                    <i class="icon-money"></i>
                    Total: {{ formatPrice(booking.total_price) }}
                  </span>
                  <span v-if="booking.deposit_amount" class="deposit">
                    Deposit: {{ formatPrice(booking.deposit_amount) }}
                  </span>
                </div>
              </div>

              <div v-if="booking.special_notes" class="booking-notes">
                <div class="notes">
                  <i class="icon-note"></i>
                  <span>{{ booking.special_notes }}</span>
                </div>
              </div>

              <div class="booking-meta">
                <span class="created-date">
                  <i class="icon-clock"></i>
                  Created: {{ formatDateTime(booking.created_at) }}
                </span>
              </div>
            </div>

            <div class="booking-actions">
              <button class="btn-view" @click="viewBookingDetails(booking)">
                <i class="icon-eye"></i>
                View Details
              </button>
              <button class="btn-edit" @click="editBooking(booking.id)">
                <i class="icon-edit"></i>
                Edit
              </button>
              <button
                class="btn-status"
                @click="updateBookingStatus(booking)"
                :class="`btn-${getNextStatusAction(booking.status).toLowerCase()}`"
              >
                <i :class="`icon-${getNextStatusIcon(booking.status)}`"></i>
                {{ getNextStatusAction(booking.status) }}
              </button>
              <button
                v-if="booking.status !== 'CANCELLED'"
                class="btn-cancel"
                @click="cancelBooking(booking.id)"
              >
                <i class="icon-close"></i>
                Cancel
              </button>
              <button
                v-if="booking.status === 'COMPLETED' || booking.status === 'CANCELLED'"
                class="btn-delete"
                @click="deleteBooking(booking.id)"
              >
                <i class="icon-trash"></i>
                Delete
              </button>
            </div>
          </div>
        </div>

        <div v-if="filteredBookings.length === 0" class="empty-state">
          <i class="icon-calendar-empty"></i>
          <h4>No bookings found</h4>
          <p>No bookings match your current filters.</p>
        </div>
      </section>

      <!-- Users Section -->
      <section v-if="activeSection === 'users'" class="content-section">
        <div class="section-header">
          <h3>Manage Users</h3>
          <div class="section-header-actions">
            <div class="search-box">
              <input
                v-model="userSearchTerm"
                type="text"
                placeholder="Search users..."
              >
            </div>
            <button class="btn-primary" @click="openCreateUserModal">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <circle cx="9" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
                <line x1="19" y1="8" x2="19" y2="14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <line x1="22" y1="11" x2="16" y2="11" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              Add User
            </button>
          </div>
        </div>
        <div class="users-table">
          <table>
            <thead>
              <tr>
                <th>Name</th>
                <th>Email</th>
                <th>Phone</th>
                <th>Role</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="user in filteredUsers" :key="user.id">
                <td>{{ user.first_name }} {{ user.last_name }}</td>
                <td>{{ user.email }}</td>
                <td>{{ user.phone }}</td>
                <td>
                  <span 
                    class="status-badge" 
                    :class="user.role === 'admin' ? 'status-confirmed' : 'status-pending'"
                  >
                    {{ user.role }}
                  </span>
                </td>
                <td>
                  <div class="action-buttons">
                    <button class="btn-edit" @click="openEditUserModal(user)" title="Edit User">
                      <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                      </svg>
                    </button>
                    <button
                      v-if="user.role !== 'admin'"
                      class="btn-delete"
                      @click="confirmDeleteUser(user)"
                      title="Delete User"
                    >
                      <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <polyline points="3,6 5,6 21,6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <line x1="10" y1="11" x2="10" y2="17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        <line x1="14" y1="11" x2="14" y2="17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                      </svg>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </section>

      <!-- Settings Section -->
      <section v-if="activeSection === 'settings'" class="content-section">
        <div class="settings-grid">
          <div class="settings-card">
            <h3>General Settings</h3>
            <form @submit.prevent="saveGeneralSettings" :class="{ loading: settingsLoading }">
              <div class="form-group">
                <label>Site Title</label>
                <input v-model="generalSettings.siteTitle" type="text" name="siteTitle">
              </div>
              <div class="form-group">
                <label>Contact Email</label>
                <input v-model="generalSettings.contactEmail" type="email" name="contactEmail">
              </div>
              <div class="form-group">
                <label>Contact Phone</label>
                <input v-model="generalSettings.contactPhone" type="tel" name="contactPhone">
              </div>
              <div class="form-group">
                <label>Address</label>
                <textarea v-model="generalSettings.address" name="address" rows="3" placeholder="Business address"></textarea>
              </div>
              <button type="submit" class="btn-primary" :disabled="settingsLoading">
                {{ settingsLoading ? 'Saving...' : 'Save Changes' }}
              </button>
            </form>
          </div>
          <div class="settings-card">
            <h3>Social Media Links</h3>
            <form @submit.prevent="saveSocialLinks" :class="{ loading: socialLoading }">
              <div class="form-group">
                <label>Instagram</label>
                <input v-model="socialLinks.instagram" type="url" placeholder="Instagram URL">
              </div>
              <div class="form-group">
                <label>Facebook</label>
                <input v-model="socialLinks.facebook" type="url" placeholder="Facebook URL">
              </div>
              <button type="submit" class="btn-primary" :disabled="socialLoading">
                {{ socialLoading ? 'Saving...' : 'Save Links' }}
              </button>
            </form>
          </div>
        </div>
      </section>
    </main>

    <!-- Service Modal -->
    <div v-if="showServiceModal" class="modal" @click="closeServiceModal">
      <div class="modal-content" @click.stop>
        <span class="close" @click="closeServiceModal">&times;</span>
        <h3>{{ editingService ? 'Edit Service' : 'Add New Service' }}</h3>
        <form @submit.prevent="saveService" :class="{ loading: serviceLoading }">
          <div class="form-group">
            <label>Service Name</label>
            <input v-model="serviceForm.name" type="text" required>
          </div>
          <div class="form-group">
            <label>Type</label>
            <select v-model="serviceForm.type" required>
              <option value="ACCOMMODATION">Accommodation</option>
              <option value="AFTERNOON_TEA">Afternoon Tea</option>
              <option value="WATER_ACTIVITY">Water Activity</option>
              <option value="SIGHTSEEING">Sightseeing</option>
              <option value="GO_KART">Go-Kart</option>
              <option value="KIDS_WATER_PARK">Kids Water Park</option>
              <option value="PARAGLIDING">Paragliding</option>
            </select>
          </div>
          <div class="form-group">
            <label>Description</label>
            <textarea v-model="serviceForm.description" required></textarea>
          </div>
          <div class="form-row">
            <div class="form-group">
              <label>Adult Price (VND)</label>
              <input v-model.number="serviceForm.price" type="number" required>
            </div>
            <div class="form-group">
              <label>Child Price (VND)</label>
              <input v-model.number="serviceForm.child_price" type="number">
            </div>
          </div>
          <div class="form-group">
            <label>Capacity</label>
            <input v-model.number="serviceForm.capacity" type="number" min="1" required>
          </div>
          <div class="form-row">
            <div class="form-group">
              <label>Open Time</label>
              <input v-model="serviceForm.open_time" type="time" required>
            </div>
            <div class="form-group">
              <label>Close Time</label>
              <input v-model="serviceForm.close_time" type="time" required>
            </div>
          </div>
          <div class="form-group">
            <label>Service Images</label>
            <div class="service-image-upload">
              <input
                @change="handleServiceImagesSelect"
                type="file"
                accept="image/*"
                multiple
                ref="serviceImagesInput"
                style="display: none;"
              >
              <button
                type="button"
                @click="$refs.serviceImagesInput.click()"
                class="btn-secondary"
              >
                Select Images
              </button>

              <!-- New image previews -->
              <div v-if="serviceImagePreviews.length > 0" class="service-image-previews">
                <div
                  v-for="(preview, index) in serviceImagePreviews"
                  :key="'preview-' + index"
                  class="service-preview-item"
                >
                  <img :src="preview" alt="Service Preview">
                  <button
                    type="button"
                    @click="removeServiceImagePreview(index)"
                    class="remove-preview-btn"
                  >
                    ×
                  </button>
                </div>
              </div>

              <!-- Existing images -->
              <div v-if="serviceForm.images && serviceForm.images.length > 0" class="existing-service-images">
                <h4>Current Images:</h4>
                <div class="service-image-previews">
                  <div
                    v-for="(image, index) in serviceForm.images"
                    :key="'existing-' + index"
                    class="existing-image-item"
                  >
                    <img :src="getImageUrl(image)" :alt="'Service Image ' + (index + 1)" @error="handleImageError">
                    <button
                      type="button"
                      @click="removeExistingServiceImage(index)"
                      class="remove-existing-btn"
                    >
                      ×
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- Inclusions Section -->
          <div class="form-group">
            <label>Inclusions (What's included)</label>
            <div class="dynamic-list">
              <div v-for="(inclusion, index) in serviceForm.inclusions" :key="'inclusion-' + index" class="list-item">
                <input v-model="serviceForm.inclusions[index]" type="text" placeholder="Enter inclusion">
                <button type="button" @click="removeInclusion(index)" class="btn-remove">×</button>
              </div>
              <button type="button" @click="addInclusion" class="btn-add">+ Add Inclusion</button>
            </div>
          </div>

          <!-- Requirements Section -->
          <div class="form-group">
            <label>Requirements</label>
            <div class="dynamic-list">
              <div v-for="(requirement, index) in serviceForm.requirements" :key="'requirement-' + index" class="list-item">
                <input v-model="serviceForm.requirements[index]" type="text" placeholder="Enter requirement">
                <button type="button" @click="removeRequirement(index)" class="btn-remove">×</button>
              </div>
              <button type="button" @click="addRequirement" class="btn-add">+ Add Requirement</button>
            </div>
          </div>

          <div class="form-group">
            <label>Status</label>
            <div class="toggle-switch">
              <input v-model="serviceForm.is_active" type="checkbox" id="serviceStatus">
              <label for="serviceStatus">Active</label>
            </div>
          </div>
          <button type="submit" class="btn-primary" :disabled="serviceLoading">
            {{ serviceLoading ? 'Saving...' : 'Save Service' }}
          </button>
        </form>
      </div>
    </div>

    <!-- Upload Modal -->
    <div v-if="showUploadModal" class="modal" @click="closeUploadModal">
      <div class="modal-content" @click.stop>
        <span class="close" @click="closeUploadModal">&times;</span>
        <h3>Upload Images</h3>
        <form @submit.prevent="uploadImages" :class="{ loading: uploadLoading }">
          <div class="form-group">
            <label>Select Images</label>
            <input 
              @change="handleFileSelect" 
              type="file" 
              multiple 
              accept="image/*" 
              required
            >
          </div>
          <div class="upload-preview">
            <div 
              v-for="(preview, index) in imagePreviews" 
              :key="index" 
              class="preview-item"
            >
              <img :src="preview" alt="Preview">
              <button 
                type="button" 
                class="preview-remove" 
                @click="removePreview(index)"
              >
                &times;
              </button>
            </div>
          </div>
          <button type="submit" class="btn-primary" :disabled="uploadLoading || !selectedFiles.length">
            {{ uploadLoading ? 'Uploading...' : 'Upload' }}
          </button>
        </form>
      </div>
    </div>

    <!-- Gallery Edit Modal -->
    <div v-if="showGalleryEditModal" class="modal" @click="closeGalleryEditModal">
      <div class="modal-content" @click.stop>
        <span class="close" @click="closeGalleryEditModal">&times;</span>
        <h3>{{ editingGalleryItem ? 'Edit Gallery Item' : 'Add Gallery Item' }}</h3>
        <form @submit.prevent="saveGalleryItem" :class="{ loading: galleryLoading }">
          <div class="form-group">
            <label>Title</label>
            <input v-model="galleryForm.title" type="text" required @input="updateAltText">
          </div>
          <div class="form-group">
            <label>Description</label>
            <textarea v-model="galleryForm.description" required></textarea>
          </div>
          <div class="form-group">
            <label>Image</label>
            <div class="image-upload-section">
              <!-- Image Preview -->
              <div v-if="galleryForm.image_url" class="image-preview">
                <img :src="galleryForm.image_url" alt="Preview" style="max-width: 200px; max-height: 150px; border-radius: 8px;">
                <button type="button" class="btn-remove" @click="removeGalleryImage">Remove</button>
              </div>

              <!-- Upload Button -->
              <div v-if="!galleryForm.image_url" class="upload-area">
                <input
                  type="file"
                  ref="galleryImageInput"
                  @change="handleGalleryImageUpload"
                  accept="image/*"
                  style="display: none;"
                >
                <button
                  type="button"
                  class="btn-upload"
                  @click="$refs.galleryImageInput.click()"
                  :disabled="galleryImageUploading"
                >
                  {{ galleryImageUploading ? 'Uploading...' : '📁 Choose Image' }}
                </button>
                <p class="upload-hint">Click to select an image file</p>
              </div>
            </div>
          </div>
          <div class="form-group">
            <label>Alt Text (Auto-generated from title)</label>
            <input v-model="galleryForm.alt_text" type="text" required readonly>
          </div>
          <div class="form-group">
            <label>Display Order</label>
            <input v-model.number="galleryForm.display_order" type="number" min="1">
          </div>
          <div class="form-group">
            <label>Status</label>
            <div class="toggle-switch">
              <input v-model="galleryForm.is_active" type="checkbox" id="galleryStatus">
              <label for="galleryStatus">Active</label>
            </div>
          </div>
          <button type="submit" class="btn-primary" :disabled="galleryLoading || !galleryForm.image_url">
            {{ galleryLoading ? 'Saving...' : (editingGalleryItem ? 'Update' : 'Add') + ' Gallery Item' }}
          </button>
        </form>
      </div>
    </div>

    <!-- Menu Modal -->
    <div v-if="showMenuModal" class="modal" @click="closeMenuModal">
      <div class="modal-content" @click.stop>
        <span class="close" @click="closeMenuModal">&times;</span>
        <h3>{{ editingMenu ? 'Edit Menu Item' : 'Add New Menu Item' }}</h3>
        <form @submit.prevent="saveMenu" :class="{ loading: menuLoading }">
          <div class="form-group">
            <label>Title *</label>
            <input v-model="menuForm.title" type="text" required placeholder="Enter menu item title">
          </div>
          <div class="form-group">
            <label>Description</label>
            <textarea v-model="menuForm.description" placeholder="Enter menu item description" rows="3"></textarea>
          </div>
          <div class="form-group">
            <label>Category *</label>
            <select v-model="menuForm.category" required>
              <option value="restaurant">Restaurant</option>
              <option value="accommodation">Accommodation</option>
              <option value="afternoon_tea">Afternoon Tea</option>
            </select>
          </div>
          <div class="form-group">
            <label>Image</label>
            <div class="image-upload-section">
              <!-- Image Preview -->
              <div v-if="menuForm.image_url" class="image-preview">
                <img :src="menuForm.image_url" alt="Preview" style="max-width: 200px; max-height: 150px; border-radius: 8px;">
                <button type="button" class="btn-remove" @click="removeMenuImage">Remove</button>
              </div>

              <!-- Upload Button -->
              <div v-if="!menuForm.image_url" class="upload-area">
                <input
                  type="file"
                  ref="menuImageInput"
                  @change="handleMenuImageUpload"
                  accept="image/*"
                  style="display: none;"
                >
                <button
                  type="button"
                  class="btn-upload"
                  @click="$refs.menuImageInput.click()"
                  :disabled="menuImageUploading"
                >
                  {{ menuImageUploading ? 'Uploading...' : '📁 Choose Image' }}
                </button>
                <p class="upload-hint">Click to select an image file</p>
              </div>
            </div>
          </div>
          <div class="form-group">
            <label>Alt Text</label>
            <input v-model="menuForm.alt_text" type="text" placeholder="Enter alt text for accessibility">
          </div>

          <!-- Full Menu Image Section -->
          <div class="form-group">
            <label>Full Menu Image</label>
            <p class="field-description">Upload a complete menu image (like a menu card/brochure) for display on the menu page</p>
            <div class="image-upload-section">
              <!-- Menu Image Preview -->
              <div v-if="menuForm.menu_image" class="image-preview">
                <img :src="menuForm.menu_image" alt="Menu Preview" style="max-width: 200px; max-height: 150px; border-radius: 8px;">
                <button type="button" class="btn-remove" @click="removeMenuFullImage">Remove</button>
              </div>

              <!-- Upload Button -->
              <div v-if="!menuForm.menu_image" class="upload-area">
                <input
                  type="file"
                  ref="menuFullImageInput"
                  @change="handleMenuFullImageUpload"
                  accept="image/*"
                  style="display: none;"
                >
                <button
                  type="button"
                  class="btn-upload"
                  @click="$refs.menuFullImageInput.click()"
                  :disabled="menuFullImageUploading"
                >
                  {{ menuFullImageUploading ? 'Uploading...' : '📁 Choose Full Menu Image' }}
                </button>
                <p class="upload-hint">Click to select a full menu image file</p>
              </div>
            </div>
          </div>

          <button type="submit" class="btn-primary" :disabled="menuLoading">
            {{ menuLoading ? 'Saving...' : (editingMenu ? 'Update' : 'Add') + ' Menu Item' }}
          </button>
        </form>
      </div>
    </div>



    <!-- Notification -->
    <div v-if="notification.show" :class="['notification', notification.type]">
      {{ notification.message }}
    </div>
    </div> <!-- End admin-authenticated -->
  </div>
</template>

<script>
import apiService from '../utils/api.js';
import { formatServiceType, formatPrice, formatDate, showNotification } from '../utils/helpers.js';

export default {
  name: 'AdminApp',
  data() {
    return {
      // Authentication
      isInitializing: true,
      isAuthenticated: false,
      currentUser: null,
      loginForm: {
        email: '',
        password: ''
      },
      loginLoading: false,
      loginError: null,

      // Change password
      showChangePasswordModal: false,
      changePasswordForm: {
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      changePasswordLoading: false,
      changePasswordError: null,

      // UI state
      activeSection: 'dashboard',

      // Notifications
      notification: {
        show: false,
        message: '',
        type: 'info'
      },

      // Data
      services: [],
      galleryImages: [],
      menus: [],
      bookings: [],
      users: [],

      // User management
      userSearchTerm: '',
      showUserModal: false,
      editingUser: null,
      userForm: {
        email: '',
        password: '',
        first_name: '',
        last_name: '',
        phone: '',
        role: '',
        changePassword: false
      },
      userFormLoading: false,
      userFormError: null,

      // Loading states
      servicesLoading: false,
      servicesError: null,
      galleryLoading: false,
      galleryError: null,
      menusLoading: false,
      menusError: null,

      // Gallery pagination
      galleryCurrentPage: 1,
      galleryItemsPerPage: 12,
      galleryPagination: {
        current_page: 1,
        per_page: 12,
        total_count: 0,
        total_pages: 0,
        has_next: false,
        has_previous: false
      },

      // Dashboard stats
      dashboardStats: {
        totalBookings: 0,
        activeServices: 0,
        galleryImages: 0,
        registeredUsers: 0
      },
      
      // Modals
      showServiceModal: false,
      showUploadModal: false,
      showGalleryEditModal: false,
      showMenuModal: false,

      // Forms and loading states
      serviceForm: {
        type: 'TOUR',
        name: '',
        description: '',
        price: 0,
        capacity: 1,
        open_time: '08:00',
        close_time: '18:00',
        images: [],
        is_active: true
      },
      editingService: null,
      serviceLoading: false,
      serviceImageFiles: [], // Array to hold multiple image files
      serviceImagePreviews: [], // Array to hold preview URLs

      galleryForm: {
        title: '',
        description: '',
        image_url: '',
        alt_text: '',
        category: 'general',
        display_order: 1,
        is_active: true
      },
      editingGalleryItem: null,
      galleryLoading: false,
      galleryImageUploading: false,

      // Menu management
      menuForm: {
        title: '',
        description: '',
        category: 'restaurant',
        image_url: '',
        alt_text: '',
        menu_image: '',
        display_order: 0,
        is_active: true
      },
      editingMenu: null,
      menuLoading: false,
      menuImageUploading: false,
      menuFullImageUploading: false, // For menu_image upload
      currentMenuCategory: 'all',
      
      selectedFiles: [],
      imagePreviews: [],
      uploadLoading: false,

      // Service image upload
      serviceImageFile: null,
      serviceImagePreview: null,
      
      generalSettings: {
        siteTitle: 'Phong Nha Valley',
        contactEmail: '<EMAIL>',
        contactPhone: '************',
        address: 'Chày Lập Hamlet, Phúc Trạch Commune\nBố Trạch District, Quảng Bình Province'
      },
      socialLinks: {
        instagram: '',
        facebook: ''
      },
      settingsLoading: false,
      socialLoading: false,

      // Homepage content
      heroContent: {
        title: '',
        subtitle: '',
        content: '',
        image_url: ''
      },
      heroContentLoading: false,

      // About Us content
      aboutContent: {
        title: '',
        subtitle: '',
        content: '',
        large_image: '',
        large_image2: '',
        small_image: ''
      },
      aboutContentLoading: false,

      // Booking Policy content
      bookingPolicyContent: {
        title: '',
        subtitle: '',
        content: ''
      },
      bookingPolicyContentLoading: false,

      // Notes content
      notesContent: {
        title: '',
        subtitle: '',
        content: ''
      },
      notesContentLoading: false,

      // Filters
      bookingDateFilter: '',
      bookingStatusFilter: '',
      bookingSearchTerm: '',
      userSearchTerm: ''
    };
  },
  
  computed: {
    token() {
      return localStorage.getItem('auth_token');
    },

    filteredBookings() {
      let filtered = this.bookings;

      if (this.bookingStatusFilter) {
        filtered = filtered.filter(booking => booking.status === this.bookingStatusFilter);
      }

      if (this.bookingDateFilter) {
        filtered = filtered.filter(booking => {
          const bookingDate = new Date(booking.booking_date).toISOString().split('T')[0];
          return bookingDate === this.bookingDateFilter;
        });
      }

      if (this.bookingSearchTerm) {
        const searchTerm = this.bookingSearchTerm.toLowerCase();
        filtered = filtered.filter(booking =>
          booking.customer_name.toLowerCase().includes(searchTerm) ||
          booking.customer_phone.includes(searchTerm) ||
          booking.id.toLowerCase().includes(searchTerm)
        );
      }

      return filtered.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
    },
    
    filteredUsers() {
      if (!this.userSearchTerm) return this.users;

      const searchTerm = this.userSearchTerm.toLowerCase();
      return this.users.filter(user =>
        user.first_name.toLowerCase().includes(searchTerm) ||
        user.last_name.toLowerCase().includes(searchTerm) ||
        user.email.toLowerCase().includes(searchTerm)
      );
    },

    // Gallery pagination computed properties
    paginatedGalleryImages() {
      // With server-side pagination, galleryImages already contains only current page items
      console.log(`📄 Gallery pagination: page ${this.galleryCurrentPage}, showing ${this.galleryImages.length} items`);
      return this.galleryImages;
    },

    galleryTotalPages() {
      return this.galleryPagination.total_pages || 0;
    },

    galleryPageNumbers() {
      const pages = [];
      for (let i = 1; i <= this.galleryTotalPages; i++) {
        pages.push(i);
      }
      return pages;
    },

    // Menu computed properties
    filteredMenus() {
      if (this.currentMenuCategory === 'all') {
        return this.menus;
      }
      return this.menus.filter(menu => menu.category === this.currentMenuCategory);
    }
  },
  
  async mounted() {
    await this.initializeAdminPanel();
    await this.loadSettings();
  },
  
  methods: {
    // Utility methods
    formatServiceType,
    formatPrice,
    formatDate,
    showNotification,
    
    async initializeAdminPanel() {
      try {
        this.isInitializing = true;

        // Check if user is already authenticated
        const token = localStorage.getItem('auth_token');

        if (token) {
          // Set token in API service
          apiService.setToken(token);

          // Try to verify token
          const user = await this.verifyAuthToken(token);
          if (user && user.role === 'admin') {
            this.currentUser = user;
            this.isAuthenticated = true;

            // Load real data
            await Promise.all([
              this.loadServices(),
              this.loadGalleryImages(),
              this.loadMenus(),
              this.loadBookings(),
              this.loadUsers(),
              this.loadSettings()
            ]);

            this.updateDashboardStats();
          } else {
            // Invalid token, clear and show login
            localStorage.removeItem('auth_token');
            apiService.setToken(null);
            this.isAuthenticated = false;
          }
        } else {
          this.isAuthenticated = false;
        }
      } catch (error) {
        console.error('Failed to initialize admin panel:', error);
        localStorage.removeItem('auth_token');
        apiService.setToken(null);
        this.isAuthenticated = false;
      } finally {
        // Always stop loading state
        this.isInitializing = false;
      }
    },

    async verifyAuthToken(token) {
      try {
        const response = await apiService.get('/admin/profile');
        return response.user || response;
      } catch (error) {
        console.error('Token verification failed:', error);
        return null;
      }
    },



    // Check if user is really logged out
    async checkLogoutStatus() {
      try {
        const token = localStorage.getItem('auth_token');
        if (!token) {
          console.log('✅ No token found - user is logged out');
          return true;
        }

        // Try to access protected endpoint
        await apiService.get('/admin/profile');
        console.log('❌ Still authenticated - logout failed');
        return false;
      } catch (error) {
        console.log('✅ Access denied - logout successful');
        return true;
      }
    },



    async handleLogin() {
      this.loginLoading = true;
      this.loginError = null;

      try {
        // Try API login first for real credentials
        console.log('=== LOGIN DEBUG ===');
        console.log('Attempting API login for:', this.loginForm.email);
        const response = await apiService.login(this.loginForm.email, this.loginForm.password);
        console.log('API login response:', response);

        if (response.token && response.user) {
          // Check if user is admin
          if (response.user.role === 'admin') {
            // Store token and user info
            localStorage.setItem('auth_token', response.token);
            apiService.setToken(response.token);
            this.currentUser = response.user;
            this.isAuthenticated = true;

            // Load admin data
            await Promise.all([
              this.loadServices(),
              this.loadGalleryImages(),
              this.loadBookings(),
              this.loadUsers(),
              this.loadSettings()
            ]);

            this.updateDashboardStats();
            this.showNotification('Welcome to admin panel!', 'success');
          } else {
            this.loginError = 'Access denied. Admin privileges required.';
          }
        } else {
          this.loginError = 'Invalid login response';
        }
      } catch (error) {
        console.error('Login failed:', error);
        this.loginError = error.message || 'Login failed. Please check your credentials.';
      } finally {
        this.loginLoading = false;
      }
    },

    // Change Password methods
    closeChangePasswordModal() {
      this.showChangePasswordModal = false;
      this.changePasswordForm = {
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      };
      this.changePasswordError = null;
    },

    async handleChangePassword() {
      this.changePasswordLoading = true;
      this.changePasswordError = null;

      try {
        // Validate passwords match
        if (this.changePasswordForm.newPassword !== this.changePasswordForm.confirmPassword) {
          this.changePasswordError = 'New passwords do not match';
          return;
        }

        // Validate password length
        if (this.changePasswordForm.newPassword.length < 6) {
          this.changePasswordError = 'New password must be at least 6 characters';
          return;
        }

        // Call API to change password
        await apiService.changePassword(
          this.changePasswordForm.currentPassword,
          this.changePasswordForm.newPassword
        );

        this.showNotification('Password changed successfully! Please login again.', 'success');
        this.closeChangePasswordModal();

        // Logout user to force re-login with new password
        this.logout();

      } catch (error) {
        console.error('Change password failed:', error);
        this.changePasswordError = error.message || 'Failed to change password';
      } finally {
        this.changePasswordLoading = false;
      }
    },

    // Navigation
    async setActiveSection(section) {
      this.activeSection = section;

      // Load data when switching to specific sections
      if (section === 'homepage') {
        await this.loadHeroContent();
        await this.loadBookingPolicyContent();
        await this.loadNotesContent();
      } else if (section === 'about') {
        await this.loadAboutContent();
      }
    },
    
    getSectionTitle() {
      const titles = {
        dashboard: 'Dashboard',
        services: 'Services',
        gallery: 'Gallery',
        homepage: 'Homepage Content',
        about: 'About Us Content',
        bookings: 'Bookings',
        users: 'Users',
        settings: 'Settings'
      };
      return titles[this.activeSection] || 'Dashboard';
    },
    
    async logout() {
      try {
        // Call API logout to invalidate refresh tokens
        await apiService.logout();

        // Clear authentication state
        localStorage.removeItem('auth_token');
        apiService.setToken(null);
        this.isAuthenticated = false;
        this.currentUser = null;
        this.loginForm = { email: '', password: '' };
        this.loginError = null;

        // Clear data
        this.services = [];
        this.galleryImages = [];
        this.bookings = [];
        this.users = [];

        // Force Vue to update the UI
        await this.$nextTick();
        this.$forceUpdate();

        this.showNotification('Logged out successfully', 'success');

      } catch (error) {
        console.error('Logout failed:', error);
        // Even if API call fails, clear local state
        localStorage.removeItem('auth_token');
        apiService.setToken(null);
        this.isAuthenticated = false;
        this.currentUser = null;
        this.loginForm = { email: '', password: '' };
        this.loginError = null;

        // Clear data
        this.services = [];
        this.galleryImages = [];
        this.bookings = [];
        this.users = [];

        // Force Vue to update the UI
        await this.$nextTick();
        this.$forceUpdate();

        this.showNotification('Logged out (with errors)', 'warning');
      }
    },
    
    // Data loading methods
    async loadServices() {
      this.servicesLoading = true;
      this.servicesError = null;
      try {
        // Try to load from admin API first (includes both active and inactive)
        console.log('🔥 CALLING ADMIN SERVICES API - VERSION 2025-07-09-04 🔥');
        console.log('🔥 This should call /api/v1/admin/services/ 🔥');
        const response = await apiService.getAdminServices();
        console.log('🔥 Admin services response received:', response);

        // Extract services array from response
        this.services = response.services || response || [];
        console.log('Admin services array:', this.services);
        console.log('Number of services loaded:', this.services.length);
      } catch (error) {
        console.error('Failed to load services from API:', error);
        this.servicesError = error.message || 'Failed to load services';
        this.services = [];
        this.showNotification('Failed to load services from server', 'error');
      } finally {
        this.servicesLoading = false;
      }
    },
    
    async loadGalleryImages(page = null) {
      this.galleryLoading = true;
      this.galleryError = null;

      // Use provided page or current page
      const currentPage = page || this.galleryCurrentPage;

      try {
        console.log(`🔍 Loading gallery images for page ${currentPage}...`);
        // Load from API with pagination
        const response = await apiService.getGalleryImages(currentPage, this.galleryItemsPerPage);
        console.log('📡 API Response:', response);
        console.log('📊 Response type:', typeof response);
        console.log('🔑 Response keys:', Object.keys(response || {}));

        // Extract gallery array and pagination from response
        this.galleryImages = response.gallery || [];
        this.galleryPagination = response.pagination || {
          current_page: 1,
          per_page: this.galleryItemsPerPage,
          total_count: 0,
          total_pages: 0,
          has_next: false,
          has_previous: false
        };

        // Update current page from response
        this.galleryCurrentPage = this.galleryPagination.current_page;

        console.log('🖼️ Gallery images array:', this.galleryImages);
        console.log('📈 Gallery images count:', this.galleryImages.length);
        console.log('📄 Pagination info:', this.galleryPagination);

        // Debug CSS grid after data loads
        this.$nextTick(() => {
          console.log('🎨 Debugging CSS Grid...');
          const gridElement = document.querySelector('.gallery-grid');
          if (gridElement) {
            const computedStyle = window.getComputedStyle(gridElement);
            console.log('✅ CSS Grid found! Computed styles:');
            console.log('- grid-template-columns:', computedStyle.gridTemplateColumns);
            console.log('- gap:', computedStyle.gap);
            console.log('- width:', computedStyle.width);
            console.log('- display:', computedStyle.display);
            console.log('- grid items count:', gridElement.children.length);
          } else {
            console.log('❌ .gallery-grid element not found!');
          }
        });
      } catch (error) {
        console.error('💥 Failed to load gallery images from API:', error);
        this.galleryError = error.message || 'Failed to load gallery images';

        // Load from localStorage if available (fallback for offline mode)
        const savedGallery = localStorage.getItem('admin_gallery');
        if (savedGallery) {
          const allImages = JSON.parse(savedGallery);
          // Simulate pagination for localStorage data
          const start = (currentPage - 1) * this.galleryItemsPerPage;
          const end = start + this.galleryItemsPerPage;
          this.galleryImages = allImages.slice(start, end);

          this.galleryPagination = {
            current_page: currentPage,
            per_page: this.galleryItemsPerPage,
            total_count: allImages.length,
            total_pages: Math.ceil(allImages.length / this.galleryItemsPerPage),
            has_next: currentPage < Math.ceil(allImages.length / this.galleryItemsPerPage),
            has_previous: currentPage > 1
          };

          console.log('📦 Loaded saved gallery from localStorage:', this.galleryImages.length, 'items');
          this.showNotification('Loaded saved gallery from local storage', 'info');
        } else {
          console.log('❌ No saved gallery data found, using empty array');
          this.galleryImages = [];
          this.galleryPagination = {
            current_page: 1,
            per_page: this.galleryItemsPerPage,
            total_count: 0,
            total_pages: 0,
            has_next: false,
            has_previous: false
          };
          this.showNotification('No gallery data available - please add images', 'warning');
        }
      } finally {
        this.galleryLoading = false;
      }
    },
    
    async loadBookings() {
      try {
        // Load from API
        const response = await apiService.getBookings();
        console.log('Loaded bookings from API:', response);

        // Extract bookings array from response
        this.bookings = response.bookings || response || [];
        console.log('Bookings array:', this.bookings);
      } catch (error) {
        console.error('Failed to load bookings from API:', error);
        this.bookings = [];
        this.showNotification('Failed to load bookings from server', 'error');
      }
    },


    
    async loadUsers() {
      try {
        // Load users from API
        const response = await apiService.getUsers();
        this.users = response.users || response || [];
      } catch (error) {
        console.error('Failed to load users:', error);
        this.users = [];
        this.showNotification('Failed to load users from server', 'error');
      }
    },
    
    updateDashboardStats() {
      // Debug logging
      console.log('updateDashboardStats called');
      console.log('this.services:', this.services, 'Type:', typeof this.services, 'IsArray:', Array.isArray(this.services));
      console.log('this.bookings:', this.bookings, 'Type:', typeof this.bookings, 'IsArray:', Array.isArray(this.bookings));
      console.log('this.galleryImages:', this.galleryImages, 'Type:', typeof this.galleryImages, 'IsArray:', Array.isArray(this.galleryImages));
      console.log('this.users:', this.users, 'Type:', typeof this.users, 'IsArray:', Array.isArray(this.users));

      // Ensure arrays are properly initialized
      const services = Array.isArray(this.services) ? this.services : [];
      const bookings = Array.isArray(this.bookings) ? this.bookings : [];
      const galleryImages = Array.isArray(this.galleryImages) ? this.galleryImages : [];
      const users = Array.isArray(this.users) ? this.users : [];

      console.log('Processed arrays - services:', services.length, 'bookings:', bookings.length);

      this.dashboardStats = {
        totalBookings: bookings.length,
        activeServices: services.filter(s => s.is_active).length,
        galleryImages: galleryImages.length,
        registeredUsers: users.length
      };

      console.log('Dashboard stats updated:', this.dashboardStats);
    },
    
    // Service management
    getEmptyServiceForm() {
      return {
        name: '',
        type: 'ACCOMMODATION',
        description: '',
        price: 0,
        child_price: null,
        capacity: 10,
        open_time: '08:00',
        close_time: '18:00',
        is_active: true,
        images: [],
        inclusions: [''],
        requirements: ['']
      };
    },

    getEmptyGalleryForm() {
      return {
        title: '',
        description: '',
        image_url: '',
        alt_text: '',
        category: 'general',
        display_order: 1,
        is_active: true
      };
    },
    
    openServiceModal(serviceId = null) {
      if (serviceId) {
        const service = this.services.find(s => s.id === serviceId);
        if (service) {
          this.serviceForm = { ...service };
          // Ensure images is always an array
          if (!this.serviceForm.images) {
            this.serviceForm.images = service.image ? [service.image] : [];
          }

          // Parse inclusions and requirements if they are strings
          if (typeof this.serviceForm.inclusions === 'string') {
            try {
              this.serviceForm.inclusions = JSON.parse(this.serviceForm.inclusions);
            } catch (e) {
              this.serviceForm.inclusions = [];
            }
          }
          if (!this.serviceForm.inclusions || this.serviceForm.inclusions.length === 0) {
            this.serviceForm.inclusions = [''];
          }

          if (typeof this.serviceForm.requirements === 'string') {
            try {
              this.serviceForm.requirements = JSON.parse(this.serviceForm.requirements);
            } catch (e) {
              this.serviceForm.requirements = [];
            }
          }
          if (!this.serviceForm.requirements || this.serviceForm.requirements.length === 0) {
            this.serviceForm.requirements = [''];
          }

          this.editingService = serviceId;
        }
      } else {
        this.serviceForm = this.getEmptyServiceForm();
        this.editingService = null;
      }
      this.showServiceModal = true;
    },
    
    closeServiceModal() {
      this.showServiceModal = false;
      this.serviceForm = this.getEmptyServiceForm();
      this.editingService = null;
      this.serviceLoading = false;
      // Clear service image upload data
      this.serviceImageFiles = [];
      this.serviceImagePreviews = [];
    },
    
    editService(serviceId) {
      this.openServiceModal(serviceId);
    },

    // Service image upload methods
    handleServiceImagesSelect(event) {
      const files = Array.from(event.target.files);
      files.forEach(file => {
        if (file && file.type.startsWith('image/')) {
          this.serviceImageFiles.push(file);

          const reader = new FileReader();
          reader.onload = (e) => {
            this.serviceImagePreviews.push(e.target.result);
          };
          reader.readAsDataURL(file);
        }
      });
    },

    removeServiceImagePreview(index) {
      this.serviceImageFiles.splice(index, 1);
      this.serviceImagePreviews.splice(index, 1);
    },

    removeExistingServiceImage(index) {
      this.serviceForm.images.splice(index, 1);
    },

    // Inclusions management
    addInclusion() {
      this.serviceForm.inclusions.push('');
    },

    removeInclusion(index) {
      if (this.serviceForm.inclusions.length > 1) {
        this.serviceForm.inclusions.splice(index, 1);
      }
    },

    // Requirements management
    addRequirement() {
      this.serviceForm.requirements.push('');
    },

    removeRequirement(index) {
      if (this.serviceForm.requirements.length > 1) {
        this.serviceForm.requirements.splice(index, 1);
      }
    },

    getImageUrl(imageUrl) {
      if (!imageUrl) return '';
      if (imageUrl.startsWith('http')) {
        return imageUrl;
      }
      // If already has /assets/uploads/ prefix, return as is
      if (imageUrl.startsWith('/assets/uploads/')) {
        return imageUrl;
      }
      // Otherwise add the prefix
      return `/assets/uploads/${imageUrl}`;
    },

    // Helper function to create slug from service name
    createServiceSlug(serviceName) {
      return serviceName
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
        .replace(/\s+/g, '-') // Replace spaces with hyphens
        .replace(/-+/g, '-') // Replace multiple hyphens with single
        .trim('-'); // Remove leading/trailing hyphens
    },

    // Helper function to delete old image file
    async deleteOldImage(imagePath) {
      if (!imagePath) return;
      try {
        // Extract filename from path
        const filename = imagePath.split('/').pop();
        if (filename && !filename.includes('placeholder')) {
          await apiService.deleteImage(filename);
        }
      } catch (error) {
        console.warn('Failed to delete old image:', error);
        // Don't throw error, just log warning
      }
    },
    
    async deleteService(serviceId) {
      if (!confirm('Are you sure you want to delete this service?')) {
        return;
      }

      try {
        // Call API to delete service
        await apiService.deleteService(serviceId);
        this.showNotification('Service deleted successfully', 'success');

        // Reload services from API to reflect changes
        await this.loadServices();
        this.updateDashboardStats();
      } catch (error) {
        console.error('Error deleting service:', error);
        this.showNotification('Failed to delete service', 'error');
      }
    },
    
    async saveService() {
      this.serviceLoading = true;
      try {
        // Handle multiple images upload with proper naming
        if (this.serviceImageFiles.length > 0) {
          try {
            // Delete old images if updating existing service
            if (this.editingService && this.serviceForm.images && this.serviceForm.images.length > 0) {
              for (const oldImage of this.serviceForm.images) {
                await this.deleteOldImage(oldImage);
              }
            }

            const serviceSlug = this.createServiceSlug(this.serviceForm.name);
            const renamedFiles = [];

            console.log('=== SERVICE IMAGES UPLOAD DEBUG ===');
            console.log('Service name:', this.serviceForm.name);
            console.log('Service slug:', serviceSlug);
            console.log('Number of files:', this.serviceImageFiles.length);

            // Create renamed files with service-based naming
            this.serviceImageFiles.forEach((file, index) => {
              const fileExtension = file.name.split('.').pop();
              const customFilename = `${serviceSlug}-${index + 1}.${fileExtension}`;

              console.log(`File ${index + 1}:`, file.name, '->', customFilename);

              const renamedFile = new File([file], customFilename, {
                type: file.type
              });
              renamedFiles.push(renamedFile);
            });

            const uploadResponse = await apiService.uploadImages(renamedFiles);
            if (uploadResponse.files && uploadResponse.files.length > 0) {
              // Set filenames array for the service
              this.serviceForm.images = uploadResponse.files.map(file => file.filename);
              console.log('Set service images to filenames:', this.serviceForm.images);
              this.showNotification(`${uploadResponse.files.length} images uploaded successfully`, 'success');
            }
          } catch (uploadError) {
            console.error('Failed to upload service images:', uploadError);
            this.showNotification('Failed to upload images', 'error');
            // Continue with save even if upload fails
          }
        }

        // Prepare service data with proper formatting
        const serviceData = { ...this.serviceForm };

        // Filter out empty inclusions and requirements
        serviceData.inclusions = this.serviceForm.inclusions.filter(item => item.trim() !== '');
        serviceData.requirements = this.serviceForm.requirements.filter(item => item.trim() !== '');

        // Call API to save service
        if (this.editingService) {
          // Update existing service
          await apiService.updateService(this.editingService, serviceData);
          this.showNotification('Service updated successfully', 'success');
        } else {
          // Create new service - ensure all required fields are present
          const newServiceData = {
            ...serviceData,
            capacity: serviceData.capacity || 10,
            open_time: serviceData.open_time || '08:00',
            close_time: serviceData.close_time || '18:00'
          };

          await apiService.createService(newServiceData);
          this.showNotification('Service created successfully', 'success');
        }

        // Reload services from API
        await this.loadServices();

        this.closeServiceModal();
        this.updateDashboardStats();
      } catch (error) {
        console.error('Error saving service:', error);
        this.showNotification(error.message || `Failed to ${this.editingService ? 'update' : 'create'} service`, 'error');
      } finally {
        this.serviceLoading = false;
      }
    },
    
    // Gallery management
    openUploadModal() {
      this.showUploadModal = true;
    },

    closeUploadModal() {
      this.showUploadModal = false;
      this.selectedFiles = [];
      this.imagePreviews = [];
      this.uploadLoading = false;
    },

    // Cleanup orphaned images
    async cleanupOrphanedImages() {
      if (!confirm('Bạn có chắc chắn muốn xóa tất cả ảnh không sử dụng? Hành động này không thể hoàn tác.')) {
        return;
      }

      try {
        this.uploadLoading = true;
        const result = await this.api.cleanupOrphanedImages();

        alert(`Đã dọn dẹp thành công!\nSố ảnh đã xóa: ${result.deleted_count}\nDanh sách: ${result.deleted_files.join(', ')}`);

        console.log('Cleanup result:', result);
      } catch (error) {
        console.error('Cleanup error:', error);
        alert('Lỗi khi dọn dẹp ảnh: ' + error.message);
      } finally {
        this.uploadLoading = false;
      }
    },
    
    handleFileSelect(event) {
      const files = Array.from(event.target.files);
      this.selectedFiles = files;
      this.imagePreviews = [];
      
      files.forEach((file, index) => {
        if (file.type.startsWith('image/')) {
          const reader = new FileReader();
          reader.onload = (e) => {
            this.imagePreviews.push(e.target.result);
          };
          reader.readAsDataURL(file);
        }
      });
    },
    
    removePreview(index) {
      this.selectedFiles.splice(index, 1);
      this.imagePreviews.splice(index, 1);
    },
    
    async uploadImages() {
      if (!this.selectedFiles || this.selectedFiles.length === 0) {
        this.showNotification('Please select files to upload', 'error');
        return;
      }

      this.uploadLoading = true;
      try {
        // Upload to server
        console.log('=== UPLOAD DEBUG ===');
        console.log('Calling apiService.uploadImages with files:', this.selectedFiles);
        const response = await apiService.uploadImages(this.selectedFiles);
        console.log('Upload response:', response);

        // Create gallery items in database for each uploaded file
        for (const file of response.files) {
          const galleryData = {
            title: file.original_name.replace(/\.[^/.]+$/, ""), // Remove extension
            description: `Uploaded image: ${file.original_name}`,
            image_url: file.url,
            alt_text: file.original_name
          };

          try {
            console.log('Creating gallery item:', galleryData);
            const galleryResponse = await apiService.createGalleryItem(galleryData);
            console.log('Gallery item created:', galleryResponse);
          } catch (error) {
            console.error('Failed to create gallery item:', error);
          }
        }

        // Reload gallery from API
        await this.loadGalleryImages();

        this.showNotification(`${response.count} image(s) uploaded successfully`, 'success');

        this.closeUploadModal();
        this.updateDashboardStats();
      } catch (error) {
        console.error('Upload error:', error);
        this.showNotification(error.message || 'Failed to upload images', 'error');
      } finally {
        this.uploadLoading = false;
      }
    },

    openGalleryEditModal(galleryId = null) {
      if (galleryId) {
        const galleryItem = this.galleryImages.find(g => g.id === galleryId);
        if (galleryItem) {
          this.galleryForm = { ...galleryItem };
          this.editingGalleryItem = galleryId;
        }
      } else {
        this.galleryForm = this.getEmptyGalleryForm();
        this.editingGalleryItem = null;
      }
      this.showGalleryEditModal = true;
    },

    closeGalleryEditModal() {
      this.showGalleryEditModal = false;
      this.galleryForm = this.getEmptyGalleryForm();
      this.editingGalleryItem = null;
      this.galleryLoading = false;
      this.galleryImageUploading = false;
    },

    // Auto-generate alt text from title
    updateAltText() {
      if (this.galleryForm.title && !this.editingGalleryItem) {
        this.galleryForm.alt_text = this.galleryForm.title;
      }
    },

    // Handle gallery image upload
    async handleGalleryImageUpload(event) {
      const file = event.target.files[0];
      if (!file) return;

      if (!file.type.startsWith('image/')) {
        this.showNotification('Please select an image file', 'error');
        return;
      }

      this.galleryImageUploading = true;
      try {
        console.log('=== GALLERY IMAGE UPLOAD ===');
        console.log('Uploading file:', file.name);

        const uploadResponse = await apiService.uploadImages([file]);
        console.log('Upload response:', uploadResponse);

        if (uploadResponse.files && uploadResponse.files.length > 0) {
          this.galleryForm.image_url = uploadResponse.files[0].url;
          this.showNotification('Image uploaded successfully', 'success');
        } else {
          throw new Error('No uploaded files returned');
        }
      } catch (error) {
        console.error('Failed to upload gallery image:', error);
        this.showNotification('Failed to upload image: ' + error.message, 'error');
      } finally {
        this.galleryImageUploading = false;
      }
    },

    // Remove gallery image
    removeGalleryImage() {
      this.galleryForm.image_url = '';
      // Reset file input
      if (this.$refs.galleryImageInput) {
        this.$refs.galleryImageInput.value = '';
      }
    },

    editGalleryImage(galleryId) {
      this.openGalleryEditModal(galleryId);
    },

    async deleteGalleryImage(galleryId) {
      console.log('🗑️ deleteGalleryImage called with ID:', galleryId);

      if (!confirm('Are you sure you want to delete this gallery image?')) {
        console.log('❌ User cancelled deletion');
        return;
      }

      try {
        console.log('🔄 Starting deletion process for gallery ID:', galleryId);

        // Call API to delete from database
        console.log('📡 Calling apiService.deleteGalleryItem...');
        const response = await apiService.deleteGalleryItem(galleryId);
        console.log('✅ API response:', response);

        // Reload gallery from API to get updated data
        console.log('🔄 Reloading gallery images...');
        await this.loadGalleryImages();
        console.log('✅ Gallery reloaded');

        this.showNotification('Gallery image deleted successfully', 'success');
        this.updateDashboardStats();
      } catch (error) {
        console.error('❌ Error deleting gallery image:', error);
        this.showNotification('Failed to delete gallery image: ' + error.message, 'error');
      }
    },

    async saveGalleryItem() {
      this.galleryLoading = true;
      try {
        console.log('=== SAVE GALLERY ITEM ===');
        console.log('Form data:', this.galleryForm);
        console.log('Editing item:', this.editingGalleryItem);

        if (this.editingGalleryItem) {
          // Update existing gallery item via API
          console.log('Updating gallery item via API...');
          const response = await apiService.updateGalleryItem(this.editingGalleryItem, {
            title: this.galleryForm.title,
            description: this.galleryForm.description,
            image_url: this.galleryForm.image_url,
            category: this.galleryForm.category || 'general',
            is_active: this.galleryForm.is_active
          });
          console.log('Update response:', response);
          this.showNotification('Gallery item updated successfully', 'success');
        } else {
          // Create new gallery item via API
          console.log('Creating new gallery item via API...');
          const response = await apiService.createGalleryItem({
            title: this.galleryForm.title,
            description: this.galleryForm.description,
            image_url: this.galleryForm.image_url,
            category: this.galleryForm.category || 'general'
          });
          console.log('Create response:', response);
          this.showNotification('Gallery item created successfully', 'success');
        }

        this.closeGalleryEditModal();

        // Reload gallery from API to get updated data
        console.log('Reloading gallery from API...');
        await this.loadGalleryImages();

        this.updateDashboardStats();
      } catch (error) {
        console.error('❌ Error saving gallery item:', error);
        this.showNotification(`Failed to ${this.editingGalleryItem ? 'update' : 'create'} gallery item: ${error.message}`, 'error');
      } finally {
        this.galleryLoading = false;
      }
    },
    
    viewImage(url) {
      window.open(url, '_blank');
    },
    
    async removeImage(imageId) {
      if (!confirm('Are you sure you want to remove this image?')) {
        return;
      }

      try {
        // Mock removal
        this.galleryImages = this.galleryImages.filter(img => img.id !== imageId);
        this.showNotification('Image removed successfully', 'success');
        this.updateDashboardStats();
      } catch (error) {
        this.showNotification('Failed to remove image', 'error');
      }
    },
    
    handleImageError(event) {
      // Suppress console spam
      const src = event.target.src;
      if (!window.loggedImageErrors) window.loggedImageErrors = new Set();
      if (!window.loggedImageErrors.has(src)) {
        console.warn('Admin image failed to load:', src);
        window.loggedImageErrors.add(src);
      }

      // Prevent infinite loop
      if (src.includes('placeholder') ||
          src.includes('data:image') ||
          event.target.dataset.errorHandled === 'true') {
        event.target.style.display = 'none';
        event.target.style.visibility = 'hidden';
        return;
      }

      // Mark as handled and use data URI
      event.target.dataset.errorHandled = 'true';
      event.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmMGYwIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIG5vdCBhdmFpbGFibGU8L3RleHQ+PC9zdmc+';
    },

    // Booking helper methods
    getServiceName(serviceId) {
      const service = this.services.find(s => s.id === serviceId);
      return service ? service.name : 'Unknown Service';
    },

    getServiceType(serviceId) {
      const service = this.services.find(s => s.id === serviceId);
      return service ? service.type : 'Unknown';
    },

    formatDateTime(dateTime) {
      if (!dateTime) return '';
      const date = new Date(dateTime);
      return date.toLocaleString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    },

    formatPrice(price) {
      if (!price) return '0 ₫';
      return new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
      }).format(price);
    },

    getNextStatusAction(currentStatus) {
      switch (currentStatus) {
        case 'PENDING': return 'Confirm';
        case 'CONFIRMED': return 'Complete';
        case 'COMPLETED': return 'Completed';
        case 'CANCELLED': return 'Cancelled';
        default: return 'Update';
      }
    },

    getNextStatusIcon(currentStatus) {
      switch (currentStatus) {
        case 'PENDING': return 'check';
        case 'CONFIRMED': return 'checkmark';
        case 'COMPLETED': return 'checkmark-circle';
        case 'CANCELLED': return 'close';
        default: return 'refresh';
      }
    },

    getNextStatus(currentStatus) {
      switch (currentStatus) {
        case 'PENDING': return 'CONFIRMED';
        case 'CONFIRMED': return 'COMPLETED';
        default: return null;
      }
    },

    viewBookingDetails(booking) {
      const details = `
Booking Details:
ID: ${booking.id}
Service: ${this.getServiceName(booking.service_id)}
Customer: ${booking.customer_name}
Phone: ${booking.customer_phone}
Booking Date: ${this.formatDate(booking.booking_date)}
${booking.check_in_date ? `Check-in: ${this.formatDateTime(booking.check_in_date)}` : ''}
${booking.check_out_date ? `Check-out: ${this.formatDateTime(booking.check_out_date)}` : ''}
Guests: ${booking.adults} Adults${booking.children_6_to_11 > 0 ? `, ${booking.children_6_to_11} Children (6-11)` : ''}${booking.children_under_6 > 0 ? `, ${booking.children_under_6} Children (Under 6)` : ''}
${booking.total_price ? `Total: ${this.formatPrice(booking.total_price)}` : ''}
${booking.deposit_amount ? `Deposit: ${this.formatPrice(booking.deposit_amount)}` : ''}
Status: ${booking.status}
Payment: ${booking.payment_status || 'PENDING'}
${booking.special_notes ? `Notes: ${booking.special_notes}` : ''}
Created: ${this.formatDateTime(booking.created_at)}
      `;
      alert(details);
    },

    async updateBookingStatus(booking) {
      const nextStatus = this.getNextStatus(booking.status);
      if (!nextStatus) return;

      if (!confirm(`Are you sure you want to ${nextStatus.toLowerCase()} this booking?`)) {
        return;
      }

      try {
        // Call API to update booking status
        const response = await fetch(`/api/v1/admin/bookings/${booking.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
          },
          body: JSON.stringify({ status: nextStatus })
        });

        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.error || 'Failed to update booking status');
        }

        const result = await response.json();

        // Update local state with server response
        booking.status = result.booking.status;
        booking.updated_at = result.booking.updated_at;
        this.showNotification(`Booking ${nextStatus.toLowerCase()} successfully`, 'success');
        this.updateDashboardStats();
      } catch (error) {
        console.error('Failed to update booking status:', error);
        this.showNotification(error.message || 'Failed to update booking status', 'error');
      }
    },

    // Booking management
    async editBooking(bookingId) {
      const booking = this.bookings.find(b => b.id === bookingId);
      if (booking) {
        const newStatus = prompt('Enter new status (PENDING, CONFIRMED, COMPLETED, CANCELLED):', booking.status);
        if (newStatus && ['PENDING', 'CONFIRMED', 'COMPLETED', 'CANCELLED'].includes(newStatus.toUpperCase())) {
          try {
            // Call API to update booking status
            const response = await fetch(`/api/v1/admin/bookings/${bookingId}`, {
              method: 'PUT',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
              },
              body: JSON.stringify({ status: newStatus.toUpperCase() })
            });

            if (!response.ok) {
              const error = await response.json();
              throw new Error(error.error || 'Failed to update booking status');
            }

            const result = await response.json();

            // Update local state with server response
            booking.status = result.booking.status;
            booking.updated_at = result.booking.updated_at;

            this.showNotification(`Booking status updated to ${newStatus}`, 'success');
            this.updateDashboardStats();
          } catch (error) {
            console.error('Failed to update booking status:', error);
            this.showNotification(error.message || 'Failed to update booking status', 'error');
          }
        } else if (newStatus) {
          this.showNotification('Invalid status. Use: PENDING, CONFIRMED, COMPLETED, or CANCELLED', 'error');
        }
      }
    },

    async cancelBooking(bookingId) {
      if (!confirm('Are you sure you want to cancel this booking?')) {
        return;
      }

      try {
        // Call API to cancel booking
        const response = await fetch(`/api/v1/admin/bookings/${bookingId}/cancel`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
          }
        });

        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.error || 'Failed to cancel booking');
        }

        // Update local state
        const booking = this.bookings.find(b => b.id === bookingId);
        if (booking) {
          booking.status = 'CANCELLED';
          booking.updated_at = new Date().toISOString();
        }

        this.showNotification('Booking cancelled successfully', 'success');
        this.updateDashboardStats();
      } catch (error) {
        console.error('Failed to cancel booking:', error);
        this.showNotification(error.message || 'Failed to cancel booking', 'error');
      }
    },

    async deleteBooking(bookingId) {
      if (!confirm('Are you sure you want to permanently delete this booking? This action cannot be undone.')) {
        return;
      }

      try {
        // Call API to delete booking
        const response = await fetch(`/api/v1/admin/bookings/${bookingId}`, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
          }
        });

        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.error || 'Failed to delete booking');
        }

        // Remove from local state
        this.bookings = this.bookings.filter(b => b.id !== bookingId);
        this.showNotification('Booking deleted permanently', 'success');
        this.updateDashboardStats();
      } catch (error) {
        console.error('Failed to delete booking:', error);
        this.showNotification(error.message || 'Failed to delete booking', 'error');
      }
    },

    // Gallery pagination methods
    async goToGalleryPage(page) {
      console.log(`📄 Going to gallery page ${page}`);
      await this.loadGalleryImages(page);
    },

    async nextGalleryPage() {
      if (this.galleryPagination.has_next) {
        const nextPage = this.galleryCurrentPage + 1;
        console.log(`📄 Next gallery page: ${nextPage}`);
        await this.loadGalleryImages(nextPage);
      }
    },

    async prevGalleryPage() {
      if (this.galleryPagination.has_previous) {
        const prevPage = this.galleryCurrentPage - 1;
        console.log(`📄 Previous gallery page: ${prevPage}`);
        await this.loadGalleryImages(prevPage);
      }
    },

    // User management
    openCreateUserModal() {
      this.editingUser = null;
      this.userForm = {
        email: '',
        password: '',
        first_name: '',
        last_name: '',
        phone: '',
        role: '',
        changePassword: false
      };
      this.userFormError = null;
      this.showUserModal = true;
    },

    openEditUserModal(user) {
      this.editingUser = user;
      this.userForm = {
        email: user.email,
        password: '',
        first_name: user.first_name || '',
        last_name: user.last_name || '',
        phone: user.phone || '',
        role: user.role,
        changePassword: false
      };
      this.userFormError = null;
      this.showUserModal = true;
    },

    closeUserModal() {
      this.showUserModal = false;
      this.editingUser = null;
      this.userForm = {
        email: '',
        password: '',
        first_name: '',
        last_name: '',
        phone: '',
        role: '',
        changePassword: false
      };
      this.userFormError = null;
    },

    async handleUserSubmit() {
      this.userFormLoading = true;
      this.userFormError = null;

      try {
        if (this.editingUser) {
          // Update user
          const updateData = {
            email: this.userForm.email,
            first_name: this.userForm.first_name,
            last_name: this.userForm.last_name,
            phone: this.userForm.phone,
            role: this.userForm.role
          };

          // Only include password if changing
          if (this.userForm.changePassword && this.userForm.password) {
            updateData.password = this.userForm.password;
          }

          const response = await apiService.updateUser(this.editingUser.id, updateData);

          // Update user in local array
          const userIndex = this.users.findIndex(u => u.id === this.editingUser.id);
          if (userIndex !== -1) {
            this.users[userIndex] = response.user;
          }

          this.showNotification('User updated successfully', 'success');
        } else {
          // Create user
          const response = await apiService.createUser({
            email: this.userForm.email,
            password: this.userForm.password,
            first_name: this.userForm.first_name,
            last_name: this.userForm.last_name,
            phone: this.userForm.phone,
            role: this.userForm.role
          });

          // Add new user to local array
          this.users.unshift(response.user);
          this.updateDashboardStats();

          this.showNotification('User created successfully', 'success');
        }

        this.closeUserModal();
      } catch (error) {
        console.error('User operation failed:', error);
        this.userFormError = error.message || 'Operation failed. Please try again.';
      } finally {
        this.userFormLoading = false;
      }
    },

    async confirmDeleteUser(user) {
      if (user.role === 'admin') {
        this.showNotification('Cannot delete admin users', 'error');
        return;
      }

      const confirmed = confirm(`Are you sure you want to delete user "${user.first_name} ${user.last_name}" (${user.email})?\n\nThis action cannot be undone.`);
      if (!confirmed) return;

      try {
        await apiService.deleteUser(user.id);

        // Remove user from local array
        this.users = this.users.filter(u => u.id !== user.id);
        this.updateDashboardStats();

        this.showNotification('User deleted successfully', 'success');
      } catch (error) {
        console.error('Failed to delete user:', error);
        this.showNotification('Failed to delete user. Please try again.', 'error');
      }
    },

    // Legacy methods (kept for compatibility)
    editUser(userId) {
      const user = this.users.find(u => u.id === userId);
      if (user) {
        this.openEditUserModal(user);
      }
    },

    deleteUser(userId) {
      const user = this.users.find(u => u.id === userId);
      if (user) {
        this.confirmDeleteUser(user);
      }
    },

    // Settings management
    async loadSettings() {
      try {
        const token = localStorage.getItem('auth_token');
        const response = await fetch('/api/v1/settings/', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (response.ok) {
          const data = await response.json();
          const settings = data.settings;

          // Update general settings
          this.generalSettings = {
            siteTitle: settings.general.site_title || '',
            contactEmail: settings.contact.email || '',
            contactPhone: settings.contact.phone || '',
            address: settings.contact.address || ''
          };

          // Update social links
          this.socialLinks = {
            instagram: settings.social_media.instagram || '',
            facebook: settings.social_media.facebook || ''
          };
        }
      } catch (error) {
        console.error('Failed to load settings:', error);
      }
    },

    async saveGeneralSettings() {
      this.settingsLoading = true;
      try {
        const token = localStorage.getItem('auth_token');
        const response = await fetch('/api/v1/admin/settings/', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({
            general: {
              site_title: this.generalSettings.siteTitle
            },
            contact: {
              email: this.generalSettings.contactEmail,
              phone: this.generalSettings.contactPhone,
              address: this.generalSettings.address
            }
          })
        });

        if (response.ok) {
          this.showNotification('Settings saved successfully', 'success');
        } else {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to save settings');
        }
      } catch (error) {
        this.showNotification('Failed to save settings: ' + error.message, 'error');
        console.error('Settings save error:', error);
      } finally {
        this.settingsLoading = false;
      }
    },
    
    async saveSocialLinks() {
      this.socialLoading = true;
      try {
        const token = localStorage.getItem('auth_token');
        const response = await fetch('/api/v1/admin/settings/', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({
            social_media: {
              instagram: this.socialLinks.instagram,
              facebook: this.socialLinks.facebook
            }
          })
        });

        if (response.ok) {
          this.showNotification('Social links saved successfully', 'success');
        } else {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to save social links');
        }
      } catch (error) {
        this.showNotification('Failed to save social links: ' + error.message, 'error');
        console.error('Social links save error:', error);
      } finally {
        this.socialLoading = false;
      }
    },

    // Homepage content management
    async loadHeroContent() {
      try {
        console.log('=== LOAD HERO CONTENT ===');
        const response = await apiService.getHomepageContent('hero');
        console.log('Hero content response:', response);

        this.heroContent = {
          title: response.title || '',
          subtitle: response.subtitle || '',
          content: response.content || '',
          image_url: response.image_url || '',
          images: response.images || []
        };
        console.log('Hero content loaded:', this.heroContent);
      } catch (error) {
        console.error('❌ Failed to load hero content:', error);
        this.showNotification('Failed to load hero content: ' + error.message, 'error');
      }
    },

    async saveHeroContent() {
      this.heroContentLoading = true;
      try {
        console.log('=== SAVE HERO CONTENT ===');
        console.log('Hero content data:', this.heroContent);

        await apiService.updateHomepageContent('hero', this.heroContent);
        this.showNotification('Hero content updated successfully', 'success');
      } catch (error) {
        console.error('❌ Failed to save hero content:', error);
        this.showNotification('Failed to save hero content: ' + error.message, 'error');
      } finally {
        this.heroContentLoading = false;
      }
    },

    async handleHeroImagesUpload(event) {
      const files = Array.from(event.target.files);
      if (!files.length) return;

      // Validate file types
      const invalidFiles = files.filter(file => !file.type.startsWith('image/'));
      if (invalidFiles.length > 0) {
        this.showNotification('Please select only image files', 'error');
        return;
      }

      try {
        this.heroContentLoading = true;

        // Upload images using the dedicated upload method
        const uploadResponse = await apiService.uploadImages(files);

        if (uploadResponse.files && uploadResponse.files.length > 0) {
          // Initialize images array if it doesn't exist
          if (!this.heroContent.images) {
            this.heroContent.images = [];
          }

          // Add new images to the array
          const newImages = uploadResponse.files.map(file => file.url);
          this.heroContent.images.push(...newImages);

          this.showNotification(`${uploadResponse.files.length} image(s) uploaded successfully`, 'success');
        } else {
          throw new Error('No uploaded files returned');
        }
      } catch (error) {
        console.error('Failed to upload hero images:', error);
        this.showNotification('Failed to upload images', 'error');
      } finally {
        this.heroContentLoading = false;
        // Clear the input
        event.target.value = '';
      }
    },

    removeHeroImage(index) {
      if (this.heroContent.images && index >= 0 && index < this.heroContent.images.length) {
        this.heroContent.images.splice(index, 1);
        this.showNotification('Image removed', 'info');
      }
    },

    moveHeroImage(index, direction) {
      if (!this.heroContent.images || this.heroContent.images.length < 2) return;

      const newIndex = index + direction;
      if (newIndex < 0 || newIndex >= this.heroContent.images.length) return;

      // Swap images
      const temp = this.heroContent.images[index];
      this.heroContent.images[index] = this.heroContent.images[newIndex];
      this.heroContent.images[newIndex] = temp;

      this.showNotification('Image order updated', 'info');
    },

    // About Us content management
    async loadAboutContent() {
      try {
        const response = await apiService.get('/content/homepage/about');
        const metadata = response.metadata ? JSON.parse(response.metadata) : {};

        this.aboutContent = {
          title: response.title || '',
          subtitle: response.subtitle || '',
          content: response.content || '',
          large_image: response.image_url || metadata.large_image || '',
          large_image2: metadata.large_image2 || '',
          small_image: metadata.small_image || ''
        };

        console.log('About content loaded:', this.aboutContent);
      } catch (error) {
        console.error('Failed to load about content:', error);
        this.showNotification('Failed to load about content', 'error');
      }
    },

    async saveAboutContent() {
      this.aboutContentLoading = true;
      try {
        // Ensure all images are properly stored in metadata
        const metadata = {
          small_image: this.aboutContent.small_image || '',
          large_image: this.aboutContent.large_image || '',
          large_image2: this.aboutContent.large_image2 || ''
        };

        const payload = {
          title: this.aboutContent.title || '',
          subtitle: this.aboutContent.subtitle || '',
          content: this.aboutContent.content || '',
          image_url: this.aboutContent.large_image || '',
          metadata: JSON.stringify(metadata)
        };

        console.log('=== SAVE ABOUT CONTENT ===');
        console.log('About content payload:', payload);
        await apiService.updateHomepageContent('about', payload);
        this.showNotification('About content updated successfully', 'success');

        // Reload content to ensure sync
        await this.loadAboutContent();
      } catch (error) {
        console.error('Failed to save about content:', error);
        this.showNotification('Failed to save about content', 'error');
      } finally {
        this.aboutContentLoading = false;
      }
    },

    async handleAboutLargeImageUpload(event) {
      const file = event.target.files[0];
      if (!file) return;

      if (!file.type.startsWith('image/')) {
        this.showNotification('Please select an image file', 'error');
        return;
      }

      try {
        this.aboutContentLoading = true;
        const uploadResponse = await apiService.uploadImages([file]);

        if (uploadResponse.files && uploadResponse.files.length > 0) {
          this.aboutContent.large_image = uploadResponse.files[0].url;
          this.showNotification('Large image uploaded successfully', 'success');
        } else {
          throw new Error('No uploaded files returned');
        }
      } catch (error) {
        console.error('Failed to upload large image:', error);
        this.showNotification('Failed to upload image', 'error');
      } finally {
        this.aboutContentLoading = false;
      }
    },

    async handleAboutLargeImage2Upload(event) {
      const file = event.target.files[0];
      if (!file) return;

      if (!file.type.startsWith('image/')) {
        this.showNotification('Please select an image file', 'error');
        return;
      }

      try {
        this.aboutContentLoading = true;
        const uploadResponse = await apiService.uploadImages([file]);

        if (uploadResponse.files && uploadResponse.files.length > 0) {
          this.aboutContent.large_image2 = uploadResponse.files[0].url;
          this.showNotification('Large image 2 uploaded successfully', 'success');
        } else {
          throw new Error('No uploaded files returned');
        }
      } catch (error) {
        console.error('Failed to upload large image 2:', error);
        this.showNotification('Failed to upload image', 'error');
      } finally {
        this.aboutContentLoading = false;
      }
    },

    async handleAboutSmallImageUpload(event) {
      const file = event.target.files[0];
      if (!file) return;

      if (!file.type.startsWith('image/')) {
        this.showNotification('Please select an image file', 'error');
        return;
      }

      try {
        this.aboutContentLoading = true;
        const uploadResponse = await apiService.uploadImages([file]);

        if (uploadResponse.files && uploadResponse.files.length > 0) {
          this.aboutContent.small_image = uploadResponse.files[0].url;
          this.showNotification('Small image uploaded successfully', 'success');
        } else {
          throw new Error('No uploaded files returned');
        }
      } catch (error) {
        console.error('Failed to upload small image:', error);
        this.showNotification('Failed to upload image', 'error');
      } finally {
        this.aboutContentLoading = false;
      }
    },

    removeAboutLargeImage() {
      this.aboutContent.large_image = '';
      this.showNotification('Large image removed', 'info');
    },

    removeAboutLargeImage2() {
      this.aboutContent.large_image2 = '';
      this.showNotification('Large image 2 removed', 'info');
    },

    removeAboutSmallImage() {
      this.aboutContent.small_image = '';
      this.showNotification('Small image removed', 'info');
    },

    // Booking Policy content management
    async loadBookingPolicyContent() {
      try {
        console.log('=== LOAD BOOKING POLICY CONTENT ===');
        const response = await apiService.getHomepageContent('booking-policy');
        console.log('Booking policy response:', response);

        this.bookingPolicyContent = {
          title: response.title || 'Booking & Cancellation Policy',
          subtitle: response.subtitle || 'Important information about reservations',
          content: response.content || ''
        };
        console.log('Booking policy loaded:', this.bookingPolicyContent);
      } catch (error) {
        console.error('❌ Failed to load booking policy content:', error);
        // Set default content if not found
        this.bookingPolicyContent = {
          title: 'Booking & Cancellation Policy',
          subtitle: 'Important information about reservations',
          content: `<div class="policy-section">
            <h4>For Accommodation Services</h4>

            <div class="policy-item">
                <h5>Check-in/Check-out Times:</h5>
                <ul>
                    <li>Check-in: 4:00 PM</li>
                    <li>Check-out: 10:00 AM</li>
                </ul>
            </div>

            <div class="policy-item">
                <h5>Advance Booking Policy:</h5>
                <ul>
                    <li>Please book at least 7 days in advance for large groups</li>
                    <li>At least 1 day in advance for individual travelers</li>
                    <li>50% deposit required to secure your reservation</li>
                </ul>
            </div>

            <div class="policy-item">
                <h5>Cancellation Policy:</h5>
                <ul>
                    <li>Cancel before 48 hours: Full deposit refund</li>
                    <li>Cancel after 48 hours: No deposit refund</li>
                </ul>
            </div>

            <div class="policy-item">
                <h5>Children's Policy:</h5>
                <ul>
                    <li>Under 6 years old: Free of charge</li>
                    <li>6 to 11 years old: 50% of adult rate</li>
                    <li>12 years and above: Full adult rate applies</li>
                </ul>
            </div>
        </div>`
        };
      }
    },

    async saveBookingPolicyContent() {
      this.bookingPolicyContentLoading = true;
      try {
        console.log('=== SAVE BOOKING POLICY CONTENT ===');
        console.log('Booking policy data:', this.bookingPolicyContent);

        await apiService.updateHomepageContent('booking-policy', this.bookingPolicyContent);
        this.showNotification('Booking policy content updated successfully', 'success');
      } catch (error) {
        console.error('❌ Failed to save booking policy content:', error);
        this.showNotification('Failed to save booking policy content: ' + error.message, 'error');
      } finally {
        this.bookingPolicyContentLoading = false;
      }
    },

    // Notes content management
    async loadNotesContent() {
      try {
        console.log('=== LOAD NOTES CONTENT ===');
        const response = await apiService.getHomepageContent('notes');
        console.log('Notes response:', response);

        this.notesContent = {
          title: response.title || 'Important Notes',
          subtitle: response.subtitle || 'Please read before your visit',
          content: response.content || ''
        };
        console.log('Notes loaded:', this.notesContent);
      } catch (error) {
        console.error('❌ Failed to load notes content:', error);
        // Set empty content if not found
        this.notesContent = {
          title: '',
          subtitle: '',
          content: ''
        };
      }
    },

    async saveNotesContent() {
      this.notesContentLoading = true;
      try {
        console.log('=== SAVE NOTES CONTENT ===');
        console.log('Notes data:', this.notesContent);

        await apiService.updateHomepageContent('notes', this.notesContent);
        this.showNotification('Notes content updated successfully', 'success');
      } catch (error) {
        console.error('❌ Failed to save notes content:', error);
        this.showNotification('Failed to save notes content: ' + error.message, 'error');
      } finally {
        this.notesContentLoading = false;
      }
    },

    // Notification system
    showNotification(message, type = 'info') {
      this.notification = {
        show: true,
        message: message,
        type: type
      };

      // Auto hide after 3 seconds
      setTimeout(() => {
        this.notification.show = false;
      }, 3000);
    },

    // Menu Management Methods
    async loadMenus() {
      try {
        this.menusLoading = true;
        this.menusError = null;

        const response = await apiService.get('/admin/menus');
        this.menus = response.menus || [];

        console.log('Loaded menus:', this.menus);
      } catch (error) {
        console.error('Error loading menus:', error);
        this.menusError = 'Failed to load menus. Please try again.';
        this.menus = [];
      } finally {
        this.menusLoading = false;
      }
    },

    filterMenusByCategory(category) {
      this.currentMenuCategory = category;
    },

    openMenuModal(menuId = null) {
      if (menuId) {
        const menu = this.menus.find(m => m.id === menuId);
        if (menu) {
          this.menuForm = { ...menu };
          this.editingMenu = menuId;
        }
      } else {
        this.menuForm = {
          title: '',
          description: '',
          category: 'restaurant',
          image_url: '',
          alt_text: '',
          menu_image: '',
          display_order: 0,
          is_active: true
        };
        this.editingMenu = null;
      }
      this.showMenuModal = true;
    },

    closeMenuModal() {
      this.showMenuModal = false;
      this.editingMenu = null;
      this.menuForm = {
        title: '',
        description: '',
        category: 'restaurant',
        image_url: '',
        alt_text: '',
        menu_image: '',
        display_order: 0,
        is_active: true
      };
    },

    editMenu(menuId) {
      this.openMenuModal(menuId);
    },

    async saveMenu() {
      this.menuLoading = true;
      try {
        console.log('=== SAVE MENU ===');
        console.log('Menu form data:', this.menuForm);
        console.log('Editing menu:', this.editingMenu);

        const menuData = {
          title: this.menuForm.title,
          description: this.menuForm.description,
          category: this.menuForm.category,
          image_url: this.menuForm.image_url || '',
          alt_text: this.menuForm.alt_text || '',
          menu_image: this.menuForm.menu_image || ''
        };

        if (this.editingMenu) {
          // Update existing menu
          await apiService.updateMenu(this.editingMenu, menuData);
          this.showNotification('Menu item updated successfully', 'success');
        } else {
          // Create new menu
          await apiService.createMenu(menuData);
          this.showNotification('Menu item created successfully', 'success');
        }

        await this.loadMenus();
        this.closeMenuModal();
      } catch (error) {
        console.error('Error saving menu:', error);
        this.showNotification('Failed to save menu item', 'error');
      } finally {
        this.menuLoading = false;
      }
    },

    async deleteMenu(menuId, menuName) {
      if (!confirm(`Are you sure you want to delete "${menuName}"? This action cannot be undone.`)) {
        return;
      }

      try {
        await apiService.deleteMenu(menuId);
        this.showNotification('Menu item deleted successfully', 'success');
        await this.loadMenus();
      } catch (error) {
        console.error('Error deleting menu:', error);
        this.showNotification('Failed to delete menu item', 'error');
      }
    },

    formatPrice(price) {
      if (!price) return 'N/A';
      return new Intl.NumberFormat('vi-VN').format(price) + ' VND';
    },

    async handleMenuImageUpload(event) {
      const file = event.target.files[0];
      if (!file) return;

      // Validate file type
      if (!file.type.startsWith('image/')) {
        this.showNotification('Please select an image file', 'error');
        return;
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        this.showNotification('Image size must be less than 5MB', 'error');
        return;
      }

      try {
        this.menuImageUploading = true;

        // Use menu-specific upload method
        const response = await apiService.uploadMenuImages([file]);

        console.log('Menu upload response:', response);

        // Set the image URL in the form (take first uploaded image)
        if (response.urls && response.urls.length > 0) {
          this.menuForm.image_url = response.urls[0];
          this.menuForm.alt_text = this.menuForm.alt_text || this.menuForm.title;
          this.showNotification('Image uploaded successfully', 'success');
        } else if (response.files && response.files.length > 0) {
          // Fallback for different response format
          this.menuForm.image_url = response.files[0].url;
          this.menuForm.alt_text = this.menuForm.alt_text || this.menuForm.title;
          this.showNotification('Image uploaded successfully', 'success');
        } else {
          throw new Error('No image URL returned');
        }
      } catch (error) {
        console.error('Error uploading image:', error);
        this.showNotification('Failed to upload image', 'error');
      } finally {
        this.menuImageUploading = false;
        // Reset file input
        event.target.value = '';
      }
    },

    removeMenuImage() {
      this.menuForm.image_url = '';
      this.menuForm.alt_text = '';
    },

    async handleMenuFullImageUpload(event) {
      const file = event.target.files[0];
      if (!file) return;

      // Validate file type
      if (!file.type.startsWith('image/')) {
        this.showNotification('Please select an image file', 'error');
        return;
      }

      // Validate file size (5MB limit)
      if (file.size > 5 * 1024 * 1024) {
        this.showNotification('Image size must be less than 5MB', 'error');
        return;
      }

      try {
        this.menuFullImageUploading = true;

        // Use menu-specific upload method
        const response = await apiService.uploadMenuImages([file]);

        console.log('Menu full image upload response:', response);

        // Set the menu image URL in the form (take first uploaded image)
        if (response.urls && response.urls.length > 0) {
          this.menuForm.menu_image = response.urls[0];
          this.showNotification('Full menu image uploaded successfully', 'success');
        } else {
          throw new Error('No URL returned from upload');
        }
      } catch (error) {
        console.error('Error uploading full menu image:', error);
        this.showNotification('Failed to upload full menu image', 'error');
      } finally {
        this.menuFullImageUploading = false;
        // Reset file input
        event.target.value = '';
      }
    },

    removeMenuFullImage() {
      this.menuForm.menu_image = '';
    },

    getCategoryDisplayName(category) {
      const categoryNames = {
        'restaurant': 'Restaurant',
        'accommodation': 'Accommodation',
        'afternoon_tea': 'Afternoon Tea'
      };
      return categoryNames[category] || category;
    }
  }
};
</script>

<style scoped>
/* Admin Layout */
.admin-layout {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  width: 100%;
  height: 100vh;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Loading Screen */
.admin-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: #f8f9fa;
}

.loading-container {
  text-align: center;
  padding: 40px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e3e3e3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-container p {
  margin: 0;
  color: #666;
  font-size: 16px;
}

/* Login Styles */
.admin-login {
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  position: fixed;
  top: 0;
  left: 0;
  overflow: hidden;
  box-sizing: border-box;
}

.admin-login::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.03)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.03)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.03)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

.login-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 50px 40px;
  border-radius: 20px;
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.2);
  width: 100%;
  max-width: 450px;
  min-width: 320px;
  position: relative;
  z-index: 1;
  margin: auto;
  box-sizing: border-box;
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
}

.login-header h1 {
  color: #2d3748;
  margin-bottom: 12px;
  font-size: 32px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.login-header p {
  color: #718096;
  font-size: 16px;
  font-weight: 500;
  margin: 0;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
  position: relative;
}

.form-group label {
  font-weight: 600;
  color: #4a5568;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.form-group input {
  padding: 16px 20px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 16px;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.8);
}

.form-group input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
  background: rgba(255, 255, 255, 1);
  transform: translateY(-2px);
}

.form-group input:disabled {
  background-color: #f8f9fa;
  cursor: not-allowed;
}

.btn-login {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 18px 32px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 10px;
  text-transform: uppercase;
  letter-spacing: 1px;
  position: relative;
  overflow: hidden;
}

.btn-login::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-login:hover:not(:disabled)::before {
  left: 100%;
}

.btn-login:hover:not(:disabled) {
  transform: translateY(-3px);
  box-shadow: 0 12px 30px rgba(102, 126, 234, 0.4);
}

.btn-login:active:not(:disabled) {
  transform: translateY(-1px);
}

.btn-login:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.change-password-modal {
  max-width: 500px;
  width: 90%;
}

.change-password-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.error-message {
  background: linear-gradient(135deg, #fed7d7 0%, #feb2b2 100%);
  color: #c53030;
  padding: 16px 20px;
  border-radius: 12px;
  border: 1px solid #fc8181;
  font-size: 14px;
  text-align: center;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(197, 48, 48, 0.15);
}

/* Admin Layout Styles */
.admin-authenticated {
  display: flex;
  min-height: 100vh;
  width: 100%;
  position: relative;
}

.sidebar {
  width: 250px;
  background: #2d3748;
  color: white;
  display: flex;
  flex-direction: column;
  position: fixed;
  left: 0;
  top: 0;
  height: 100vh;
  overflow-y: auto;
  z-index: 1000;
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid #4a5568;
}

.sidebar-header h1 {
  font-size: 20px;
  margin-bottom: 4px;
  color: white;
}

.sidebar-header p {
  font-size: 14px;
  color: #a0aec0;
  margin: 0;
}

.sidebar-nav ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar-nav li {
  border-bottom: 1px solid #4a5568;
}

.sidebar-nav a {
  display: block;
  padding: 16px 20px;
  color: #e2e8f0;
  text-decoration: none;
  transition: background-color 0.2s ease;
}

.sidebar-nav a:hover,
.sidebar-nav a.active {
  background-color: #4a5568;
  color: white;
}

.sidebar-footer {
  margin-top: auto;
  padding: 20px;
  border-top: 1px solid #4a5568;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.btn-change-password-sidebar {
  width: 100%;
  background: #C4A962;
  color: white;
  border: none;
  padding: 12px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.btn-change-password-sidebar:hover {
  background: #B8A055;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(196, 169, 98, 0.3);
}

.btn-logout {
  width: 100%;
  background: #e53e3e;
  color: white;
  border: none;
  padding: 12px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.btn-logout:hover {
  background: #c53030;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(229, 62, 62, 0.3);
}

.main-content {
  flex: 1;
  background: #f7fafc;
  min-height: 100vh;
  margin-left: 250px;
  width: calc(100% - 250px);
  position: relative;
}

.content-header {
  background: white;
  padding: 20px 30px;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title h2 {
  margin: 0;
  color: #2d3748;
  font-size: 24px;
}

.admin-name {
  color: #4a5568;
  font-size: 14px;
}

.content-section {
  padding: 30px;
  overflow: visible;
  height: auto;
  min-height: auto;
}

/* Dashboard Welcome */
.dashboard-welcome {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
  border-left: 4px solid #667eea;
}

.dashboard-welcome h3 {
  margin: 0 0 10px 0;
  color: #2d3748;
  font-size: 24px;
}

.dashboard-welcome p {
  margin: 0;
  color: #718096;
  font-size: 16px;
}

/* Dashboard Stats */
.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}



.stat-card {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #667eea;
}

.stat-card h3 {
  margin: 0 0 8px 0;
  color: #4a5568;
  font-size: 14px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  color: #2d3748;
  margin: 0 0 4px 0;
}

.stat-label {
  color: #718096;
  font-size: 12px;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .admin-login {
    padding: 15px;
  }

  .login-container {
    padding: 40px 30px;
    max-width: 100%;
    margin: 0;
    border-radius: 15px;
  }

  .login-header h1 {
    font-size: 28px;
  }

  .form-group input {
    padding: 14px 18px;
  }

  .btn-login {
    padding: 16px 28px;
    font-size: 15px;
  }

  .admin-authenticated {
    flex-direction: column;
  }

  .sidebar {
    position: relative;
    width: 100%;
    height: auto;
  }

  .main-content {
    margin-left: 0;
    width: 100%;
  }

  .dashboard-stats {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .stat-card {
    padding: 20px;
  }


}

@media (max-width: 480px) {
  .admin-login {
    padding: 10px;
  }

  .login-container {
    padding: 30px 20px;
    border-radius: 12px;
  }

  .login-header h1 {
    font-size: 24px;
  }

  .login-header p {
    font-size: 14px;
  }

  .form-group input {
    padding: 12px 16px;
    font-size: 15px;
  }

  .btn-login {
    padding: 14px 24px;
    font-size: 14px;
  }
}

/* Modal Styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  backdrop-filter: blur(4px);
}

.modal-content {
  background: white;
  border-radius: 12px;
  padding: 30px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
}

/* Admin Change Password Modal Styles */
.admin-change-password-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.75);
  backdrop-filter: blur(8px);
  z-index: 99999;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  animation: adminFadeIn 0.3s ease-out;
}

@keyframes adminFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.admin-change-password-modal {
  background: white;
  width: 100%;
  max-width: 520px;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  animation: adminSlideUp 0.3s ease-out;
}

@keyframes adminSlideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.admin-change-password-header {
  background: linear-gradient(135deg, #C4A962 0%, #B8A055 100%);
  color: white;
  padding: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.admin-header-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.admin-header-icon {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.admin-header-text h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  line-height: 1.2;
}

.admin-header-text p {
  margin: 4px 0 0 0;
  font-size: 14px;
  opacity: 0.9;
  line-height: 1.3;
}

.admin-close-button {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.admin-close-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.admin-change-password-content {
  padding: 32px;
}

.admin-password-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.admin-form-field {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.admin-field-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.admin-field-label svg {
  color: #6b7280;
}

.admin-field-input {
  width: 100%;
  padding: 14px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 10px;
  font-size: 15px;
  transition: all 0.2s ease;
  background: #fafafa;
  box-sizing: border-box;
}

.admin-field-input:focus {
  outline: none;
  border-color: #C4A962;
  background: white;
  box-shadow: 0 0 0 3px rgba(196, 169, 98, 0.1);
}

.admin-field-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: #f3f4f6;
}

.admin-password-hint {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
}

.admin-password-hint svg {
  flex-shrink: 0;
}

.admin-error-alert {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 14px 16px;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 10px;
  color: #dc2626;
  font-size: 14px;
  font-weight: 500;
}

.admin-error-alert svg {
  flex-shrink: 0;
  color: #dc2626;
}

.admin-form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 8px;
}

.admin-btn-secondary {
  padding: 12px 24px;
  background: #f3f4f6;
  color: #374151;
  border: 2px solid #e5e7eb;
  border-radius: 10px;
  cursor: pointer;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.2s ease;
}

.admin-btn-secondary:hover:not(:disabled) {
  background: #e5e7eb;
  border-color: #d1d5db;
}

.admin-btn-secondary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.admin-btn-primary-action {
  padding: 12px 24px;
  background: linear-gradient(135deg, #C4A962 0%, #B8A055 100%);
  color: white;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 160px;
  justify-content: center;
}

.admin-btn-primary-action:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 8px 25px rgba(196, 169, 98, 0.3);
}

.admin-btn-primary-action:disabled {
  opacity: 0.8;
  cursor: not-allowed;
  transform: none;
}

.admin-loading-icon {
  animation: adminSpin 1s linear infinite;
}

@keyframes adminSpin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.btn-edit,
.btn-delete {
  padding: 8px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  width: 32px;
  height: 32px;
}

.btn-edit {
  background: #3b82f6;
  color: white;
}

.btn-edit:hover {
  background: #2563eb;
  transform: scale(1.05);
}

.btn-delete {
  background: #ef4444;
  color: white;
}

.btn-delete:hover:not(:disabled) {
  background: #dc2626;
  transform: scale(1.05);
}

.btn-delete:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  opacity: 0.6;
}

/* Section Header Actions */
.section-header-actions {
  display: flex;
  gap: 16px;
  align-items: center;
}

.section-header-actions .search-box {
  margin: 0;
}

/* Checkbox Label */
.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #374151;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: #C4A962;
}

/* Mobile Responsive for Admin Modal */
@media (max-width: 640px) {
  .admin-change-password-overlay {
    padding: 16px;
  }

  .admin-change-password-header {
    padding: 20px;
  }

  .admin-header-icon {
    width: 40px;
    height: 40px;
  }

  .admin-header-text h3 {
    font-size: 18px;
  }

  .admin-change-password-content {
    padding: 24px;
  }

  .admin-form-actions {
    flex-direction: column-reverse;
  }

  .admin-btn-secondary,
  .admin-btn-primary-action {
    width: 100%;
    justify-content: center;
  }

  .section-header-actions {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .action-buttons {
    justify-content: center;
  }
}

.modal-content h3 {
  margin: 0 0 25px 0;
  color: #2d3748;
  font-size: 24px;
  font-weight: 600;
}

.close {
  position: absolute;
  top: 15px;
  right: 20px;
  font-size: 28px;
  font-weight: bold;
  color: #718096;
  cursor: pointer;
  transition: color 0.2s ease;
}

.close:hover {
  color: #2d3748;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: #4a5568;
  font-weight: 600;
  font-size: 14px;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s ease;
  box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group textarea {
  min-height: 100px;
  resize: vertical;
}

/* Dynamic List Styles */
.dynamic-list {
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  padding: 15px;
  background: #f8f9fa;
}

.list-item {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
  align-items: center;
}

.list-item input {
  flex: 1;
  margin-bottom: 0;
}

.btn-remove {
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  cursor: pointer;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.3s ease;
}

.btn-remove:hover {
  background: #c82333;
}

.btn-add {
  background: #28a745;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.3s ease;
}

.btn-add:hover {
  background: #218838;
}

.toggle-switch {
  display: flex;
  align-items: center;
  gap: 10px;
}

.toggle-switch input[type="checkbox"] {
  width: auto;
  margin: 0;
}

/* Gallery Styles */
.header-actions {
  display: flex;
  gap: 10px;
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  margin-top: 20px;
  grid-auto-rows: minmax(200px, auto);
}

/* Responsive gallery grid */
@media (min-width: 1400px) {
  .gallery-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }
}

@media (max-width: 1200px) {
  .gallery-grid {
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  }
}

@media (max-width: 768px) {
  .gallery-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
  }
}

@media (max-width: 480px) {
  .gallery-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 10px;
  }
}

/* Gallery Pagination Styles */
.pagination-container {
  margin-top: 30px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.pagination-info {
  color: #64748b;
  font-size: 14px;
  font-weight: 500;
}

.pagination-controls {
  display: flex;
  gap: 8px;
  align-items: center;
}

.pagination-btn {
  padding: 8px 16px;
  border: 2px solid #e2e8f0;
  background: white;
  color: #475569;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  min-width: 40px;
}

.pagination-btn:hover:not(:disabled) {
  border-color: #667eea;
  color: #667eea;
  transform: translateY(-1px);
}

.pagination-btn.active {
  background: #667eea;
  border-color: #667eea;
  color: white;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

@media (max-width: 768px) {
  .pagination-container {
    flex-direction: column;
    text-align: center;
  }

  .pagination-controls {
    flex-wrap: wrap;
    justify-content: center;
  }
}

.gallery-admin-item {
  position: relative;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.gallery-admin-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.gallery-admin-item img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.gallery-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: white;
  padding: 20px;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.gallery-admin-item:hover .gallery-overlay {
  transform: translateY(0);
}

.gallery-info h4 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
}

.gallery-info p {
  margin: 0 0 10px 0;
  font-size: 14px;
  opacity: 0.9;
}

.gallery-info .status {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  background: #e53e3e;
}

.gallery-info .status.active {
  background: #38a169;
}

.gallery-actions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.btn-edit,
.btn-delete {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.btn-edit {
  background: #667eea;
  color: white;
}

.btn-edit:hover {
  background: #5a67d8;
}

.btn-delete {
  background: #e53e3e;
  color: white;
}

.btn-delete:hover {
  background: #c53030;
}

.btn-secondary {
  background: #718096;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.btn-secondary:hover {
  background: #4a5568;
}

/* Services Admin Styles */
.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 24px;
  margin-top: 20px;
}

.service-admin-card {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid #e2e8f0;
  position: relative;
}

.service-admin-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  border-color: #C4A962;
}

.service-admin-header {
  background: linear-gradient(135deg, #C4A962 0%, #B8A055 100%);
  padding: 20px 24px;
  color: white;
  position: relative;
}

.service-admin-header h4 {
  margin: 0 0 8px 0;
  font-size: 1.25rem;
  font-weight: 700;
  line-height: 1.3;
}

.service-type-badge {
  display: inline-block;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  backdrop-filter: blur(10px);
}

.service-admin-body {
  padding: 24px;
}

.service-admin-body p {
  color: #4a5568;
  line-height: 1.6;
  margin: 0 0 20px 0;
  font-size: 0.95rem;
}

.service-description-text {
  white-space: pre-wrap;
  word-wrap: break-word;
  max-height: 120px;
  overflow-y: auto;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.service-price-info {
  background: #f7fafc;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 20px;
  border: 1px solid #e2e8f0;
}

.price-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.price-item:last-child {
  margin-bottom: 0;
}

.price-item span {
  color: #718096;
  font-size: 0.9rem;
  font-weight: 500;
}

.price-item strong {
  color: #C4A962;
  font-size: 1.1rem;
  font-weight: 700;
}

.service-status {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 20px;
  padding: 8px 12px;
  background: #f7fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.status-indicator.active {
  background: #38a169;
  box-shadow: 0 0 0 2px rgba(56, 161, 105, 0.2);
}

.status-indicator.inactive {
  background: #e53e3e;
  box-shadow: 0 0 0 2px rgba(229, 62, 62, 0.2);
}

.service-status span {
  font-size: 0.9rem;
  font-weight: 600;
  color: #4a5568;
}

.service-actions {
  display: flex;
  gap: 12px;
  margin-top: 20px;
}

.service-actions .btn-edit,
.service-actions .btn-delete {
  flex: 1;
  padding: 10px 16px;
  font-size: 0.9rem;
  font-weight: 600;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.service-actions .btn-edit {
  background: #667eea;
  color: white;
  border: none;
}

.service-actions .btn-edit:hover {
  background: #5a67d8;
  transform: translateY(-1px);
}

.service-actions .btn-delete {
  background: #e53e3e;
  color: white;
  border: none;
}

.service-actions .btn-delete:hover {
  background: #c53030;
  transform: translateY(-1px);
}

/* Responsive Design for Services */
@media (max-width: 768px) {
  .services-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .service-admin-header {
    padding: 16px 20px;
  }

  .service-admin-header h4 {
    font-size: 1.1rem;
  }

  .service-admin-body {
    padding: 20px;
  }

  .service-actions {
    flex-direction: column;
  }

  .service-actions .btn-edit,
  .service-actions .btn-delete {
    flex: none;
  }
}

@media (max-width: 480px) {
  .services-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .service-admin-card {
    border-radius: 12px;
  }

  .service-admin-header {
    padding: 14px 16px;
  }

  .service-admin-body {
    padding: 16px;
  }

  .price-item {
    font-size: 0.85rem;
  }
}

/* Homepage Content Styles */
.homepage-content-form {
  max-width: 800px;
}

.form-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.form-card h4 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 1.2rem;
  font-weight: 600;
}

.image-upload-section {
  border: 2px dashed #ddd;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  background: #fafafa;
}

.current-image {
  margin-bottom: 16px;
}

.current-image img {
  max-width: 100%;
  max-height: 200px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Hero Images Management */
.current-images {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.current-image-item {
  position: relative;
  border: 2px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
}

.current-image-item img {
  width: 100%;
  height: 150px;
  object-fit: cover;
  display: block;
}

.image-controls {
  position: absolute;
  top: 5px;
  right: 5px;
  display: flex;
  gap: 5px;
  flex-direction: column;
}

.btn-move {
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  padding: 5px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  min-width: 30px;
}

.btn-move:hover:not(:disabled) {
  background: rgba(0, 0, 0, 0.9);
}

.btn-move:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.upload-hint {
  font-size: 0.9rem;
  color: #666;
  margin-top: 5px;
  text-align: center;
}

.btn-remove {
  background: #dc3545;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  margin-top: 8px;
  cursor: pointer;
  font-size: 0.9rem;
}

.btn-remove:hover {
  background: #c82333;
}

.btn-upload {
  background: #007bff;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
}

.btn-upload:hover {
  background: #0056b3;
}

.form-actions {
  display: flex;
  gap: 12px;
  margin-top: 24px;
}

.form-actions .btn-primary,
.form-actions .btn-secondary {
  padding: 12px 24px;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  border: none;
}

.form-actions .btn-primary {
  background: #28a745;
  color: white;
}

.form-actions .btn-primary:hover {
  background: #218838;
}

.form-actions .btn-primary:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.form-actions .btn-secondary {
  background: #6c757d;
  color: white;
}

.form-actions .btn-secondary:hover {
  background: #5a6268;
}

/* About Us Enhanced Styles */
.about-content-container {
  display: grid;
  grid-template-columns: 1fr;
  gap: 30px;
  max-width: 1200px;
}

.about-content-form .form-card {
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border: 1px solid #e9ecef;
  box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.about-content-form .form-card h4 {
  color: #2c3e50;
  margin-bottom: 25px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.form-input, .form-textarea {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
  background: #ffffff;
}

.form-input:focus, .form-textarea:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
}

.form-group label {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-weight: 500;
  color: #495057;
}

/* Images Section */
.about-images-section .form-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
}

.images-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 25px;
}

.image-upload-card {
  background: #ffffff;
  border: 2px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
}

.image-upload-card:hover {
  border-color: #007bff;
  box-shadow: 0 8px 25px rgba(0,123,255,0.15);
}

.image-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.image-card-header h5 {
  margin: 0;
  color: #2c3e50;
  font-weight: 600;
}

.image-type-badge {
  background: #007bff;
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

.image-type-badge.secondary {
  background: #6c757d;
}

.image-upload-area {
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-placeholder {
  text-align: center;
  padding: 30px;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
}

.upload-placeholder:hover {
  border-color: #007bff;
  background: rgba(0,123,255,0.05);
}

.upload-icon {
  font-size: 48px;
  color: #6c757d;
  margin-bottom: 15px;
}

.upload-placeholder h6 {
  margin: 10px 0 5px 0;
  color: #495057;
  font-weight: 600;
}

.upload-placeholder p {
  margin: 0 0 10px 0;
  color: #6c757d;
  font-size: 14px;
}

.upload-hint {
  font-size: 12px;
  color: #adb5bd;
  font-style: italic;
}

.image-preview-card {
  width: 100%;
}

.image-preview-container {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 10px;
}

.image-preview-container img {
  width: 100%;
  height: 150px;
  object-fit: cover;
  display: block;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-preview-container:hover .image-overlay {
  opacity: 1;
}

.remove-btn, .edit-btn {
  background: rgba(255,255,255,0.9);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.remove-btn {
  color: #dc3545;
}

.edit-btn {
  color: #007bff;
}

.remove-btn:hover, .edit-btn:hover {
  background: white;
  transform: scale(1.1);
}

.image-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.image-name {
  font-weight: 500;
  color: #495057;
}

.image-status {
  background: #28a745;
  color: white;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 10px;
}

/* Preview Section */
.about-preview-section .form-card {
  background: linear-gradient(135deg, #e3f2fd 0%, #ffffff 100%);
  border: 1px solid #bbdefb;
}

.preview-container {
  background: #ffffff;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.about-preview {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 30px;
  align-items: center;
}

.preview-images {
  display: flex;
  flex-direction: column;
  gap: 15px;
  align-items: center;
}

.preview-small-image, .preview-large-image {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.preview-small-image {
  width: 120px;
  height: 90px;
}

.preview-large-image {
  width: 200px;
  height: 150px;
}

.preview-small-image img, .preview-large-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.preview-placeholder {
  width: 100%;
  height: 100%;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6c757d;
  font-size: 12px;
  border: 2px dashed #dee2e6;
}

.preview-content {
  padding: 20px;
}

.preview-subtitle {
  color: #6c757d;
  font-size: 14px;
  font-weight: 500;
  margin: 0 0 8px 0;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.preview-title {
  color: #2c3e50;
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 15px 0;
  line-height: 1.2;
}

.preview-description {
  color: #495057;
  font-size: 16px;
  line-height: 1.6;
  margin: 0;
}

/* Action Buttons */
.about-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-top: 30px;
}

.save-btn, .reset-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 30px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.save-btn {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(40,167,69,0.3);
}

.save-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(40,167,69,0.4);
}

.save-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.reset-btn {
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(108,117,125,0.3);
}

.reset-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(108,117,125,0.4);
}

/* Service Image Upload Styles */
.service-image-upload {
  margin-top: 10px;
}

.service-image-previews {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.service-preview-item {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.service-preview-item img {
  width: 100%;
  height: 80px;
  object-fit: cover;
  display: block;
}

.remove-preview-btn {
  position: absolute;
  top: 5px;
  right: 5px;
  background: rgba(255, 0, 0, 0.8);
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-preview-btn:hover {
  background: rgba(255, 0, 0, 1);
}

.existing-service-images {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #e0e0e0;
}

.existing-service-images h4 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 14px;
}

.existing-images-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 15px;
}

.existing-image-item {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.existing-image-item img {
  width: 100%;
  height: 80px;
  object-fit: cover;
  display: block;
}

.remove-existing-btn {
  position: absolute;
  top: 5px;
  right: 5px;
  background: rgba(255, 0, 0, 0.8);
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-existing-btn:hover {
  background: rgba(255, 0, 0, 1);
}

/* Bookings Section Styles */
.filter-controls {
  display: flex;
  gap: 15px;
  align-items: center;
  flex-wrap: wrap;
}

.filter-input,
.filter-select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.filter-input:focus,
.filter-select:focus {
  outline: none;
  border-color: #C4A962;
  box-shadow: 0 0 0 2px rgba(196, 169, 98, 0.1);
}

.search-input {
  min-width: 250px;
  flex: 1;
}

.bookings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.booking-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border-left: 4px solid #ddd;
}

.booking-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.booking-pending {
  border-left-color: #ffc107;
}

.booking-confirmed {
  border-left-color: #28a745;
}

.booking-completed {
  border-left-color: #17a2b8;
}

.booking-cancelled {
  border-left-color: #dc3545;
}

.booking-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.booking-id {
  display: flex;
  align-items: center;
  gap: 8px;
}

.id-label {
  font-size: 12px;
  color: #6c757d;
  text-transform: uppercase;
  font-weight: 600;
}

.id-value {
  font-family: monospace;
  font-weight: 600;
  color: #495057;
}

.booking-status {
  display: flex;
  gap: 8px;
  align-items: center;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-pending {
  background: #fff3cd;
  color: #856404;
}

.status-confirmed {
  background: #d4edda;
  color: #155724;
}

.status-completed {
  background: #d1ecf1;
  color: #0c5460;
}

.status-cancelled {
  background: #f8d7da;
  color: #721c24;
}

.payment-badge {
  padding: 3px 6px;
  border-radius: 8px;
  font-size: 10px;
  font-weight: 500;
  text-transform: uppercase;
}

.payment-pending {
  background: #e2e3e5;
  color: #6c757d;
}

.payment-partial {
  background: #fff3cd;
  color: #856404;
}

.payment-completed {
  background: #d4edda;
  color: #155724;
}

.booking-content {
  padding: 20px;
}

.booking-service h4 {
  margin: 0 0 4px 0;
  color: #212529;
  font-size: 16px;
  font-weight: 600;
}

.service-type {
  font-size: 12px;
  color: #6c757d;
  text-transform: uppercase;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.booking-customer,
.booking-dates,
.booking-guests,
.booking-pricing,
.booking-notes,
.booking-meta {
  margin-top: 16px;
}

.customer-info,
.customer-phone,
.booking-date,
.check-dates,
.guest-count,
.price-info,
.notes,
.created-date {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 14px;
  color: #495057;
}

.check-dates {
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
  margin-top: 8px;
}

.check-in,
.check-out {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: #6c757d;
}

.guest-count {
  flex-wrap: wrap;
  gap: 12px;
}

.adults,
.children {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #495057;
}

.total-price {
  font-weight: 600;
  color: #28a745;
}

.deposit {
  font-size: 13px;
  color: #6c757d;
}

.booking-actions {
  padding: 16px 20px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.btn-view,
.btn-edit,
.btn-status,
.btn-cancel,
.btn-delete,
.btn-confirm,
.btn-complete {
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.btn-view {
  background: #6c757d;
  color: white;
}

.btn-view:hover {
  background: #5a6268;
}

.btn-edit {
  background: #007bff;
  color: white;
}

.btn-edit:hover {
  background: #0056b3;
}

.btn-status,
.btn-confirm {
  background: #28a745;
  color: white;
}

.btn-status:hover,
.btn-confirm:hover {
  background: #1e7e34;
}

.btn-complete {
  background: #17a2b8;
  color: white;
}

.btn-complete:hover {
  background: #117a8b;
}

.btn-cancel {
  background: #dc3545;
  color: white;
}

.btn-cancel:hover {
  background: #c82333;
}

.btn-delete {
  background: #6f42c1;
  color: white;
}

.btn-delete:hover {
  background: #5a2d91;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6c757d;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state h4 {
  margin: 0 0 8px 0;
  color: #495057;
}

.empty-state p {
  margin: 0;
  font-size: 14px;
}

/* Icons (using text symbols for now) */
.icon-edit::before { content: "✏️"; }
.icon-title::before { content: "📝"; }
.icon-subtitle::before { content: "📄"; }
.icon-content::before { content: "📋"; }
.icon-image::before { content: "🖼️"; }
.icon-upload::before { content: "📤"; }
.icon-trash::before { content: "🗑️"; }
.icon-eye::before { content: "👁️"; }
.icon-save::before { content: "💾"; }
.icon-refresh::before { content: "🔄"; }
.icon-user::before { content: "👤"; }
.icon-phone::before { content: "📞"; }
.icon-calendar::before { content: "📅"; }
.icon-login::before { content: "🏠"; }
.icon-logout::before { content: "🚪"; }
.icon-users::before { content: "👥"; }
.icon-child::before { content: "👶"; }
.icon-baby::before { content: "🍼"; }
.icon-money::before { content: "💰"; }
.icon-note::before { content: "📝"; }
.icon-clock::before { content: "🕐"; }
.icon-check::before { content: "✅"; }
.icon-checkmark::before { content: "✓"; }
.icon-checkmark-circle::before { content: "✅"; }
.icon-close::before { content: "❌"; }
.icon-trash::before { content: "🗑️"; }
.icon-calendar-empty::before { content: "📅"; }

/* Responsive Design */
@media (max-width: 768px) {
  .images-grid {
    grid-template-columns: 1fr;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .about-preview {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .preview-images {
    flex-direction: row;
    justify-content: center;
  }

  .about-actions {
    flex-direction: column;
  }
}

/* Menu Management Styles */
.menu-categories {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.category-btn {
  padding: 8px 16px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: 500;
}

.category-btn:hover {
  border-color: #C4A962;
  background: #f8f9fa;
}

.category-btn.active {
  background: #C4A962;
  color: white;
  border-color: #C4A962;
}

.menu-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.menu-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: 1px solid #e2e8f0;
}

.menu-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.menu-card-header {
  padding: 16px;
  background: linear-gradient(135deg, #C4A962 0%, #B8A055 100%);
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.menu-card-header h4 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  line-height: 1.3;
}

.price {
  font-size: 16px;
  font-weight: 700;
  margin: 0;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-available {
  background: rgba(56, 161, 105, 0.2);
  color: #38a169;
  border: 1px solid #38a169;
}

.status-unavailable {
  background: rgba(229, 62, 62, 0.2);
  color: #e53e3e;
  border: 1px solid #e53e3e;
}

.description {
  padding: 0 16px;
  color: #4a5568;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 16px;
}

.menu-images {
  padding: 0 16px;
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.menu-image-thumb {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.no-images {
  padding: 0 16px;
  color: #a0aec0;
  font-size: 14px;
  font-style: italic;
  margin-bottom: 16px;
}

.menu-meta {
  padding: 0 16px;
  margin-bottom: 16px;
}

.menu-meta small {
  color: #718096;
  font-size: 12px;
}

.menu-actions {
  padding: 16px;
  display: flex;
  gap: 8px;
  border-top: 1px solid #e2e8f0;
  background: #f8f9fa;
}

.menu-actions .btn-edit,
.menu-actions .btn-delete {
  flex: 1;
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.menu-actions .btn-edit {
  background: #667eea;
  color: white;
}

.menu-actions .btn-edit:hover {
  background: #5a67d8;
  transform: translateY(-1px);
}

.menu-actions .btn-delete {
  background: #e53e3e;
  color: white;
}

.menu-actions .btn-delete:hover {
  background: #c53030;
  transform: translateY(-1px);
}

.field-description {
  font-size: 12px;
  color: #718096;
  margin: 5px 0 10px 0;
  line-height: 1.4;
  font-style: italic;
}

@media (max-width: 768px) {
  .menu-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .menu-categories {
    flex-direction: column;
    gap: 8px;
  }

  .category-btn {
    text-align: center;
  }

  .menu-actions {
    flex-direction: column;
  }
}
</style>
