<template>
  <div id="booking-manager-app" class="booking-manager-layout">
    <!-- Login Screen -->
    <div v-if="!isAuthenticated" class="manager-login">
      <div class="login-container">
        <div class="login-header">
          <h1>Booking Manager Login</h1>
          <p>Phong Nha Valley Booking Management</p>
        </div>

        <form @submit.prevent="handleLogin" class="login-form">
          <div class="form-group">
            <label for="email">Email</label>
            <input
              type="email"
              id="email"
              v-model="loginForm.email"
              required
              placeholder="<EMAIL>"
              :disabled="loginLoading"
            />
          </div>

          <div class="form-group">
            <label for="password">Password</label>
            <input
              type="password"
              id="password"
              v-model="loginForm.password"
              required
              placeholder="Enter your password"
              :disabled="loginLoading"
            />
          </div>

          <button type="submit" class="btn-login" :disabled="loginLoading">
            {{ loginLoading ? 'Logging in...' : 'Login' }}
          </button>



          <div v-if="loginError" class="error-message">
            {{ loginError }}
          </div>
        </form>
      </div>
    </div>



    <!-- Change Password Modal -->
    <div v-if="showChangePasswordModal" class="change-password-overlay" @click="closeChangePasswordModal">
      <div class="change-password-modal" @click.stop>
        <!-- Header -->
        <div class="change-password-header">
          <div class="header-content">
            <div class="header-icon">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 1L3 5V11C3 16.55 6.84 21.74 12 23C17.16 21.74 21 16.55 21 11V5L12 1Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M9 12L11 14L15 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>
            <div class="header-text">
              <h3>Change Password</h3>
              <p>Update your account security</p>
            </div>
          </div>
          <button class="close-button" @click="closeChangePasswordModal">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </button>
        </div>

        <!-- Content -->
        <div class="change-password-content">
          <form @submit.prevent="handleChangePassword" class="password-form">
            <!-- Current Password -->
            <div class="form-field">
              <label for="current_password" class="field-label">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <rect x="3" y="11" width="18" height="11" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                  <circle cx="12" cy="16" r="1" fill="currentColor"/>
                  <path d="M7 11V7a5 5 0 0 1 10 0v4" stroke="currentColor" stroke-width="2"/>
                </svg>
                Current Password
              </label>
              <input
                type="password"
                id="current_password"
                v-model="changePasswordForm.currentPassword"
                required
                placeholder="Enter your current password"
                :disabled="changePasswordLoading"
                class="field-input"
              />
            </div>

            <!-- New Password -->
            <div class="form-field">
              <label for="new_password" class="field-label">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <rect x="3" y="11" width="18" height="11" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                  <circle cx="12" cy="16" r="1" fill="currentColor"/>
                  <path d="M7 11V7a5 5 0 0 1 10 0v4" stroke="currentColor" stroke-width="2"/>
                </svg>
                New Password
              </label>
              <input
                type="password"
                id="new_password"
                v-model="changePasswordForm.newPassword"
                required
                placeholder="Enter new password (minimum 6 characters)"
                minlength="6"
                :disabled="changePasswordLoading"
                class="field-input"
              />
              <div class="password-hint">
                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                  <path d="M12 6v6l4 2" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                Password must be at least 6 characters long
              </div>
            </div>

            <!-- Confirm Password -->
            <div class="form-field">
              <label for="confirm_password" class="field-label">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M9 12L11 14L15 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  <rect x="3" y="11" width="18" height="11" rx="2" ry="2" stroke="currentColor" stroke-width="2"/>
                  <path d="M7 11V7a5 5 0 0 1 10 0v4" stroke="currentColor" stroke-width="2"/>
                </svg>
                Confirm New Password
              </label>
              <input
                type="password"
                id="confirm_password"
                v-model="changePasswordForm.confirmPassword"
                required
                placeholder="Confirm your new password"
                :disabled="changePasswordLoading"
                class="field-input"
              />
            </div>

            <!-- Error Message -->
            <div v-if="changePasswordError" class="error-alert">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
                <line x1="15" y1="9" x2="9" y2="15" stroke="currentColor" stroke-width="2"/>
                <line x1="9" y1="9" x2="15" y2="15" stroke="currentColor" stroke-width="2"/>
              </svg>
              {{ changePasswordError }}
            </div>

            <!-- Actions -->
            <div class="form-actions">
              <button
                type="button"
                @click="closeChangePasswordModal"
                :disabled="changePasswordLoading"
                class="btn-secondary"
              >
                Cancel
              </button>
              <button
                type="submit"
                :disabled="changePasswordLoading"
                class="btn-primary-action"
              >
                <svg v-if="changePasswordLoading" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="loading-icon">
                  <path d="M21 12a9 9 0 11-6.219-8.56" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                </svg>
                {{ changePasswordLoading ? 'Changing Password...' : 'Change Password' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- Booking Manager Panel -->
    <div v-else class="manager-panel">
      <!-- Header -->
      <header class="manager-header">
        <div class="header-left">
          <h1>📋 Booking Management</h1>
          <p>Manage all bookings and reservations</p>
        </div>
        <div class="header-right">
          <div class="user-info">
            <span>Welcome, {{ currentUser?.first_name || 'User' }}!</span>
            <button type="button" class="btn-change-password" @click="openChangePasswordModal">
              <i class="icon-key"></i>
              <span>Change Password</span>
            </button>
            <button @click="logout" class="btn-logout">Logout</button>
          </div>
        </div>
      </header>

      <!-- Stats Dashboard -->
      <div class="stats-section">
        <div class="stat-card">
          <div class="stat-icon">⏳</div>
          <div class="stat-content">
            <h3>{{ stats.pending }}</h3>
            <p>Pending Bookings</p>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">✅</div>
          <div class="stat-content">
            <h3>{{ stats.confirmed }}</h3>
            <p>Confirmed Bookings</p>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">🎯</div>
          <div class="stat-content">
            <h3>{{ stats.completed }}</h3>
            <p>Completed Bookings</p>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">📊</div>
          <div class="stat-content">
            <h3>{{ stats.total }}</h3>
            <p>Total Bookings</p>
          </div>
        </div>
      </div>

      <!-- Filters -->
      <div class="filters-section">
        <div class="filter-controls">
          <input 
            v-model="bookingDateFilter" 
            type="date" 
            placeholder="Filter by date"
            class="filter-input"
          >
          <select v-model="bookingStatusFilter" class="filter-select">
            <option value="">All Status</option>
            <option value="PENDING">Pending</option>
            <option value="CONFIRMED">Confirmed</option>
            <option value="COMPLETED">Completed</option>
            <option value="CANCELLED">Cancelled</option>
          </select>
          <input 
            v-model="bookingSearchTerm" 
            type="text" 
            placeholder="Search by customer name or phone..."
            class="filter-input search-input"
          >
          <button @click="refreshBookings" class="btn-refresh">
            <i class="icon-refresh"></i>
            Refresh
          </button>
        </div>
      </div>

      <!-- Bookings Grid -->
      <div class="bookings-section">
        <div class="bookings-grid">
          <div 
            v-for="booking in filteredBookings" 
            :key="booking.id" 
            class="booking-card"
            :class="`booking-${(booking.status || 'pending').toLowerCase()}`"
          >
            <div class="booking-header">
              <div class="booking-id">
                <span class="id-label">Booking #</span>
                <span class="id-value">{{ (booking.id || '').substring(0, 8) }}</span>
              </div>
              <div class="booking-status">
                <span 
                  class="status-badge" 
                  :class="`status-${(booking.status || 'pending').toLowerCase()}`"
                >
                  {{ booking.status || 'PENDING' }}
                </span>
                <span 
                  class="payment-badge" 
                  :class="`payment-${booking.payment_status ? booking.payment_status.toLowerCase() : 'pending'}`"
                >
                  {{ booking.payment_status || 'PENDING' }}
                </span>
              </div>
            </div>

            <div class="booking-content">
              <div class="booking-service">
                <h4>{{ getServiceName(booking.service_id) }}</h4>
                <span class="service-type">{{ getServiceType(booking.service_id) }}</span>
              </div>

              <div class="booking-customer">
                <div class="customer-info">
                  <i class="icon-user"></i>
                  <span>{{ booking.customer_name || 'N/A' }}</span>
                </div>
                <div class="customer-phone">
                  <i class="icon-phone"></i>
                  <span>{{ booking.customer_phone || 'N/A' }}</span>
                </div>
              </div>

              <div class="booking-dates">
                <div class="booking-date">
                  <i class="icon-calendar"></i>
                  <span>{{ booking.booking_date ? formatDate(booking.booking_date) : 'N/A' }}</span>
                </div>
                <div v-if="booking.check_in_date && booking.check_out_date" class="accommodation-dates">
                  <div class="check-dates">
                    <span class="check-in">
                      <i class="icon-login"></i>
                      Check-in: {{ formatDateTime(booking.check_in_date) }}
                    </span>
                    <span class="check-out">
                      <i class="icon-logout"></i>
                      Check-out: {{ formatDateTime(booking.check_out_date) }}
                    </span>
                  </div>
                </div>
              </div>

              <div class="booking-guests">
                <div class="guest-count">
                  <span class="adults">
                    <i class="icon-users"></i>
                    {{ booking.adults || 0 }} Adults
                  </span>
                  <span v-if="booking.children_6_to_11 > 0" class="children">
                    <i class="icon-child"></i>
                    {{ booking.children_6_to_11 }} Children (6-11)
                  </span>
                  <span v-if="booking.children_under_6 > 0" class="children">
                    <i class="icon-baby"></i>
                    {{ booking.children_under_6 }} Children (Under 6)
                  </span>
                </div>
              </div>

              <div v-if="booking.total_price" class="booking-pricing">
                <div class="price-info">
                  <span class="total-price">
                    <i class="icon-money"></i>
                    Total: {{ formatPrice(booking.total_price) }}
                  </span>
                  <span v-if="booking.deposit_amount" class="deposit">
                    Deposit: {{ formatPrice(booking.deposit_amount) }}
                  </span>
                </div>
              </div>

              <div v-if="booking.special_notes" class="booking-notes">
                <div class="notes">
                  <i class="icon-note"></i>
                  <span>{{ booking.special_notes }}</span>
                </div>
              </div>

              <div class="booking-meta">
                <span class="created-date">
                  <i class="icon-clock"></i>
                  Created: {{ booking.created_at ? formatDateTime(booking.created_at) : 'N/A' }}
                </span>
              </div>
            </div>

            <div class="booking-actions">
              <button class="btn-view" @click="viewBookingDetails(booking)">
                <i class="icon-eye"></i>
                View Details
              </button>
              <button 
                class="btn-status" 
                @click="updateBookingStatus(booking)"
                :class="`btn-${getNextStatusAction(booking.status).toLowerCase()}`"
                :disabled="!getNextStatus(booking.status)"
              >
                <i :class="`icon-${getNextStatusIcon(booking.status)}`"></i>
                {{ getNextStatusAction(booking.status) }}
              </button>
              <button
                v-if="booking.status !== 'CANCELLED'"
                class="btn-cancel"
                @click="cancelBooking(booking.id)"
              >
                <i class="icon-close"></i>
                Cancel
              </button>
              <button
                v-if="booking.status === 'COMPLETED' || booking.status === 'CANCELLED'"
                class="btn-delete"
                @click="deleteBooking(booking.id)"
              >
                <i class="icon-trash"></i>
                Delete
              </button>
            </div>
          </div>
        </div>

        <div v-if="filteredBookings.length === 0" class="empty-state">
          <i class="icon-calendar-empty"></i>
          <h4>No bookings found</h4>
          <p>No bookings match your current filters.</p>
        </div>
      </div>
    </div>

    <!-- Notification -->
    <div v-if="notification.show" class="notification" :class="notification.type">
      {{ notification.message }}
    </div>
  </div>
</template>

<script>
import apiService from '../utils/api.js';

export default {
  name: 'BookingManagerApp',
  data() {
    return {
      // Authentication
      isAuthenticated: false,
      currentUser: null,
      loginForm: {
        email: '',
        password: ''
      },
      loginLoading: false,
      loginError: null,

      // Change password
      showChangePasswordModal: false,
      changePasswordForm: {
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      changePasswordLoading: false,
      changePasswordError: null,

      // Bookings data
      bookings: [],
      services: [],
      
      // Filters
      bookingDateFilter: '',
      bookingStatusFilter: '',
      bookingSearchTerm: '',

      // Stats
      stats: {
        pending: 0,
        confirmed: 0,
        completed: 0,
        total: 0
      },

      // Notification
      notification: {
        show: false,
        message: '',
        type: 'success'
      }
    };
  },

  computed: {
    filteredBookings() {
      let filtered = this.bookings;

      if (this.bookingStatusFilter) {
        filtered = filtered.filter(booking => (booking.status || 'PENDING') === this.bookingStatusFilter);
      }

      if (this.bookingDateFilter) {
        filtered = filtered.filter(booking => {
          if (!booking.booking_date) return false;
          const bookingDate = new Date(booking.booking_date).toISOString().split('T')[0];
          return bookingDate === this.bookingDateFilter;
        });
      }

      if (this.bookingSearchTerm) {
        const searchTerm = this.bookingSearchTerm.toLowerCase();
        filtered = filtered.filter(booking =>
          (booking.customer_name || '').toLowerCase().includes(searchTerm) ||
          (booking.customer_phone || '').includes(searchTerm) ||
          (booking.id || '').toLowerCase().includes(searchTerm)
        );
      }

      return filtered.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
    }
  },

  async mounted() {
    await this.initializeBookingManager();
  },

  methods: {
    async initializeBookingManager() {
      try {
        // Check if user is already authenticated
        const token = localStorage.getItem('booking_manager_token');
        if (token) {
          // Set token in API service
          apiService.setToken(token);

          // Try to verify token
          console.log('Verifying booking manager token');
          const user = await this.verifyAuthToken(token);
          if (user && user.role === 'booking_manager') {
            this.currentUser = user;
            this.isAuthenticated = true;

            // Load booking data
            await Promise.all([
              this.loadBookings(),
              this.loadServices()
            ]);

            this.updateStats();
          } else {
            // Invalid token, clear and show login
            localStorage.removeItem('booking_manager_token');
            apiService.setToken(null);
            this.isAuthenticated = false;
            return;
          }
        } else {
          this.isAuthenticated = false;
        }
      } catch (error) {
        console.error('Failed to initialize booking manager:', error);
        localStorage.removeItem('booking_manager_token');
        apiService.setToken(null);
        this.isAuthenticated = false;
      }
    },

    async verifyAuthToken(token) {
      try {
        const response = await apiService.get('/admin/profile');
        return response.user || response;
      } catch (error) {
        console.error('Token verification failed:', error);
        return null;
      }
    },

    async handleLogin() {
      this.loginLoading = true;
      this.loginError = null;

      try {
        console.log('=== BOOKING MANAGER LOGIN DEBUG ===');
        console.log('Attempting API login for:', this.loginForm.email);
        const response = await apiService.login(this.loginForm.email, this.loginForm.password);
        console.log('API login response:', response);

        if (response.token && response.user) {
          // Check if user is booking manager
          if (response.user.role === 'booking_manager') {
            // Store token and user info
            localStorage.setItem('booking_manager_token', response.token);
            apiService.setToken(response.token);
            this.currentUser = response.user;
            this.isAuthenticated = true;

            // Load booking data
            await Promise.all([
              this.loadBookings(),
              this.loadServices()
            ]);

            this.updateStats();
            this.showNotification('Welcome to booking management panel!', 'success');
          } else {
            this.loginError = 'Access denied. Booking manager privileges required.';
          }
        } else {
          this.loginError = 'Invalid login response';
        }
      } catch (error) {
        console.error('Login failed:', error);
        this.loginError = error.response?.data?.error || 'Login failed. Please try again.';
      } finally {
        this.loginLoading = false;
      }
    },

    logout() {
      // Clear authentication state
      localStorage.removeItem('booking_manager_token');
      this.isAuthenticated = false;
      this.currentUser = null;
      this.loginForm = { email: '', password: '' };
      this.loginError = null;

      // Clear data
      this.bookings = [];
      this.services = [];

      this.showNotification('Logged out successfully', 'success');
    },

    async loadBookings() {
      try {
        console.log('Loading bookings...');
        const response = await apiService.get('/admin/bookings');
        this.bookings = response.bookings || [];
        console.log('Loaded bookings:', this.bookings.length);
      } catch (error) {
        console.error('Failed to load bookings:', error);
        this.showNotification('Failed to load bookings', 'error');
      }
    },

    async loadServices() {
      try {
        console.log('Loading services...');
        const response = await apiService.get('/services');
        this.services = response.services || [];
        console.log('Loaded services:', this.services.length);
      } catch (error) {
        console.error('Failed to load services:', error);
        this.showNotification('Failed to load services', 'error');
      }
    },

    async refreshBookings() {
      await this.loadBookings();
      this.updateStats();
      this.showNotification('Bookings refreshed', 'success');
    },

    updateStats() {
      this.stats = {
        pending: this.bookings.filter(b => b.status === 'PENDING').length,
        confirmed: this.bookings.filter(b => b.status === 'CONFIRMED').length,
        completed: this.bookings.filter(b => b.status === 'COMPLETED').length,
        total: this.bookings.length
      };
    },

    // Booking helper methods
    getServiceName(serviceId) {
      const service = this.services.find(s => s.id === serviceId);
      return service ? service.name : 'Unknown Service';
    },

    getServiceType(serviceId) {
      const service = this.services.find(s => s.id === serviceId);
      return service ? service.type : 'Unknown';
    },

    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    },

    formatDateTime(dateTime) {
      if (!dateTime) return '';
      const date = new Date(dateTime);
      return date.toLocaleString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    },

    formatPrice(price) {
      if (!price) return '0 ₫';
      return new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
      }).format(price);
    },

    getNextStatusAction(currentStatus) {
      switch (currentStatus) {
        case 'PENDING': return 'Confirm';
        case 'CONFIRMED': return 'Complete';
        case 'COMPLETED': return 'Completed';
        case 'CANCELLED': return 'Cancelled';
        default: return 'Update';
      }
    },

    getNextStatusIcon(currentStatus) {
      switch (currentStatus) {
        case 'PENDING': return 'check';
        case 'CONFIRMED': return 'checkmark';
        case 'COMPLETED': return 'checkmark-circle';
        case 'CANCELLED': return 'close';
        default: return 'refresh';
      }
    },

    getNextStatus(currentStatus) {
      switch (currentStatus) {
        case 'PENDING': return 'CONFIRMED';
        case 'CONFIRMED': return 'COMPLETED';
        default: return null;
      }
    },

    viewBookingDetails(booking) {
      const details = `
Booking Details:
ID: ${booking.id || 'N/A'}
Service: ${this.getServiceName(booking.service_id)}
Customer: ${booking.customer_name || 'N/A'}
Phone: ${booking.customer_phone || 'N/A'}
Booking Date: ${booking.booking_date ? this.formatDate(booking.booking_date) : 'N/A'}
${booking.check_in_date ? `Check-in: ${this.formatDateTime(booking.check_in_date)}` : ''}
${booking.check_out_date ? `Check-out: ${this.formatDateTime(booking.check_out_date)}` : ''}
Guests: ${booking.adults || 0} Adults${(booking.children_6_to_11 || 0) > 0 ? `, ${booking.children_6_to_11} Children (6-11)` : ''}${(booking.children_under_6 || 0) > 0 ? `, ${booking.children_under_6} Children (Under 6)` : ''}
${booking.total_price ? `Total: ${this.formatPrice(booking.total_price)}` : ''}
${booking.deposit_amount ? `Deposit: ${this.formatPrice(booking.deposit_amount)}` : ''}
Status: ${booking.status || 'PENDING'}
Payment: ${booking.payment_status || 'PENDING'}
${booking.special_notes ? `Notes: ${booking.special_notes}` : ''}
Created: ${booking.created_at ? this.formatDateTime(booking.created_at) : 'N/A'}
      `;
      alert(details);
    },

    async updateBookingStatus(booking) {
      const nextStatus = this.getNextStatus(booking.status);
      if (!nextStatus) return;

      if (!confirm(`Are you sure you want to ${nextStatus.toLowerCase()} this booking?`)) {
        return;
      }

      try {
        const token = localStorage.getItem('booking_manager_token');
        console.log('🔑 Using token for booking update:', token ? 'Token exists' : 'No token found');

        // Call API to update booking status
        const response = await apiService.put(`/admin/bookings/${booking.id}`, {
          status: nextStatus
        });

        const result = await response.json();

        // Update local state with server response
        booking.status = result.booking.status;
        booking.updated_at = result.booking.updated_at;
        this.updateStats();
        this.showNotification(`Booking ${nextStatus.toLowerCase()} successfully`, 'success');
      } catch (error) {
        console.error('Failed to update booking status:', error);
        this.showNotification(error.message || 'Failed to update booking status', 'error');
      }
    },

    async cancelBooking(bookingId) {
      if (!confirm('Are you sure you want to cancel this booking?')) {
        return;
      }

      try {
        // Call API to cancel booking
        const response = await apiService.put(`/admin/bookings/${bookingId}/cancel`, {});

        // Update local state
        const booking = this.bookings.find(b => b.id === bookingId);
        if (booking) {
          booking.status = 'CANCELLED';
          booking.updated_at = new Date().toISOString();
        }
        this.updateStats();
        this.showNotification('Booking cancelled successfully', 'success');
      } catch (error) {
        console.error('Failed to cancel booking:', error);
        this.showNotification(error.message || 'Failed to cancel booking', 'error');
      }
    },

    async deleteBooking(bookingId) {
      if (!confirm('Are you sure you want to permanently delete this booking? This action cannot be undone.')) {
        return;
      }

      try {
        // Call API to delete booking
        const response = await apiService.delete(`/admin/bookings/${bookingId}`);

        // Remove from local state
        this.bookings = this.bookings.filter(b => b.id !== bookingId);
        this.updateStats();
        this.showNotification('Booking deleted permanently', 'success');
      } catch (error) {
        console.error('Failed to delete booking:', error);
        this.showNotification(error.message || 'Failed to delete booking', 'error');
      }
    },

    showNotification(message, type = 'success') {
      this.notification = {
        show: true,
        message,
        type
      };

      setTimeout(() => {
        this.notification.show = false;
      }, 3000);
    },

    // Change Password Methods
    openChangePasswordModal() {
      this.showChangePasswordModal = true;
    },

    closeChangePasswordModal() {
      this.showChangePasswordModal = false;
      this.changePasswordForm = {
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      };
      this.changePasswordError = null;
      this.changePasswordLoading = false;
    },

    async handleChangePassword() {
      try {
        this.changePasswordLoading = true;
        this.changePasswordError = null;

        // Validate form
        if (!this.changePasswordForm.currentPassword) {
          this.changePasswordError = 'Current password is required';
          return;
        }

        if (!this.changePasswordForm.newPassword) {
          this.changePasswordError = 'New password is required';
          return;
        }

        if (this.changePasswordForm.newPassword.length < 6) {
          this.changePasswordError = 'New password must be at least 6 characters';
          return;
        }

        if (this.changePasswordForm.newPassword !== this.changePasswordForm.confirmPassword) {
          this.changePasswordError = 'New passwords do not match';
          return;
        }

        // Call API to change password
        const response = await apiService.changePassword(
          this.changePasswordForm.currentPassword,
          this.changePasswordForm.newPassword
        );

        // Success
        this.showNotification('Password changed successfully', 'success');
        this.closeChangePasswordModal();

      } catch (error) {
        console.error('Failed to change password:', error);
        this.changePasswordError = error.message || 'Failed to change password';
      } finally {
        this.changePasswordLoading = false;
      }
    }
  }
};
</script>

<style scoped>
/* Booking Manager Layout */
.booking-manager-layout {
  min-height: 100vh;
  background: #f8f9fa;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Login Styles */
.manager-login {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-container {
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  width: 100%;
  max-width: 400px;
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-header h1 {
  color: #333;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.login-header p {
  color: #666;
  margin: 0;
  font-size: 14px;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-group label {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.form-group input {
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  color: #333;
  background: white;
  transition: border-color 0.3s ease;
}

.form-group input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.form-group input::placeholder {
  color: #999;
  opacity: 1;
}

.btn-login {
  background: #667eea;
  color: white;
  border: none;
  padding: 12px;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.btn-login:hover:not(:disabled) {
  background: #5a6fd8;
}

.btn-login:disabled {
  background: #ccc;
  cursor: not-allowed;
}

/* Login Actions */
.login-actions {
  margin-top: 20px;
  text-align: center;
}

.btn-change-password {
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 2px 8px rgba(108, 117, 125, 0.2);
  position: relative;
  overflow: hidden;
}

.btn-change-password::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.btn-change-password:hover {
  background: linear-gradient(135deg, #5a6268 0%, #343a40 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(108, 117, 125, 0.3);
}

.btn-change-password:hover::before {
  left: 100%;
}

.btn-change-password:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(108, 117, 125, 0.2);
}

.btn-change-password .icon-key {
  font-size: 16px;
}

.error-message {
  color: #dc3545;
  font-size: 14px;
  text-align: center;
  padding: 10px;
  background: #f8d7da;
  border-radius: 4px;
  border: 1px solid #f5c6cb;
}

/* Change Password Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex !important;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(4px);
  animation: fadeIn 0.3s ease;
}

.modal {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  width: 100%;
  max-width: 450px;
  max-height: 90vh;
  overflow: visible;
  animation: slideUp 0.3s ease;
  position: relative;
  z-index: 10000;
  margin: 20px;
}

.modal-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 10001;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.modal-close {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.modal-close:hover {
  background: rgba(255, 255, 255, 0.2);
}

.modal-content {
  padding: 24px;
  background: white;
  position: relative;
  z-index: 10001;
}

.change-password-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.modal-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
}

/* Change Password Modal Styles */
.change-password-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.75);
  backdrop-filter: blur(8px);
  z-index: 99999;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.change-password-modal {
  background: white;
  width: 100%;
  max-width: 520px;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.change-password-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-icon {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.header-text h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  line-height: 1.2;
}

.header-text p {
  margin: 4px 0 0 0;
  font-size: 14px;
  opacity: 0.9;
  line-height: 1.3;
}

.close-button {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.change-password-content {
  padding: 32px;
}

.password-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.field-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.field-label svg {
  color: #6b7280;
}

.field-input {
  width: 100%;
  padding: 14px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 10px;
  font-size: 15px;
  transition: all 0.2s ease;
  background: #fafafa;
  box-sizing: border-box;
}

.field-input:focus {
  outline: none;
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.field-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: #f3f4f6;
}

.password-hint {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
}

.password-hint svg {
  flex-shrink: 0;
}

.error-alert {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 14px 16px;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 10px;
  color: #dc2626;
  font-size: 14px;
  font-weight: 500;
}

.error-alert svg {
  flex-shrink: 0;
  color: #dc2626;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 8px;
}

.btn-secondary {
  padding: 12px 24px;
  background: #f3f4f6;
  color: #374151;
  border: 2px solid #e5e7eb;
  border-radius: 10px;
  cursor: pointer;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.2s ease;
}

.btn-secondary:hover:not(:disabled) {
  background: #e5e7eb;
  border-color: #d1d5db;
}

.btn-secondary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary-action {
  padding: 12px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 160px;
  justify-content: center;
}

.btn-primary-action:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.btn-primary-action:disabled {
  opacity: 0.8;
  cursor: not-allowed;
  transform: none;
}

.loading-icon {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Mobile Responsive */
@media (max-width: 640px) {
  .change-password-overlay {
    padding: 16px;
  }

  .change-password-header {
    padding: 20px;
  }

  .header-icon {
    width: 40px;
    height: 40px;
  }

  .header-text h3 {
    font-size: 18px;
  }

  .change-password-content {
    padding: 24px;
  }

  .form-actions {
    flex-direction: column-reverse;
  }

  .btn-secondary,
  .btn-primary-action {
    width: 100%;
    justify-content: center;
  }
}

.modal-actions .btn-cancel {
  background: #6c757d;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.modal-actions .btn-cancel:hover:not(:disabled) {
  background: #5a6268;
}

.modal-actions .btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.modal-actions .btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4c93 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.modal-actions .btn-primary:disabled,
.modal-actions .btn-cancel:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Manager Panel */
.manager-panel {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* Header */
.manager-header {
  background: white;
  padding: 20px 30px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left h1 {
  margin: 0 0 4px 0;
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

.header-left p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.user-info span {
  color: #333;
  font-weight: 500;
}

.user-info .btn-change-password {
  background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  box-shadow: 0 1px 4px rgba(108, 117, 125, 0.2);
}

.user-info .btn-change-password:hover {
  background: linear-gradient(135deg, #5a6268 0%, #343a40 100%);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(108, 117, 125, 0.3);
}

.user-info .btn-change-password .icon-key {
  font-size: 12px;
}

.btn-logout {
  background: #dc3545;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.btn-logout:hover {
  background: #c82333;
}

/* Stats Section */
.stats-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  padding: 30px;
  background: #f8f9fa;
}

.stat-card {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  font-size: 32px;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 12px;
}

.stat-content h3 {
  margin: 0 0 4px 0;
  font-size: 28px;
  font-weight: 700;
  color: #333;
}

.stat-content p {
  margin: 0;
  color: #666;
  font-size: 14px;
  font-weight: 500;
}

/* Filters Section */
.filters-section {
  padding: 20px 30px;
  background: white;
  border-bottom: 1px solid #e9ecef;
}

.filter-controls {
  display: flex;
  gap: 15px;
  align-items: center;
  flex-wrap: wrap;
}

.filter-input,
.filter-select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.filter-input:focus,
.filter-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.search-input {
  min-width: 250px;
  flex: 1;
}

.btn-refresh {
  background: #28a745;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: background-color 0.3s ease;
}

.btn-refresh:hover {
  background: #218838;
}

/* Bookings Section */
.bookings-section {
  flex: 1;
  padding: 30px;
  background: #f8f9fa;
}

.bookings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.booking-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border-left: 4px solid #ddd;
}

.booking-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.booking-pending {
  border-left-color: #ffc107;
}

.booking-confirmed {
  border-left-color: #28a745;
}

.booking-completed {
  border-left-color: #17a2b8;
}

.booking-cancelled {
  border-left-color: #dc3545;
}

.booking-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.booking-id {
  display: flex;
  align-items: center;
  gap: 8px;
}

.id-label {
  font-size: 12px;
  color: #6c757d;
  text-transform: uppercase;
  font-weight: 600;
}

.id-value {
  font-family: monospace;
  font-weight: 600;
  color: #495057;
}

.booking-status {
  display: flex;
  gap: 8px;
  align-items: center;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-pending {
  background: #fff3cd;
  color: #856404;
}

.status-confirmed {
  background: #d4edda;
  color: #155724;
}

.status-completed {
  background: #d1ecf1;
  color: #0c5460;
}

.status-cancelled {
  background: #f8d7da;
  color: #721c24;
}

.payment-badge {
  padding: 3px 6px;
  border-radius: 8px;
  font-size: 10px;
  font-weight: 500;
  text-transform: uppercase;
}

.payment-pending {
  background: #e2e3e5;
  color: #6c757d;
}

.payment-partial {
  background: #fff3cd;
  color: #856404;
}

.payment-completed {
  background: #d4edda;
  color: #155724;
}

.booking-content {
  padding: 20px;
}

.booking-service h4 {
  margin: 0 0 4px 0;
  color: #212529;
  font-size: 16px;
  font-weight: 600;
}

.service-type {
  font-size: 12px;
  color: #6c757d;
  text-transform: uppercase;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.booking-customer,
.booking-dates,
.booking-guests,
.booking-pricing,
.booking-notes,
.booking-meta {
  margin-top: 16px;
}

.customer-info,
.customer-phone,
.booking-date,
.check-dates,
.guest-count,
.price-info,
.notes,
.created-date {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 14px;
  color: #495057;
}

.check-dates {
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
  margin-top: 8px;
}

.check-in,
.check-out {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: #6c757d;
}

.guest-count {
  flex-wrap: wrap;
  gap: 12px;
}

.adults,
.children {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #495057;
}

.total-price {
  font-weight: 600;
  color: #28a745;
}

.deposit {
  font-size: 13px;
  color: #6c757d;
}

.booking-actions {
  padding: 16px 20px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.btn-view,
.btn-edit,
.btn-status,
.btn-cancel,
.btn-confirm,
.btn-complete {
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.btn-view {
  background: #6c757d;
  color: white;
}

.btn-view:hover {
  background: #5a6268;
}

.btn-edit {
  background: #007bff;
  color: white;
}

.btn-edit:hover {
  background: #0056b3;
}

.btn-status,
.btn-confirm {
  background: #28a745;
  color: white;
}

.btn-status:hover,
.btn-confirm:hover {
  background: #1e7e34;
}

.btn-status:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.btn-complete {
  background: #17a2b8;
  color: white;
}

.btn-complete:hover {
  background: #117a8b;
}

.btn-cancel {
  background: #dc3545;
  color: white;
}

.btn-cancel:hover {
  background: #c82333;
}

.btn-delete {
  background: #6f42c1;
  color: white;
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.btn-delete:hover {
  background: #5a2d91;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6c757d;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state h4 {
  margin: 0 0 8px 0;
  color: #495057;
}

.empty-state p {
  margin: 0;
  font-size: 14px;
}

/* Notification */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 20px;
  border-radius: 6px;
  color: white;
  font-weight: 500;
  z-index: 1000;
  animation: slideIn 0.3s ease;
}

.notification.success {
  background: #28a745;
}

.notification.error {
  background: #dc3545;
}

.notification.info {
  background: #17a2b8;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Icons */
.icon-refresh::before { content: "🔄"; }
.icon-user::before { content: "👤"; }
.icon-phone::before { content: "📞"; }
.icon-calendar::before { content: "📅"; }
.icon-login::before { content: "🏠"; }
.icon-logout::before { content: "🚪"; }
.icon-users::before { content: "👥"; }
.icon-child::before { content: "👶"; }
.icon-baby::before { content: "🍼"; }
.icon-money::before { content: "💰"; }
.icon-note::before { content: "📝"; }
.icon-clock::before { content: "🕐"; }
.icon-eye::before { content: "👁️"; }
.icon-check::before { content: "✅"; }
.icon-checkmark::before { content: "✓"; }
.icon-checkmark-circle::before { content: "✅"; }
.icon-close::before { content: "❌"; }
.icon-calendar-empty::before { content: "📅"; }
.icon-key::before { content: "🔑"; }

/* Responsive Design */
@media (max-width: 768px) {
  .manager-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .stats-section {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    padding: 20px;
  }

  .filter-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .search-input {
    min-width: auto;
  }

  .bookings-grid {
    grid-template-columns: 1fr;
  }

  .booking-actions {
    flex-direction: column;
  }
}
</style>
