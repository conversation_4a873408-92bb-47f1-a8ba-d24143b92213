<template>
  <div id="services-app">
    <!-- Header -->
    <header class="header" :class="{ scrolled: isScrolled }">
      <div class="container">
        <nav class="nav-brand">
          <a href="/" class="brand-link">
            <div class="logo">
              <img
                src="/logo/logo-ngang-08.png"
                alt="Phong Nha Valley"
                class="logo-img"
              />
            </div>
          </a>
        </nav>

        <nav class="nav-menu" id="navMenu">
          <ul>
            <li><a href="/">Trang Chủ</a></li>
            <li><a href="/about.html">Về Phong Nha</a></li>
            <li><a href="/services.html" class="active">Dịch Vụ</a></li>
            <li><a href="/gallery.html">Thư Viện</a></li>
            <li><a href="/menu.html">Menu</a></li>
            <li><a href="/contact.html"><PERSON><PERSON><PERSON></a></li>
            <li><a href="#" class="btn-call" @click="handleCallClick">Gọi Ngay</a></li>
          </ul>
        </nav>

        <!-- Language Switcher -->
        <div class="language-switcher">
          <button
            class="lang-btn"
            :class="{ active: currentLanguage === 'vi' }"
            @click="switchLanguage('vi')"
            :disabled="translating"
            title="Tiếng Việt"
          >
            <span class="flag-emoji">🇻🇳</span>
            VI
          </button>
          <button
            class="lang-btn"
            :class="{ active: currentLanguage === 'en' }"
            @click="switchLanguage('en')"
            :disabled="translating"
            title="English - Powered by Google Translate"
          >
            <span class="flag-emoji">🇬🇧</span>
            EN
            <span v-if="currentLanguage === 'en'" class="google-icon">🌐</span>
          </button>
          <div v-if="translating" class="translation-loading">
            <span class="loading-spinner"></span>
            <span>Translating...</span>
          </div>
        </div>
        <div class="hamburger" id="hamburger" @click="toggleMenu">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>
    </header>

    <!-- Hero Section -->
    <section class="services-hero" ref="servicesHero">
      <div class="hero-overlay"></div>
      <div class="container">
        <div class="hero-content">
          <h1 class="hero-title">Dịch Vụ</h1>
          <p class="hero-subtitle">PHONG NHA VALLEY GLAMPING</p>
        </div>
      </div>
    </section>

    <!-- Services Content Section -->
    <section class="services-content">
      <div class="container">
        <div class="section-header">
          <h2>Tất Cả Dịch Vụ</h2>
          <p>Lựa chọn dịch vụ phù hợp với nhu cầu của bạn</p>
        </div>

        <!-- Filter Tabs -->
        <div class="services-filter">
          <button 
            class="filter-btn"
            :class="{ active: selectedFilter === 'all' }"
            @click="setFilter('all')"
          >
            Tất cả ({{ totalServices }})
          </button>
          <button 
            v-for="type in serviceTypes" 
            :key="type"
            class="filter-btn"
            :class="{ active: selectedFilter === type }"
            @click="setFilter(type)"
          >
            {{ formatServiceType(type) }} ({{ getServiceCountByType(type) }})
          </button>
        </div>

        <!-- Loading State -->
        <div v-if="loading" class="loading-state">
          <div class="loading-spinner"></div>
          <p>Đang tải dịch vụ...</p>
        </div>

        <!-- Error State -->
        <div v-else-if="error" class="error-state">
          <i class="fas fa-exclamation-triangle"></i>
          <p>{{ error }}</p>
          <button @click="loadServices" class="btn-retry">Thử lại</button>
        </div>

        <!-- Services Grid -->
        <div v-else class="services-grid">
          <div 
            v-for="service in filteredServices" 
            :key="service.id"
            class="service-card"
            @click="goToServiceDetail(service)"
            :title="`Click để xem chi tiết ${service.name}`"
          >
            <div class="service-image">
              <img 
                v-if="service.images && service.images.length > 0"
                :src="buildImageUrl(service.images[0])" 
                :alt="service.name"
                @error="handleImageError"
              >
              <div v-else class="placeholder-image">
                <i class="fas fa-image"></i>
                <span>{{ service.name }}</span>
              </div>
              <div 
                class="service-status" 
                :class="service.is_active ? 'active' : 'inactive'"
              >
                {{ service.is_active ? 'Available' : 'Coming Soon' }}
              </div>
            </div>
            
            <div class="service-content">
              <div class="service-type">{{ formatServiceType(service.type) }}</div>
              <h3 class="service-name">{{ service.name }}</h3>
              <p class="service-description">{{ service.description || 'Mô tả sẽ được cập nhật sớm.' }}</p>
              
              <div class="service-details">
                <div class="service-price">
                  <span class="price-adult">{{ formatPrice(service.price) }}</span>
                  <span v-if="service.child_price" class="price-child">
                    Trẻ em: {{ formatPrice(service.child_price) }}
                  </span>
                </div>
                
                <div class="service-hours" v-if="service.open_time && service.close_time">
                  <i class="fas fa-clock"></i>
                  <span>{{ formatTime(service.open_time) }} - {{ formatTime(service.close_time) }}</span>
                </div>
                
                <div class="service-capacity" v-if="service.capacity">
                  <i class="fas fa-users"></i>
                  <span>Tối đa {{ service.capacity }} người</span>
                </div>
              </div>
              
              <div class="service-actions">
                <button class="btn-view-details" @click.stop="goToServiceDetail(service)">
                  <i class="fas fa-info-circle"></i>
                  Xem Chi Tiết
                </button>
                <button class="btn-book-now" @click.stop="openBookingModal(service)">
                  <i class="fas fa-calendar-check"></i>
                  Đặt Ngay
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Empty State -->
        <div v-if="!loading && !error && filteredServices.length === 0" class="empty-state">
          <i class="fas fa-search"></i>
          <h3>Không tìm thấy dịch vụ</h3>
          <p v-if="selectedFilter !== 'all'">
            Không có dịch vụ nào thuộc loại "{{ formatServiceType(selectedFilter) }}".
          </p>
          <p v-else>Hiện tại chưa có dịch vụ nào được đăng tải.</p>
          <button @click="setFilter('all')" class="btn-show-all" v-if="selectedFilter !== 'all'">
            Xem tất cả dịch vụ
          </button>
        </div>

        <!-- Service Statistics -->
        <div v-if="!loading && !error && services.length > 0" class="services-stats">
          <div class="stats-item">
            <i class="fas fa-concierge-bell"></i>
            <span class="stats-number">{{ totalServices }}</span>
            <span class="stats-label">Tổng dịch vụ</span>
          </div>
          <div class="stats-item">
            <i class="fas fa-check-circle"></i>
            <span class="stats-number">{{ activeServices }}</span>
            <span class="stats-label">Đang hoạt động</span>
          </div>
          <div class="stats-item">
            <i class="fas fa-tags"></i>
            <span class="stats-number">{{ serviceTypes.length }}</span>
            <span class="stats-label">Loại dịch vụ</span>
          </div>
        </div>
      </div>
    </section>



    <!-- Booking Modal -->
    <div v-if="showBookingModal" class="booking-modal-overlay" @click="closeBookingModal">
      <div class="booking-modal" @click.stop>
        <div class="booking-modal-header">
          <h3>Đặt Dịch Vụ</h3>
          <button class="modal-close-btn" @click="closeBookingModal">
            <i class="fas fa-times"></i>
          </button>
        </div>

        <div class="booking-modal-content">
          <!-- Selected Service Info -->
          <div v-if="selectedServiceForBooking" class="selected-service-info">
            <div class="service-preview">
              <img
                v-if="selectedServiceForBooking.images && selectedServiceForBooking.images.length > 0"
                :src="buildImageUrl(selectedServiceForBooking.images[0])"
                :alt="selectedServiceForBooking.name"
                class="service-preview-image"
              >
              <div class="service-preview-details">
                <h4>{{ selectedServiceForBooking.name }}</h4>
                <p class="service-type">{{ formatServiceType(selectedServiceForBooking.type) }}</p>
                <p class="service-price">{{ formatPrice(selectedServiceForBooking.price) }}</p>
              </div>
            </div>
          </div>

          <!-- Booking Form -->
          <form @submit.prevent="handleBookingSubmit" class="booking-form" :class="{ loading: bookingLoading }">
            <!-- Customer Information -->
            <div class="form-row">
              <div class="form-group">
                <label for="customer_name">Họ và tên *</label>
                <input
                  v-model="bookingForm.customer_name"
                  type="text"
                  id="customer_name"
                  name="customer_name"
                  placeholder="Nhập họ và tên đầy đủ"
                  required
                >
              </div>
              <div class="form-group">
                <label for="customer_phone">Số điện thoại *</label>
                <input
                  v-model="bookingForm.customer_phone"
                  type="tel"
                  id="customer_phone"
                  name="customer_phone"
                  placeholder="0123456789"
                  required
                >
              </div>
            </div>

            <!-- Booking Date -->
            <div class="form-group">
              <label for="booking_date">Ngày đặt dịch vụ *</label>
              <input
                v-model="bookingForm.date"
                type="date"
                id="booking_date"
                name="booking_date"
                :min="todayDate"
                required
              >
            </div>

            <!-- Accommodation Dates (shown only for accommodation services) -->
            <div v-if="selectedServiceForBooking && selectedServiceForBooking.type === 'glamping'" class="form-row">
              <div class="form-group">
                <label for="check_in_date">Ngày nhận phòng</label>
                <input
                  v-model="bookingForm.check_in_date"
                  type="date"
                  id="check_in_date"
                  name="check_in_date"
                  :min="todayDate"
                >
              </div>
              <div class="form-group">
                <label for="check_out_date">Ngày trả phòng</label>
                <input
                  v-model="bookingForm.check_out_date"
                  type="date"
                  id="check_out_date"
                  name="check_out_date"
                  :min="bookingForm.check_in_date"
                >
              </div>
            </div>

            <!-- Guest Information -->
            <div class="form-row">
              <div class="form-group">
                <label for="adults">Người lớn *</label>
                <input
                  v-model.number="bookingForm.adults"
                  type="number"
                  id="adults"
                  name="adults"
                  min="1"
                  value="2"
                  required
                >
              </div>
              <div class="form-group">
                <label for="children_6_to_11">Trẻ em (6-11 tuổi)</label>
                <input
                  v-model.number="bookingForm.children_6_to_11"
                  type="number"
                  id="children_6_to_11"
                  name="children_6_to_11"
                  min="0"
                  value="0"
                >
              </div>
            </div>

            <div class="form-group">
              <label for="children_under_6">Trẻ em (Dưới 6 tuổi)</label>
              <input
                v-model.number="bookingForm.children_under_6"
                type="number"
                id="children_under_6"
                name="children_under_6"
                min="0"
                value="0"
              >
            </div>

            <div class="form-group">
              <label for="special_notes">Ghi chú đặc biệt</label>
              <textarea
                v-model="bookingForm.notes"
                id="special_notes"
                name="special_notes"
                rows="3"
                placeholder="Yêu cầu đặc biệt hoặc ghi chú..."
              ></textarea>
            </div>



            <!-- Submit Button -->
            <div class="form-actions">
              <button type="button" class="btn-cancel" @click="closeBookingModal">
                Hủy
              </button>
              <button type="submit" class="btn-submit" :disabled="bookingLoading">
                <i v-if="bookingLoading" class="fas fa-spinner fa-spin"></i>
                <i v-else class="fas fa-paper-plane"></i>
                {{ bookingLoading ? 'Đang gửi...' : 'Gửi đặt chỗ' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import apiService from '../utils/api.js';

export default {
  name: 'ServicesApp',
  data() {
    return {
      // Language
      currentLanguage: 'vi',
      translating: false,

      // Header state
      isScrolled: false,
      mobileMenuOpen: false,

      // Services data
      services: [],
      loading: true,
      error: null,

      // Filter
      selectedFilter: 'all',

      // Hero data
      heroData: {},



      // Booking modal
      showBookingModal: false,
      selectedServiceForBooking: null,
      bookingLoading: false,
      bookingForm: {
        customer_name: '',
        customer_phone: '',
        date: '',
        check_in_date: '',
        check_out_date: '',
        adults: 2,
        children_6_to_11: 0,
        children_under_6: 0,
        notes: ''
      }
    };
  },
  
  computed: {
    filteredServices() {
      if (this.selectedFilter === 'all') {
        return this.services;
      }
      return this.services.filter(service => service.type === this.selectedFilter);
    },
    
    serviceTypes() {
      const types = [...new Set(this.services.map(service => service.type))];
      return types.sort();
    },
    
    totalServices() {
      return this.services.length;
    },
    
    activeServices() {
      return this.services.filter(service => service.is_active).length;
    },

    todayDate() {
      return new Date().toISOString().split('T')[0];
    }
  },
  
  async mounted() {
    console.log('🔍 DEBUG ServicesApp: Component mounted');
    console.log('🔍 DEBUG ServicesApp: Language switcher data:', {
      currentLanguage: this.currentLanguage,
      translating: this.translating
    });

    // Setup scroll listener for header
    window.addEventListener('scroll', this.handleScroll);

    await this.loadHeroImage();
    await this.loadServices();

    // Check if language switcher is in DOM
    this.$nextTick(() => {
      const langSwitcher = document.querySelector('.language-switcher');
      console.log('🔍 DEBUG ServicesApp: Language switcher in DOM:', !!langSwitcher);
      if (langSwitcher) {
        console.log('🔍 DEBUG ServicesApp: Language switcher HTML:', langSwitcher.outerHTML);
      }
    });
  },

  beforeUnmount() {
    // Cleanup scroll listener
    window.removeEventListener('scroll', this.handleScroll);
  },
  
  methods: {
    // Language switcher methods
    switchLanguage(lang) {
      console.log('🔍 DEBUG ServicesApp: switchLanguage called with:', lang);
      if (this.translating || this.currentLanguage === lang) {
        console.log('🔍 DEBUG ServicesApp: Switch blocked - translating:', this.translating, 'currentLanguage:', this.currentLanguage);
        return;
      }

      console.log('🔍 DEBUG ServicesApp: Starting language switch to:', lang);
      this.translating = true;
      this.currentLanguage = lang;

      if (lang === 'en') {
        this.translateToEnglish();
      } else {
        this.restoreVietnamese();
      }
    },

    translateToEnglish() {
      const translations = {
        // Navigation
        'Home': 'Home',
        'About Us': 'About Us',
        'Services': 'Services',
        'Gallery': 'Gallery',
        'Menu': 'Menu',
        'Contact': 'Contact',
        'Call': 'Call',

        // Services page specific
        'Dịch vụ của chúng tôi': 'Our Services',
        'Tất cả': 'All',
        'Đặt ngay': 'Book Now',
        'Xem chi tiết': 'View Details',
        'Đặt dịch vụ': 'Book Service',
        'Đóng': 'Close',

        // Form labels
        'Họ và tên': 'Full Name',
        'Số điện thoại': 'Phone Number',
        'Ngày đặt dịch vụ': 'Service Date',
        'Người lớn': 'Adults',
        'Trẻ em (6-11 tuổi)': 'Children (6-11 years)',
        'Trẻ em (Dưới 6 tuổi)': 'Children (Under 6 years)',
        'Ghi chú đặc biệt': 'Special Notes',
        'Đang xử lý...': 'Processing...'
      };

      this.applyTranslations(translations);

      setTimeout(() => {
        this.translating = false;
      }, 1000);
    },

    restoreVietnamese() {
      const reverseTranslations = {
        // Navigation
        'Home': 'Trang Chủ',
        'About Us': 'Về Phong Nha',
        'Services': 'Dịch Vụ',
        'Gallery': 'Thư viện',
        'Menu': 'Menu',
        'Contact': 'Liên hệ',
        'Call': 'Gọi ngay',

        // Services page specific
        'Our Services': 'Dịch vụ của chúng tôi',
        'All': 'Tất cả',
        'Book Now': 'Đặt ngay',
        'View Details': 'Xem chi tiết',
        'Book Service': 'Đặt dịch vụ',
        'Close': 'Đóng',

        // Form labels
        'Full Name': 'Họ và tên',
        'Phone Number': 'Số điện thoại',
        'Service Date': 'Ngày đặt dịch vụ',
        'Adults': 'Người lớn',
        'Children (6-11 years)': 'Trẻ em (6-11 tuổi)',
        'Children (Under 6 years)': 'Trẻ em (Dưới 6 tuổi)',
        'Special Notes': 'Ghi chú đặc biệt',
        'Processing...': 'Đang xử lý...'
      };

      this.applyTranslations(reverseTranslations);

      setTimeout(() => {
        this.translating = false;
      }, 1000);
    },

    applyTranslations(translations) {
      Object.keys(translations).forEach(originalText => {
        const translatedText = translations[originalText];

        const walker = document.createTreeWalker(
          document.body,
          NodeFilter.SHOW_TEXT,
          null,
          false
        );

        const textNodes = [];
        let node;

        while (node = walker.nextNode()) {
          if (node.textContent.trim() === originalText.trim()) {
            textNodes.push(node);
          }
        }

        textNodes.forEach(textNode => {
          textNode.textContent = translatedText;
        });
      });
    },

    // Header methods
    handleScroll() {
      this.isScrolled = window.scrollY > 50;
    },

    toggleMenu() {
      const navMenu = document.getElementById('navMenu');
      const hamburger = document.getElementById('hamburger');
      navMenu.classList.toggle('active');
      hamburger.classList.toggle('active');
    },

    handleCallClick() {
      // Check if device supports phone calls (mobile)
      if (/Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
        window.location.href = 'tel:+84123456789';
      } else {
        // For desktop, show notification or copy number
        alert('Số điện thoại: +84 123 456 789');
      }
    },

    async loadHeroImage() {
      try {
        console.log('Loading hero image for Services page...');
        const response = await apiService.get('/content/homepage/hero');
        this.heroData = response;
        
        // Apply background image if available
        if (response.image_url && this.$refs.servicesHero) {
          this.applyHeroBackground(response.image_url);
        }
      } catch (error) {
        console.error('Failed to load hero image:', error);
      }
    },
    
    applyHeroBackground(imageUrl) {
      if (this.$refs.servicesHero) {
        this.$refs.servicesHero.style.backgroundImage = `url('${imageUrl}')`;
      }
    },
    
    async loadServices() {
      this.loading = true;
      this.error = null;
      
      try {
        console.log('🛎️ Loading all services for Services page...');
        const services = await apiService.getServices();
        console.log('📡 Services API Response:', services);
        
        this.services = services || [];
        console.log(`✅ Loaded ${this.services.length} services`);
      } catch (error) {
        console.error('❌ Failed to load services:', error);
        this.error = 'Không thể tải danh sách dịch vụ. Vui lòng thử lại sau.';
        this.services = [];
      } finally {
        this.loading = false;
      }
    },
    
    setFilter(type) {
      this.selectedFilter = type;
    },
    
    getServiceCountByType(type) {
      return this.services.filter(service => service.type === type).length;
    },
    
    formatServiceType(type) {
      const typeMap = {
        'glamping': 'Glamping',
        'adventure': 'Adventure Tours',
        'dining': 'Dining',
        'wellness': 'Wellness',
        'transport': 'Transport',
        'activity': 'Activities'
      };
      return typeMap[type] || type.charAt(0).toUpperCase() + type.slice(1);
    },
    
    formatPrice(price) {
      if (!price) return 'Liên hệ';
      return new Intl.NumberFormat('vi-VN', {
        style: 'currency',
        currency: 'VND'
      }).format(price);
    },
    
    formatTime(time) {
      if (!time) return '';
      // Convert HH:MM:SS to HH:MM
      return time.substring(0, 5);
    },
    
    buildImageUrl(imagePath) {
      if (!imagePath) return '';
      if (imagePath.startsWith('http')) return imagePath;
      return `/assets/uploads/${imagePath}`;
    },
    
    handleImageError(event) {
      console.warn('Failed to load service image:', event.target.src);
      event.target.style.display = 'none';
      const placeholder = event.target.parentElement.querySelector('.placeholder-image');
      if (placeholder) {
        placeholder.style.display = 'flex';
      }
    },
    
    goToServiceDetail(service) {
      window.location.href = `/service.html?id=${service.id}`;
    },
    
    scrollToBooking() {
      const bookingSection = document.getElementById('booking');
      if (bookingSection) {
        bookingSection.scrollIntoView({ behavior: 'smooth' });
      }
    },
    


    // Booking Modal Methods
    openBookingModal(service) {
      console.log('🎯 Opening booking modal for service:', service.name);
      this.selectedServiceForBooking = service;
      this.showBookingModal = true;

      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden';
    },

    closeBookingModal() {
      console.log('❌ Closing booking modal');
      this.showBookingModal = false;
      this.selectedServiceForBooking = null;
      this.resetBookingForm();

      // Restore body scroll
      document.body.style.overflow = '';
    },

    resetBookingForm() {
      this.bookingForm = {
        customer_name: '',
        customer_phone: '',
        date: '',
        check_in_date: '',
        check_out_date: '',
        adults: 2,
        children_6_to_11: 0,
        children_under_6: 0,
        notes: ''
      };
    },

    async handleBookingSubmit() {
      // Validate required fields
      if (!this.bookingForm.customer_name || !this.bookingForm.customer_phone ||
          !this.selectedServiceForBooking || !this.bookingForm.date || !this.bookingForm.adults) {
        this.showNotification('Vui lòng điền đầy đủ thông tin bắt buộc', 'error');
        return;
      }

      this.bookingLoading = true;

      try {
        // Convert date to ISO datetime format for backend
        const bookingDate = new Date(this.bookingForm.date + 'T00:00:00.000Z');

        const bookingData = {
          customer_name: this.bookingForm.customer_name,
          customer_phone: this.bookingForm.customer_phone,
          service_id: this.selectedServiceForBooking.id,
          booking_date: bookingDate.toISOString(),
          adults: this.bookingForm.adults,
          children_6_to_11: this.bookingForm.children_6_to_11,
          children_under_6: this.bookingForm.children_under_6,
          special_notes: this.bookingForm.notes
        };

        // Add check-in/check-out dates for glamping
        if (this.selectedServiceForBooking.type === 'glamping') {
          if (this.bookingForm.check_in_date) {
            const checkInDate = new Date(this.bookingForm.check_in_date + 'T00:00:00.000Z');
            bookingData.check_in_date = checkInDate.toISOString();
          }
          if (this.bookingForm.check_out_date) {
            const checkOutDate = new Date(this.bookingForm.check_out_date + 'T00:00:00.000Z');
            bookingData.check_out_date = checkOutDate.toISOString();
          }
        }

        console.log('📤 Submitting booking data:', bookingData);
        await apiService.createBooking(bookingData);

        this.showNotification('Đặt chỗ thành công! Chúng tôi sẽ liên hệ với bạn sớm nhất.', 'success');
        this.closeBookingModal();
      } catch (error) {
        console.error('❌ Booking submission failed:', error);
        this.showNotification(error.message || 'Có lỗi xảy ra khi đặt chỗ. Vui lòng thử lại.', 'error');
      } finally {
        this.bookingLoading = false;
      }
    },

    showNotification(message, type = 'info') {
      // Simple notification - you can enhance this with a proper notification system
      if (type === 'success') {
        alert('✅ ' + message);
      } else if (type === 'error') {
        alert('❌ ' + message);
      } else {
        alert('ℹ️ ' + message);
      }
    },


  }
};
</script>

<style scoped>
/* Logo image styling */
.logo-img {
  height: 100%;
  width: auto;
  max-width: 200px;
  object-fit: contain;
}

/* Services Hero Section */
.services-hero {
  height: 60vh;
  background: linear-gradient(135deg, #2d2d2d 0%, #1a1a1a 100%);
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  display: flex;
  align-items: center;
  position: relative;
  margin-top: 0; /* Remove margin-top, use body padding instead */
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  color: #fff;
  max-width: 800px;
  margin: 0 auto;
}

.hero-title {
  font-family: 'SVN-Megante', serif;
  font-size: 4rem;
  font-weight: normal;
  margin-bottom: 20px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.hero-subtitle {
  font-family: 'SVN-Alluring', serif;
  font-size: 1.2rem;
  color: #C4A962;
  letter-spacing: 3px;
  text-transform: uppercase;
  margin-bottom: 30px;
}

.hero-description {
  font-family: 'Poppins', sans-serif;
  font-size: 1.1rem;
  line-height: 1.8;
  opacity: 0.9;
  max-width: 600px;
  margin: 0 auto;
}

/* Services Content Section */
.services-content {
  padding: 100px 0;
  background: #f8f9fa;
}

.section-header {
  text-align: center;
  margin-bottom: 60px;
}

.section-header h2 {
  font-family: 'SVN-Megante', serif;
  font-size: 2.5rem;
  color: #2d2d2d;
  margin-bottom: 15px;
}

.section-header p {
  font-family: 'SVN-Alluring', serif;
  font-size: 1.1rem;
  color: #666;
}

/* Filter Tabs */
.services-filter {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-bottom: 50px;
  flex-wrap: wrap;
}

.filter-btn {
  background: #fff;
  border: 2px solid #e0e0e0;
  color: #666;
  padding: 12px 24px;
  border-radius: 25px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.filter-btn:hover {
  border-color: #C4A962;
  color: #C4A962;
  transform: translateY(-2px);
}

.filter-btn.active {
  background: #C4A962;
  border-color: #C4A962;
  color: #fff;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(196, 169, 98, 0.3);
}

/* Loading, Error, Empty States */
.loading-state,
.error-state,
.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #C4A962;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-state i,
.empty-state i {
  font-size: 3rem;
  color: #C4A962;
  margin-bottom: 20px;
}

.btn-retry,
.btn-show-all {
  background: #C4A962;
  color: #fff;
  border: none;
  padding: 12px 30px;
  border-radius: 25px;
  cursor: pointer;
  font-size: 1rem;
  margin-top: 20px;
  transition: all 0.3s ease;
}

.btn-retry:hover,
.btn-show-all:hover {
  background: #B8A055;
  transform: translateY(-2px);
}

/* Services Grid */
.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 30px;
  margin-bottom: 60px;
}

.service-card {
  background: #fff;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  height: auto;
}

.service-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
}

.service-image {
  position: relative;
  height: 250px;
  overflow: hidden;
}

.service-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.service-card:hover .service-image img {
  transform: scale(1.1);
}

.placeholder-image {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f0f0f0 0%, #e0e0e0 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 1.1rem;
}

.placeholder-image i {
  font-size: 2rem;
  margin-bottom: 10px;
}

.service-status {
  position: absolute;
  top: 15px;
  right: 15px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.service-status.active {
  background: rgba(40, 167, 69, 0.9);
  color: #fff;
}

.service-status.inactive {
  background: rgba(220, 53, 69, 0.9);
  color: #fff;
}

.service-content {
  padding: 25px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.service-type {
  font-family: 'Poppins', sans-serif;
  color: #C4A962;
  font-size: 0.9rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 10px;
}

.service-name {
  font-family: 'SVN-Megante', serif;
  font-size: 1.4rem;
  color: #2d2d2d;
  margin-bottom: 15px;
  line-height: 1.3;
}

.service-description {
  font-family: 'Poppins', sans-serif;
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
  flex: 1;
}

.service-details {
  margin-bottom: 20px;
}

.service-price {
  font-family: 'Poppins', sans-serif;
  margin-bottom: 10px;
}

.price-adult {
  font-size: 1.2rem;
  font-weight: 700;
  color: #C4A962;
}

.price-child {
  font-size: 0.9rem;
  color: #666;
  margin-left: 10px;
}

.service-hours,
.service-capacity {
  font-family: 'Poppins', sans-serif;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 5px;
}

.service-hours i,
.service-capacity i {
  color: #C4A962;
  width: 16px;
}

.service-actions {
  display: flex;
  gap: 10px;
}

.btn-view-details,
.btn-book-now {
  flex: 1;
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.btn-view-details {
  background: #f8f9fa;
  color: #666;
  border: 2px solid #e0e0e0;
}

.btn-view-details:hover {
  background: #e9ecef;
  border-color: #C4A962;
  color: #C4A962;
}

.btn-book-now {
  background: #C4A962;
  color: #fff;
}

.btn-book-now:hover {
  background: #B8A055;
  transform: translateY(-2px);
}

/* Service Statistics */
.services-stats {
  display: flex;
  justify-content: center;
  gap: 60px;
  margin-top: 60px;
  padding: 40px 0;
  border-top: 1px solid #e0e0e0;
  font-family: 'Poppins', sans-serif;
}

.stats-item {
  text-align: center;
  font-family: 'Poppins', sans-serif;
}

.stats-item i {
  font-size: 2.5rem;
  color: #C4A962;
  margin-bottom: 15px;
}

.stats-number {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: #2d2d2d;
  margin-bottom: 5px;
  font-family: 'Poppins', sans-serif;
}

.stats-label {
  color: #666;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-family: 'Poppins', sans-serif;
}



.btn-contact {
  background: linear-gradient(135deg, #0068FF 0%, #0052CC 100%);
  color: #fff;
  text-decoration: none;
}

.btn-contact:hover {
  background: linear-gradient(135deg, #0052CC 0%, #003D99 100%);
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(0, 104, 255, 0.3);
  color: #fff;
  text-decoration: none;
}

.btn-call-direct {
  background: transparent;
  color: #fff;
  border: 2px solid #fff;
  text-decoration: none;
}

.btn-call-direct:hover {
  background: #fff;
  color: #2d2d2d;
  transform: translateY(-3px);
  text-decoration: none;
}

/* Booking Modal */
.booking-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  padding: 20px;
  overflow-y: auto;
}

.booking-modal {
  background: #fff;
  border-radius: 15px;
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.booking-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25px 30px;
  border-bottom: 1px solid #e0e0e0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 15px 15px 0 0;
}

.booking-modal-header h3 {
  font-family: 'SVN-Megante', serif;
  font-size: 1.5rem;
  color: #2d2d2d;
  margin: 0;
}

.modal-close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #666;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  transition: all 0.3s ease;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close-btn:hover {
  background: #f0f0f0;
  color: #C4A962;
}

.booking-modal-content {
  padding: 30px;
}

/* Selected Service Info */
.selected-service-info {
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 10px;
  border-left: 4px solid #C4A962;
}

.service-preview {
  display: flex;
  gap: 15px;
  align-items: center;
}

.service-preview-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 10px;
  flex-shrink: 0;
}

.service-preview-details h4 {
  font-family: 'SVN-Megante', serif;
  font-size: 1.2rem;
  color: #2d2d2d;
  margin: 0 0 5px 0;
}

.service-preview-details .service-type {
  font-family: 'Poppins', sans-serif;
  color: #C4A962;
  font-size: 0.9rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin: 0 0 5px 0;
}

.service-preview-details .service-price {
  font-size: 1.1rem;
  font-weight: 700;
  color: #C4A962;
  margin: 0;
}

/* Booking Form */
.booking-form {
  position: relative;
}

.booking-form.loading {
  opacity: 0.7;
  pointer-events: none;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #2d2d2d;
  font-size: 0.95rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 12px 15px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
  font-family: inherit;
  color: #2d2d2d;
  background-color: #fff;
}

.form-group input::placeholder,
.form-group textarea::placeholder {
  color: #999;
  opacity: 1;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #C4A962;
  box-shadow: 0 0 0 3px rgba(196, 169, 98, 0.1);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}



/* Form Actions */
.form-actions {
  display: flex;
  gap: 15px;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e0e0e0;
}

.btn-cancel,
.btn-submit {
  flex: 1;
  padding: 15px 25px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.btn-cancel {
  background: #f8f9fa;
  color: #666;
  border: 2px solid #e0e0e0;
}

.btn-cancel:hover {
  background: #e9ecef;
  border-color: #C4A962;
  color: #C4A962;
}

.btn-submit {
  background: linear-gradient(135deg, #C4A962 0%, #B8A055 100%);
  color: #fff;
}

.btn-submit:hover:not(:disabled) {
  background: linear-gradient(135deg, #B8A055 0%, #A69149 100%);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(196, 169, 98, 0.3);
}

.btn-submit:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .hero-description {
    font-size: 1rem;
  }

  .services-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .services-filter {
    gap: 10px;
  }

  .filter-btn {
    padding: 10px 16px;
    font-size: 0.9rem;
  }

  .services-stats {
    flex-direction: column;
    gap: 30px;
  }



  /* Booking Modal Mobile */
  .booking-modal-overlay {
    padding: 10px;
  }

  .booking-modal {
    max-height: 95vh;
  }

  .booking-modal-header {
    padding: 20px;
  }

  .booking-modal-header h3 {
    font-size: 1.3rem;
  }

  .booking-modal-content {
    padding: 20px;
  }

  .service-preview {
    flex-direction: column;
    text-align: center;
  }

  .service-preview-image {
    width: 60px;
    height: 60px;
    align-self: center;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .form-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .services-content {
    padding: 60px 0;
  }

  .section-header h2 {
    font-size: 2rem;
  }

  .service-actions {
    flex-direction: column;
  }

  .services-filter {
    justify-content: flex-start;
    overflow-x: auto;
    padding-bottom: 10px;
  }

  /* Booking Modal Extra Small Screens */
  .booking-modal-overlay {
    padding: 5px;
    align-items: flex-start;
    padding-top: 20px;
  }

  .booking-modal {
    max-height: calc(100vh - 40px);
    border-radius: 10px;
  }

  .booking-modal-header {
    padding: 15px;
  }

  .booking-modal-header h3 {
    font-size: 1.2rem;
  }

  .booking-modal-content {
    padding: 15px;
  }

  .selected-service-info {
    padding: 15px;
    margin-bottom: 20px;
  }

  .form-group {
    margin-bottom: 15px;
  }

  .form-group input,
  .form-group select,
  .form-group textarea {
    padding: 10px 12px;
    font-size: 0.95rem;
  }

  .form-actions {
    margin-top: 20px;
    padding-top: 15px;
  }

  .btn-cancel,
  .btn-submit {
    padding: 12px 20px;
    font-size: 0.95rem;
  }
}
</style>
