import { createApp } from 'vue';
import ServicesApp from '../components/ServicesApp.vue';

console.log('🛎️ Services page script loaded');

// Create and mount the Services Vue app
const servicesApp = createApp(ServicesApp);
servicesApp.mount('#services-app');

console.log('✅ Services Vue app mounted successfully');

// Mobile menu functionality
document.addEventListener('DOMContentLoaded', function() {
  const hamburger = document.getElementById('hamburgerMenu');
  const navMenu = document.getElementById('navMenu');
  const callButton = document.getElementById('callButton');

  // Mobile menu toggle
  if (hamburger && navMenu) {
    hamburger.addEventListener('click', function() {
      navMenu.classList.toggle('active');
      hamburger.classList.toggle('active');
    });

    // Close menu when clicking on a link
    const navLinks = navMenu.querySelectorAll('a');
    navLinks.forEach(link => {
      link.addEventListener('click', function() {
        navMenu.classList.remove('active');
        hamburger.classList.remove('active');
      });
    });

    // Close menu when clicking outside
    document.addEventListener('click', function(e) {
      if (!hamburger.contains(e.target) && !navMenu.contains(e.target)) {
        navMenu.classList.remove('active');
        hamburger.classList.remove('active');
      }
    });
  }

  // Call button functionality
  if (callButton) {
    callButton.addEventListener('click', function(e) {
      e.preventDefault();
      
      // Check if it's a mobile device
      const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
      
      if (isMobile) {
        // On mobile, initiate a phone call
        window.location.href = 'tel:+84123456789';
      } else {
        // On desktop, show contact information
        alert('📞 Liên hệ với chúng tôi:\n\nĐiện thoại: +84 123 456 789\nEmail: <EMAIL>\n\nHoặc truy cập trang Contact để biết thêm thông tin!');
      }
    });
  }

  // Smooth scrolling for anchor links
  const anchorLinks = document.querySelectorAll('a[href^="#"]');
  anchorLinks.forEach(link => {
    link.addEventListener('click', function(e) {
      const href = this.getAttribute('href');
      if (href === '#') return;
      
      e.preventDefault();
      const target = document.querySelector(href);
      if (target) {
        target.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
    });
  });

  // Header scroll effect
  const header = document.querySelector('.header');
  if (header) {
    let lastScrollTop = 0;
    
    window.addEventListener('scroll', function() {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      
      if (scrollTop > 100) {
        header.classList.add('scrolled');
      } else {
        header.classList.remove('scrolled');
      }
      
      // Hide/show header on scroll
      if (scrollTop > lastScrollTop && scrollTop > 200) {
        header.style.transform = 'translateY(-100%)';
      } else {
        header.style.transform = 'translateY(0)';
      }
      
      lastScrollTop = scrollTop;
    });
  }

  console.log('✅ Services page DOM interactions initialized');
});

// Add some services-specific functionality
window.servicesUtils = {
  // Function to filter services by type
  filterServicesByType: function(services, type) {
    if (type === 'all') return services;
    return services.filter(service => service.type === type);
  },

  // Function to get service types
  getServiceTypes: function(services) {
    const types = [...new Set(services.map(service => service.type))];
    return types.sort();
  },

  // Function to format service type for display
  formatServiceType: function(type) {
    const typeMap = {
      'glamping': 'Glamping',
      'adventure': 'Adventure Tours',
      'dining': 'Dining',
      'wellness': 'Wellness',
      'transport': 'Transport',
      'activity': 'Activities'
    };
    return typeMap[type] || type.charAt(0).toUpperCase() + type.slice(1);
  },

  // Function to format price
  formatPrice: function(price) {
    if (!price) return 'Liên hệ';
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(price);
  },

  // Function to check if service is available
  isServiceAvailable: function(service) {
    return service.is_active === true;
  },

  // Function to get service statistics
  getServiceStats: function(services) {
    return {
      total: services.length,
      active: services.filter(s => s.is_active).length,
      inactive: services.filter(s => !s.is_active).length,
      types: [...new Set(services.map(s => s.type))].length
    };
  },

  // Function to sort services
  sortServices: function(services, sortBy = 'name') {
    return [...services].sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'price':
          return (a.price || 0) - (b.price || 0);
        case 'type':
          return a.type.localeCompare(b.type);
        case 'status':
          return b.is_active - a.is_active; // Active first
        default:
          return 0;
      }
    });
  }
};

console.log('🛎️ Services utilities loaded');
