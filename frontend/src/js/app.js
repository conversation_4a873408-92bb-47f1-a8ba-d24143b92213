import { createApp } from 'vue';
import MainApp from '../components/MainApp.vue';
import apiService from '../utils/api.js';
import { formatServiceType, formatPrice, formatTime, showNotification } from '../utils/helpers.js';

// Load settings from API and update contact info
async function loadSettings() {
  try {
    console.log('Loading settings...');
    const response = await apiService.get('/settings/');
    const settingsData = response;
    console.log('Settings data received:', settingsData);

    if (settingsData && settingsData.settings) {
      const settings = settingsData.settings;

      // Update contact info
      updateContactInfo(settings.contact);

      // Update site title
      if (settings.general && settings.general.site_title) {
        document.title = settings.general.site_title;
      }
    }
  } catch (error) {
    console.error('Failed to load settings:', error);
  }
}

// Update contact information in the DOM
function updateContactInfo(contactInfo) {
  if (!contactInfo) return;

  // Update phone number
  if (contactInfo.phone) {
    const phoneElements = document.querySelectorAll('.contact-details .contact-item:nth-child(1) p');
    phoneElements.forEach(el => {
      el.textContent = contactInfo.phone;
    });

    // Update call button phone number
    const callButton = document.querySelector('.btn-call');
    if (callButton) {
      callButton.setAttribute('data-phone', contactInfo.phone);
    }
  }

  // Update email
  if (contactInfo.email) {
    const emailElements = document.querySelectorAll('.contact-details .contact-item:nth-child(2) p');
    emailElements.forEach(el => {
      el.textContent = contactInfo.email;
    });
  }

  // Update address
  if (contactInfo.address) {
    const addressElements = document.querySelectorAll('.contact-details .contact-item:nth-child(3) p');
    addressElements.forEach(el => {
      // Replace \n with <br> for proper line breaks
      el.innerHTML = contactInfo.address.replace(/\n/g, '<br>');
    });
  }
}

// Load hero content from API
async function loadHeroContent() {
  try {
    console.log('Loading hero content...');

    // Get elements
    const heroSection = document.querySelector('.hero');
    const heroLoading = document.querySelector('.hero-loading');
    const heroContent = document.querySelector('.hero-content');
    const heroTitle = document.querySelector('.hero-title');
    const heroSubtitle = document.querySelector('.hero-subtitle');
    const heroDescription = document.querySelector('.hero-description');

    const response = await apiService.get('/content/homepage/hero');
    const heroData = response;
    console.log('Hero data received:', heroData);

    // Update hero section content
    if (heroTitle) {
      heroTitle.innerHTML = heroData.title || 'Glamping, Because<br>Therapy is <span class="highlight">Expensive</span>';
    }
    if (heroSubtitle) {
      heroSubtitle.textContent = heroData.subtitle || 'PHONG NHA - VALLEY GLAMPING';
    }
    if (heroDescription) {
      heroDescription.textContent = heroData.content || 'Experience nature-integrated glamping beside Chày River. Connect with nature and foster positive energy in our tranquil retreat.';
    }

    // Update background image if provided
    if (heroData.image_url && heroSection) {
      console.log('Setting hero background image:', heroData.image_url);
      // Create or update hero background
      let heroBg = heroSection.querySelector('.hero-bg');
      console.log('Found hero-bg element:', heroBg);
      if (!heroBg) {
        console.log('Creating new hero-bg element');
        heroBg = document.createElement('div');
        heroBg.className = 'hero-bg';
        heroSection.insertBefore(heroBg, heroSection.firstChild);
      }

      // Set background image with overlay
      const backgroundStyle = `
        linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)),
        url('${heroData.image_url}') center/cover no-repeat
      `;
      console.log('Setting background style:', backgroundStyle);
      heroBg.style.background = backgroundStyle;
    }

    // Hide loading and show content with animation
    if (heroLoading) {
      heroLoading.style.display = 'none';
    }
    if (heroContent) {
      heroContent.style.display = 'block';
      heroContent.classList.add('loaded');
    }

  } catch (error) {
    console.error('Failed to load hero content:', error);
    // Fallback: hide loading and show default content
    if (heroLoading) {
      heroLoading.style.display = 'none';
    }
    if (heroContent) {
      heroContent.style.display = 'block';
      heroContent.classList.add('loaded');
    }
  }
}

// Create and mount MainApp.vue for homepage
try {
    console.log('🚀 Starting to mount MainApp.vue...');

    // Check if element exists
    const appElement = document.getElementById('app');
    if (!appElement) {
        throw new Error('Element #app not found in DOM');
    }
    console.log('✅ Found #app element');

    // Create Vue app
    const app = createApp(MainApp);
    console.log('✅ Created Vue app');

    // Mount app
    const mountedApp = app.mount('#app');
    console.log('✅ MainApp.vue mounted successfully to #app');
    console.log('✅ Mounted app:', mountedApp);

    // Make app globally accessible for debugging
    window.vueApp = mountedApp;

} catch (error) {
    console.error('❌ Failed to mount MainApp.vue:', error);
    console.error('❌ Error details:', error.message);
    console.error('❌ Stack trace:', error.stack);
}

// Utility functions and non-Vue JavaScript below











// MainApp.vue will handle the #app mounting
// servicesApp is not needed as MainApp.vue handles all functionality
console.log('✅ MainApp.vue will handle Vue mounting for homepage');

// Gallery functionality is now handled by the main servicesApp

// Load hero content and settings when DOM is ready
document.addEventListener('DOMContentLoaded', async function() {
  try {
    await loadHeroContent();
    await loadSettings();
  } catch (error) {
    console.error('Failed to load content in DOMContentLoaded:', error);
  }
});

// Add CSS animations and styles
const style = document.createElement('style');
style.textContent = `
  @keyframes slideInRight {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
  
  @keyframes slideOutRight {
    from {
      transform: translateX(0);
      opacity: 1;
    }
    to {
      transform: translateX(100%);
      opacity: 0;
    }
  }
  
  .hamburger.active span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
  }
  
  .hamburger.active span:nth-child(2) {
    opacity: 0;
  }
  
  .hamburger.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
  }

  .fade-in {
    animation: fadeIn 0.5s ease-in;
  }

  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
  }

  .loading {
    opacity: 0.7;
    pointer-events: none;
  }

  .user-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    padding: 10px 0;
    min-width: 150px;
    z-index: 1001;
  }

  .user-menu a {
    display: block;
    padding: 10px 20px;
    color: #333;
    text-decoration: none;
  }

  .user-menu a:hover {
    background-color: #f8f9fa;
  }

  /* Gallery Styles */
  .gallery-loading, .gallery-error, .gallery-empty {
    text-align: center;
    padding: 40px 20px;
  }

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #4CAF50;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 30px;
  }

  .gallery-item {
    position: relative;
    aspect-ratio: 4/3;
    overflow: hidden;
    border-radius: 12px;
    cursor: pointer;
    transition: transform 0.3s ease;
  }

  .gallery-item:hover {
    transform: translateY(-5px);
  }

  .gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
  }

  .gallery-item:hover img {
    transform: scale(1.05);
  }

  .gallery-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0,0,0,0.8));
    color: white;
    padding: 20px;
    transform: translateY(100%);
    transition: transform 0.3s ease;
  }

  .gallery-item:hover .gallery-overlay {
    transform: translateY(0);
  }

  .gallery-info h4 {
    margin: 0 0 5px 0;
    font-size: 1.1rem;
  }

  .gallery-info p {
    margin: 0;
    font-size: 0.9rem;
    opacity: 0.9;
  }

  .gallery-load-more {
    text-align: center;
    margin-top: 40px;
  }

  .load-more-btn, .retry-btn {
    background: #4CAF50;
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1rem;
    transition: background 0.3s ease;
  }

  .load-more-btn:hover, .retry-btn:hover {
    background: #45a049;
  }

  .load-more-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
  }

  /* Lightbox Styles */
  .lightbox-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 20px;
  }

  .lightbox-content {
    position: relative;
    max-width: 90vw;
    max-height: 90vh;
    background: white;
    border-radius: 12px;
    overflow: hidden;
  }

  .lightbox-close {
    position: absolute;
    top: 15px;
    right: 20px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    font-size: 24px;
    cursor: pointer;
    z-index: 1001;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .lightbox-content img {
    width: 100%;
    height: auto;
    max-height: 70vh;
    object-fit: contain;
  }

  .lightbox-info {
    padding: 20px;
  }

  .lightbox-info h3 {
    margin: 0 0 10px 0;
    color: #333;
  }

  .lightbox-info p {
    margin: 0;
    color: #666;
    line-height: 1.5;
  }

  .lightbox-counter {
    margin-top: 10px;
    font-size: 0.9rem;
    color: #999;
    text-align: center;
  }

  /* Lightbox Navigation */
  .lightbox-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1001;
  }

  .lightbox-nav:hover {
    background: rgba(0, 0, 0, 0.9);
    transform: translateY(-50%) scale(1.1);
  }

  .lightbox-prev {
    left: 20px;
  }

  .lightbox-next {
    right: 20px;
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .gallery-grid {
      grid-template-columns: 1fr;
      gap: 15px;
    }

    .gallery-item {
      aspect-ratio: 16/9;
    }

    .lightbox-content {
      max-width: 95vw;
      max-height: 95vh;
    }

    .lightbox-nav {
      width: 40px;
      height: 40px;
      font-size: 20px;
    }

    .lightbox-prev {
      left: 10px;
    }

    .lightbox-next {
      right: 10px;
    }
  }

  @media (min-width: 769px) and (max-width: 1024px) {
    .gallery-grid {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  @media (min-width: 1025px) {
    .gallery-grid {
      grid-template-columns: repeat(3, 1fr);
    }
  }

  @media (min-width: 1400px) {
    .gallery-grid {
      grid-template-columns: repeat(4, 1fr);
    }
  }
`;
document.head.appendChild(style);
