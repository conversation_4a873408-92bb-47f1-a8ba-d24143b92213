import { createApp } from 'vue';
import AdminApp from '../components/AdminApp.vue';

console.log('Admin.js loading...');

// Create and mount the Vue application
const app = createApp(AdminApp);

// Global error handler
app.config.errorHandler = (err, vm, info) => {
  console.error('Vue Admin Error:', err, info);
};

// Mount the app
try {
  console.log('🔥 Mounting Vue admin app...');
  app.mount('#admin-app');
  console.log('🔥 Vue admin app mounted successfully!');
} catch (error) {
  console.error('Failed to mount Vue admin app:', error);
}

// Add admin-specific styles
const style = document.createElement('style');
style.textContent = `
  .loading {
    opacity: 0.7;
    pointer-events: none;
  }

  .notification {
    position: fixed;
    top: 100px;
    right: 20px;
    background: #17a2b8;
    color: white;
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    z-index: 2001;
    animation: slideInRight 0.3s ease;
  }

  .notification.error {
    background: #dc3545;
  }

  .notification.success {
    background: #28a745;
  }

  .notification.info {
    background: #17a2b8;
  }

  @keyframes slideInRight {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }

  @keyframes slideOutRight {
    from {
      transform: translateX(0);
      opacity: 1;
    }
    to {
      transform: translateX(100%);
      opacity: 0;
    }
  }

  .fade-in {
    animation: fadeIn 0.5s ease-in;
  }

  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
  }

  .preview-item {
    position: relative;
    display: inline-block;
    margin: 10px;
  }

  .preview-item img {
    width: 100px;
    height: 100px;
    object-fit: cover;
    border-radius: 8px;
  }

  .preview-remove {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    cursor: pointer;
    font-size: 12px;
    line-height: 1;
  }
`;
document.head.appendChild(style);
