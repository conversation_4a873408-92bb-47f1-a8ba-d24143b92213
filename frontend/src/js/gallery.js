import { createApp } from 'vue';
import GalleryApp from '../components/GalleryApp.vue';

console.log('🎨 Gallery page script loaded');

// Create and mount the Gallery Vue app
const galleryApp = createApp(GalleryApp);
galleryApp.mount('#gallery-app');

console.log('✅ Gallery Vue app mounted successfully');

// Mobile menu functionality
document.addEventListener('DOMContentLoaded', function() {
  const hamburger = document.getElementById('hamburgerMenu');
  const navMenu = document.getElementById('navMenu');
  const callButton = document.getElementById('callButton');

  // Mobile menu toggle
  if (hamburger && navMenu) {
    hamburger.addEventListener('click', function() {
      navMenu.classList.toggle('active');
      hamburger.classList.toggle('active');
    });

    // Close menu when clicking on a link
    const navLinks = navMenu.querySelectorAll('a');
    navLinks.forEach(link => {
      link.addEventListener('click', function() {
        navMenu.classList.remove('active');
        hamburger.classList.remove('active');
      });
    });

    // Close menu when clicking outside
    document.addEventListener('click', function(e) {
      if (!hamburger.contains(e.target) && !navMenu.contains(e.target)) {
        navMenu.classList.remove('active');
        hamburger.classList.remove('active');
      }
    });
  }

  // Call button functionality
  if (callButton) {
    callButton.addEventListener('click', function(e) {
      e.preventDefault();
      
      // Check if it's a mobile device
      const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
      
      if (isMobile) {
        // On mobile, initiate a phone call
        window.location.href = 'tel:+84123456789';
      } else {
        // On desktop, show contact information
        alert('📞 Liên hệ với chúng tôi:\n\nĐiện thoại: +84 123 456 789\nEmail: <EMAIL>\n\nHoặc truy cập trang Contact để biết thêm thông tin!');
      }
    });
  }

  // Smooth scrolling for anchor links
  const anchorLinks = document.querySelectorAll('a[href^="#"]');
  anchorLinks.forEach(link => {
    link.addEventListener('click', function(e) {
      const href = this.getAttribute('href');
      if (href === '#') return;
      
      e.preventDefault();
      const target = document.querySelector(href);
      if (target) {
        target.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
    });
  });

  // Header scroll effect
  const header = document.querySelector('.header');
  if (header) {
    let lastScrollTop = 0;
    
    window.addEventListener('scroll', function() {
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      
      if (scrollTop > 100) {
        header.classList.add('scrolled');
      } else {
        header.classList.remove('scrolled');
      }
      
      // Hide/show header on scroll
      if (scrollTop > lastScrollTop && scrollTop > 200) {
        header.style.transform = 'translateY(-100%)';
      } else {
        header.style.transform = 'translateY(0)';
      }
      
      lastScrollTop = scrollTop;
    });
  }

  console.log('✅ Gallery page DOM interactions initialized');
});

// Add some gallery-specific functionality
window.galleryUtils = {
  // Function to handle image loading errors
  handleImageError: function(img) {
    console.warn('Failed to load image:', img.src);
    img.src = '/assets/images/placeholder.jpg'; // Fallback image
    img.alt = 'Image not available';
  },

  // Function to preload images for better performance
  preloadImages: function(imageUrls) {
    imageUrls.forEach(url => {
      const img = new Image();
      img.src = url;
    });
  },

  // Function to get image dimensions
  getImageDimensions: function(url) {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = function() {
        resolve({
          width: this.naturalWidth,
          height: this.naturalHeight,
          aspectRatio: this.naturalWidth / this.naturalHeight
        });
      };
      img.onerror = reject;
      img.src = url;
    });
  }
};

console.log('🖼️ Gallery utilities loaded');
