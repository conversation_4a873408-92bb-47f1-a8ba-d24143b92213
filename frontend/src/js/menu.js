// Menu Application Entry Point
import { createApp } from 'vue';
import MenuApp from '../components/MenuApp.vue';

// Global error handler
window.addEventListener('unhandledrejection', function(event) {
    console.error('Unhandled promise rejection:', event.reason);
});

// Initialize the menu app
function initMenuApp() {
    try {
        console.log('Initializing Menu App...');
        
        // Create Vue app
        const app = createApp(MenuApp);
        
        // Global error handler for Vue
        app.config.errorHandler = (err, instance, info) => {
            console.error('Vue error:', err);
            console.error('Component info:', info);
            
            // Show user-friendly error
            const errorContainer = document.getElementById('menu-app');
            if (errorContainer) {
                errorContainer.innerHTML = `
                    <div class="error-container">
                        <div class="error-icon">🍽️</div>
                        <h1 class="error-title">Application Error</h1>
                        <p class="error-message">The menu page encountered an error. Please refresh the page and try again.</p>
                        <div class="error-actions">
                            <button class="btn-retry" onclick="location.reload()">Refresh Page</button>
                            <a href="/" class="btn-home">Go to Homepage</a>
                        </div>
                    </div>
                `;
            }
        };
        
        // Mount the app
        app.mount('#menu-app');
        
        console.log('Menu App initialized successfully');
        
    } catch (error) {
        console.error('Failed to initialize Menu App:', error);
        
        // Show error state
        const errorContainer = document.getElementById('menu-app');
        if (errorContainer) {
            errorContainer.innerHTML = `
                <div class="error-container">
                    <div class="error-icon">❌</div>
                    <h1 class="error-title">Initialization Failed</h1>
                    <p class="error-message">Failed to load the menu application. Please check your internet connection and try again.</p>
                    <div class="error-actions">
                        <button class="btn-retry" onclick="location.reload()">Retry</button>
                        <a href="/" class="btn-home">Go to Homepage</a>
                    </div>
                </div>
            `;
        }
    }
}

// Wait for DOM to be ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initMenuApp);
} else {
    initMenuApp();
}

// Export for debugging
window.MenuApp = {
    init: initMenuApp
};
