# Frontend Pages

This directory is reserved for future page template development.

## Current Pages

### Main Website (`/`)
- **File**: `frontend/public/index.html`
- **Description**: Customer-facing website with services, gallery, and booking functionality
- **Sections**:
  - Hero section with call-to-action
  - Services showcase
  - About section with mission/vision
  - Photo gallery
  - Contact and booking form
  - Authentication modal

### Admin Panel (`/admin/`)
- **File**: `frontend/public/admin/index.html`
- **Description**: Administrative interface for managing content and bookings
- **Sections**:
  - Dashboard with statistics
  - Service management
  - Gallery management
  - Booking management
  - User management
  - Settings

## Future Page Templates

When implementing template-based development:

### Customer Pages
- `home.html` - Landing page template
- `services.html` - Services listing page
- `service-detail.html` - Individual service page
- `gallery.html` - Photo gallery page
- `contact.html` - Contact page
- `booking.html` - Booking page
- `profile.html` - User profile page

### Admin Pages
- `admin-dashboard.html` - Admin dashboard template
- `admin-services.html` - Service management template
- `admin-bookings.html` - Booking management template
- `admin-users.html` - User management template
- `admin-gallery.html` - Gallery management template
- `admin-settings.html` - Settings template

## Template Features

Future templates should include:
- Responsive design
- Accessibility features
- SEO optimization
- Component integration
- Dynamic content loading
- Form validation
- Error handling

## Current Status

Currently using monolithic HTML files. Pages directory is prepared for future template-based development.
