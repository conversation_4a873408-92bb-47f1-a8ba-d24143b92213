/**
 * Service utility functions for robust service navigation
 */

/**
 * Validate if a string is a valid UUID format
 * @param {string} uuid - The UUID string to validate
 * @returns {boolean} - True if valid UUID format
 */
export function isValidUUID(uuid) {
  if (!uuid || typeof uuid !== 'string') {
    return false;
  }
  
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
}

/**
 * Validate service data structure
 * @param {object} service - The service object to validate
 * @returns {boolean} - True if service has required fields
 */
export function isValidService(service) {
  if (!service || typeof service !== 'object') {
    return false;
  }
  
  // Check required fields
  const requiredFields = ['id', 'name', 'type'];
  return requiredFields.every(field => service[field] && service[field].toString().trim() !== '');
}

/**
 * Create a safe service navigation URL
 * @param {string} serviceId - The service ID
 * @returns {string} - The navigation URL
 */
export function createServiceURL(serviceId) {
  if (!isValidUUID(serviceId)) {
    console.error('Invalid service ID for URL creation:', serviceId);
    return '/';
  }
  
  return `/service.html?id=${serviceId}`;
}

/**
 * Extract service ID from current URL
 * @returns {string|null} - The service ID or null if not found/invalid
 */
export function getServiceIdFromURL() {
  const urlParams = new URLSearchParams(window.location.search);
  const serviceId = urlParams.get('id');
  
  if (!serviceId || !isValidUUID(serviceId)) {
    return null;
  }
  
  return serviceId;
}

/**
 * Navigate to service page with validation
 * @param {string} serviceId - The service ID to navigate to
 * @param {array} services - Array of available services for validation
 * @returns {boolean} - True if navigation was initiated
 */
export function navigateToService(serviceId, services = []) {
  // Validate service ID format
  if (!isValidUUID(serviceId)) {
    console.error('Invalid service ID format:', serviceId);
    return false;
  }
  
  // Validate service exists in provided services array
  if (services.length > 0) {
    const service = services.find(s => s.id === serviceId);
    if (!service) {
      console.error('Service not found in available services:', serviceId);
      return false;
    }
    
    if (!service.is_active) {
      console.warn('Service is not active:', service.name);
      return false;
    }
  }
  
  // Create and navigate to URL
  const url = createServiceURL(serviceId);
  console.log('Navigating to service:', { serviceId, url });
  
  window.location.href = url;
  return true;
}

/**
 * Get service display order for consistent sorting
 * @param {array} services - Array of services to sort
 * @returns {array} - Sorted services array
 */
export function getSortedServices(services) {
  if (!Array.isArray(services)) {
    return [];
  }
  
  return services
    .filter(service => isValidService(service))
    .sort((a, b) => {
      // Primary sort: by name (A-Z)
      const nameCompare = a.name.localeCompare(b.name);
      if (nameCompare !== 0) {
        return nameCompare;
      }
      
      // Secondary sort: by ID for consistency
      return a.id.localeCompare(b.id);
    });
}

/**
 * Debug service navigation issues
 * @param {string} serviceId - The service ID being debugged
 * @param {array} services - Available services
 * @param {string} context - Context where debug is called from
 */
export function debugServiceNavigation(serviceId, services, context = 'unknown') {
  console.group(`🔍 Service Navigation Debug - ${context}`);
  console.log('Service ID:', serviceId);
  console.log('Is valid UUID:', isValidUUID(serviceId));
  console.log('Available services count:', services.length);
  
  if (services.length > 0) {
    const service = services.find(s => s.id === serviceId);
    console.log('Service found:', !!service);
    if (service) {
      console.log('Service details:', {
        id: service.id,
        name: service.name,
        type: service.type,
        active: service.is_active
      });
    }
  }
  
  console.log('Target URL:', createServiceURL(serviceId));
  console.groupEnd();
}
