// Helper utility functions

export function formatServiceType(type) {
  return type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
}

export function formatPrice(price) {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND'
  }).format(price);
}

export function formatDate(dateString) {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
}

export function formatTime(timeString) {
  if (!timeString) return '';

  // Handle different time formats
  let time;
  if (timeString.includes('T')) {
    // ISO format like "0000-01-01T09:00:00Z"
    time = new Date(timeString);
  } else if (timeString.includes(':')) {
    // Time format like "09:00:00" or "09:00"
    const [hours, minutes] = timeString.split(':');
    time = new Date();
    time.setHours(parseInt(hours), parseInt(minutes) || 0, 0, 0);
  } else {
    return timeString; // Return as-is if format is unknown
  }

  // Format to 12-hour format with AM/PM
  return time.toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  });
}

export function getServiceImage(type) {
  const imageMap = {
    'ACCOMMODATION': 'accommodation.jpg',
    'AFTERNOON_TEA': 'tea.jpg',
    'WATER_ACTIVITY': 'water-activities.jpg',
    'SIGHTSEEING': 'sightseeing.jpg',
    'GO_KART': 'gokart.jpg',
    'KIDS_WATER_PARK': 'kids-park.jpg',
    'PARAGLIDING': 'paragliding.jpg'
  };
  return imageMap[type] || 'service-default.jpg';
}

export function scrollToSection(sectionId) {
  console.log('🔍 scrollToSection called with sectionId:', sectionId);
  const element = document.getElementById(sectionId);
  console.log('🔍 Found element:', element);

  if (element) {
    const headerHeight = document.querySelector('.header')?.offsetHeight || 0;
    const elementPosition = element.offsetTop - headerHeight;

    console.log('🔍 Header height:', headerHeight);
    console.log('🔍 Element position:', elementPosition);
    console.log('🔍 Scrolling to position:', elementPosition);

    window.scrollTo({
      top: elementPosition,
      behavior: 'smooth'
    });

    console.log('✅ Scroll command executed');
  } else {
    console.error('❌ Element not found with id:', sectionId);
  }
}

export function showNotification(message, type = 'info') {
  // Remove existing notifications
  const existingNotifications = document.querySelectorAll('.notification');
  existingNotifications.forEach(n => n.remove());

  const notification = document.createElement('div');
  notification.className = `notification ${type}`;
  notification.style.cssText = `
    position: fixed;
    top: 100px;
    right: 20px;
    background: ${type === 'error' ? '#dc3545' : type === 'success' ? '#28a745' : '#17a2b8'};
    color: white;
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    z-index: 2001;
    animation: slideInRight 0.3s ease;
  `;
  notification.textContent = message;

  document.body.appendChild(notification);

  setTimeout(() => {
    notification.style.animation = 'slideOutRight 0.3s ease';
    setTimeout(() => notification.remove(), 300);
  }, 3000);
}

export function getTodayDate() {
  return new Date().toISOString().split('T')[0];
}

export function addDays(date, days) {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result.toISOString().split('T')[0];
}
