// API utility functions for Vue.js application
const API_BASE = '/api/v1';

class ApiService {
  constructor() {
    this.baseURL = API_BASE;
    this.token = localStorage.getItem('auth_token');
  }

  setToken(token) {
    this.token = token;
    if (token) {
      localStorage.setItem('auth_token', token);
    } else {
      localStorage.removeItem('auth_token');
    }
  }

  getToken() {
    return this.token;
  }

  async call(endpoint, options = {}) {
    const url = `${API_BASE}${endpoint}`;
    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    };

    if (this.token) {
      config.headers.Authorization = `Bearer ${this.token}`;
    }

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'API request failed');
      }

      return data;
    } catch (error) {
      console.error('API Error:', error);
      throw error;
    }
  }

  // Authentication methods
  async login(email, password) {
    const response = await this.call('/admin/login', {
      method: 'POST',
      body: JSON.stringify({ email, password })
    });

    this.setToken(response.token);
    return response;
  }

  async register(userData) {
    const response = await this.call('/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData)
    });

    this.setToken(response.token);
    return response;
  }

  async changePassword(currentPassword, newPassword) {
    return await this.call('/admin/change-password', {
      method: 'POST',
      body: JSON.stringify({
        current_password: currentPassword,
        new_password: newPassword
      })
    });
  }

  async getCurrentUser() {
    return await this.call('/user/profile');
  }

  async logout() {
    try {
      // Call logout API to invalidate refresh tokens
      if (this.token) {
        await this.call('/admin/logout', { method: 'POST' });
      }
    } catch (error) {
      console.error('Logout API call failed:', error);
      // Continue with local logout even if API call fails
    } finally {
      // Always clear local token
      this.setToken(null);
    }
  }

  // Generic HTTP methods
  async get(endpoint) {
    return await this.call(endpoint, { method: 'GET' });
  }

  async post(endpoint, data) {
    return await this.call(endpoint, {
      method: 'POST',
      body: JSON.stringify(data)
    });
  }

  async put(endpoint, data) {
    return await this.call(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data)
    });
  }

  async delete(endpoint) {
    return await this.call(endpoint, { method: 'DELETE' });
  }

  // Services methods
  async getServices() {
    const response = await this.call('/services');
    return response.services || [];
  }

  async getAdminServices() {
    console.log('🚀 API: Calling /admin/services endpoint - VERSION 2025-07-09-04 🚀');
    console.log('🚀 Full URL will be: ' + this.baseURL + '/admin/services 🚀');
    const response = await this.call('/admin/services');
    console.log('🚀 API: Admin services response:', response);
    return response.services || [];
  }

  async getService(id) {
    return await this.call(`/services/${id}`);
  }

  async createService(serviceData) {
    return await this.call('/admin/services', {
      method: 'POST',
      body: JSON.stringify(serviceData)
    });
  }

  async updateService(id, serviceData) {
    return await this.call(`/admin/services/${id}`, {
      method: 'PUT',
      body: JSON.stringify(serviceData)
    });
  }

  async deleteService(id) {
    return await this.call(`/admin/services/${id}`, {
      method: 'DELETE'
    });
  }

  // Gallery methods
  async getGalleryImages(page = 1, limit = 12) {
    try {
      const response = await this.call(`/content/gallery?page=${page}&limit=${limit}`);
      return response; // Return full response with gallery and pagination data
    } catch (error) {
      console.error('Failed to load gallery images:', error);
      return {
        gallery: [],
        pagination: {
          current_page: 1,
          per_page: limit,
          total_count: 0,
          total_pages: 0,
          has_next: false,
          has_previous: false
        }
      };
    }
  }

  async createGalleryItem(galleryData) {
    return await this.call('/admin/content/gallery', {
      method: 'POST',
      body: JSON.stringify(galleryData)
    });
  }

  async updateGalleryItem(id, galleryData) {
    return await this.call(`/admin/content/gallery/${id}`, {
      method: 'PUT',
      body: JSON.stringify(galleryData)
    });
  }

  async deleteGalleryItem(id) {
    return await this.call(`/admin/content/gallery/${id}`, {
      method: 'DELETE'
    });
  }

  // Upload images method
  async uploadImages(files) {
    const formData = new FormData();

    console.log('=== API UPLOAD DEBUG ===');
    console.log('Files to upload:', files);

    // Add files to FormData
    for (let i = 0; i < files.length; i++) {
      console.log(`File ${i}:`, files[i].name, files[i]);
      formData.append('images', files[i]);
    }

    const url = `${API_BASE}/admin/content/upload`;
    const config = {
      method: 'POST',
      body: formData,
      headers: {}
    };

    if (this.token) {
      config.headers.Authorization = `Bearer ${this.token}`;
    }

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Upload failed');
      }

      return data;
    } catch (error) {
      console.error('Upload Error:', error);
      throw error;
    }
  }

  // Delete image method
  async deleteImage(filename) {
    const url = `${API_BASE}/admin/content/delete-image`;
    const config = {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ filename })
    };

    if (this.token) {
      config.headers.Authorization = `Bearer ${this.token}`;
    }

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Delete failed');
      }

      return data;
    } catch (error) {
      console.error('Delete Image Error:', error);
      throw error;
    }
  }

  // Cleanup orphaned images method
  async cleanupOrphanedImages() {
    const url = `${API_BASE}/admin/content/cleanup-orphaned-images`;
    const config = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    };

    if (this.token) {
      config.headers.Authorization = `Bearer ${this.token}`;
    }

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Cleanup failed');
      }

      return data;
    } catch (error) {
      console.error('Cleanup Orphaned Images Error:', error);
      throw error;
    }
  }

  // Booking methods
  async getBookings() {
    try {
      const response = await this.call('/admin/bookings');
      return response.bookings || [];
    } catch (error) {
      console.error('Failed to load bookings:', error);
      return [];
    }
  }

  // Bookings methods
  async createBooking(bookingData) {
    // Public booking endpoint - no authentication required
    // Use trailing slash to avoid 307 redirect
    return await this.callPublic('/bookings/', {
      method: 'POST',
      body: JSON.stringify(bookingData)
    });
  }

  // Public API call without authentication
  async callPublic(endpoint, options = {}) {
    const url = `${API_BASE}${endpoint}`;
    console.log('DEBUG: Making public API call to:', url);
    console.log('DEBUG: Request options:', options);

    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    };

    // Don't add Authorization header for public endpoints

    try {
      const response = await fetch(url, config);
      console.log('DEBUG: Response status:', response.status);
      console.log('DEBUG: Response URL:', response.url);

      const data = await response.json();

      if (!response.ok) {
        console.error('DEBUG: API Error response:', data);
        throw new Error(data.error || 'API request failed');
      }

      console.log('DEBUG: API Success response:', data);
      return data;
    } catch (error) {
      console.error('API Error:', error);
      throw error;
    }
  }

  async getUserBookings() {
    return await this.call('/bookings');
  }

  async checkAvailability(serviceId, date) {
    // Public availability check - no authentication required
    return await this.callPublic(`/availability?service_id=${serviceId}&date=${date}`);
  }

  async updateBookingStatus(id, statusData) {
    return await this.call(`/admin/bookings/${id}/status`, {
      method: 'PUT',
      body: JSON.stringify(statusData)
    });
  }

  // User methods
  async getUsers() {
    try {
      const response = await this.call('/admin/users');
      return response.users || [];
    } catch (error) {
      console.error('Failed to load users:', error);
      return [];
    }
  }

  async createUser(userData) {
    return await this.call('/admin/users', {
      method: 'POST',
      body: JSON.stringify(userData)
    });
  }

  async updateUser(userId, userData) {
    return await this.call(`/admin/users/${userId}`, {
      method: 'PUT',
      body: JSON.stringify(userData)
    });
  }

  async deleteUser(userId) {
    return await this.call(`/admin/users/${userId}`, {
      method: 'DELETE'
    });
  }

  // Homepage content methods
  async getHomepageContent(section) {
    try {
      const response = await this.call(`/admin/content/homepage/${section}`);
      return response;
    } catch (error) {
      console.error(`Failed to load ${section} content:`, error);
      throw error;
    }
  }

  async updateHomepageContent(section, contentData) {
    try {
      const response = await this.call(`/admin/content/homepage/${section}`, {
        method: 'PUT',
        body: JSON.stringify(contentData)
      });
      return response;
    } catch (error) {
      console.error(`Failed to update ${section} content:`, error);
      throw error;
    }
  }

  // Settings methods
  async getSettings() {
    try {
      const response = await this.call('/settings/');
      return response;
    } catch (error) {
      console.error('Failed to load settings:', error);
      throw error;
    }
  }

  async updateSettings(settingsData) {
    try {
      const response = await this.call('/admin/settings/', {
        method: 'PUT',
        body: JSON.stringify(settingsData)
      });
      return response;
    } catch (error) {
      console.error('Failed to update settings:', error);
      throw error;
    }
  }

  // Menu methods
  async getMenus() {
    try {
      const response = await this.call('/admin/menus');
      return response;
    } catch (error) {
      console.error('Failed to load menus:', error);
      throw error;
    }
  }

  async getMenu(id) {
    try {
      const response = await this.call(`/menus/${id}`);
      return response;
    } catch (error) {
      console.error('Failed to load menu:', error);
      throw error;
    }
  }

  async createMenu(menuData) {
    try {
      const response = await this.call('/admin/menus', {
        method: 'POST',
        body: JSON.stringify(menuData)
      });
      return response;
    } catch (error) {
      console.error('Failed to create menu:', error);
      throw error;
    }
  }

  async updateMenu(id, menuData) {
    try {
      const response = await this.call(`/admin/menus/${id}`, {
        method: 'PUT',
        body: JSON.stringify(menuData)
      });
      return response;
    } catch (error) {
      console.error('Failed to update menu:', error);
      throw error;
    }
  }

  async deleteMenu(id) {
    try {
      const response = await this.call(`/admin/menus/${id}`, {
        method: 'DELETE'
      });
      return response;
    } catch (error) {
      console.error('Failed to delete menu:', error);
      throw error;
    }
  }

  // Upload menu images method
  async uploadMenuImages(files) {
    const formData = new FormData();

    console.log('=== MENU UPLOAD DEBUG ===');
    console.log('Files to upload:', files);

    // Add files to FormData
    for (let i = 0; i < files.length; i++) {
      console.log(`File ${i}:`, files[i].name, files[i]);
      formData.append('images', files[i]);
    }

    const url = `${API_BASE}/admin/menus/upload`;
    const config = {
      method: 'POST',
      body: formData,
      headers: {}
    };

    if (this.token) {
      config.headers.Authorization = `Bearer ${this.token}`;
    }

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Upload failed');
      }

      return data;
    } catch (error) {
      console.error('Menu Upload Error:', error);
      throw error;
    }
  }
}

// Create singleton instance
const apiService = new ApiService();

export default apiService;
