<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Services - Phong Nha Valley Glamping</title>
    <meta name="description" content="Khám phá tất cả các dịch vụ tuyệt vời tại Phong Nha Valley Glamping - từ glamping, adventure tours đến các hoạt động giải trí">
    <link rel="stylesheet" href="/assets/css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="nav-brand">
                <a href="/" class="logo-link">
                    <div class="logo">
                        <span>VALLEY GLAMPING</span>
                    </div>
                </a>
            </div>
            <nav class="nav-menu" id="navMenu">
                <ul>
                    <li><a href="/">Trang Chủ</a></li>
                    <li><a href="/about.html" class="active">Về Phong Nha</a></li>
                    <li><a href="/services.html">Dịch Vụ</a></li>
                    <li><a href="/gallery.html">Thư Viện</a></li>
                    <li><a href="/menu.html">Menu</a></li>                    
                    <li><a href="/contact.html">Liên Hệ</a></li>
                    <li><a href="#" class="btn-call" onclick="handleCallAction()">Gọi Ngay</a></li>
                </ul>
            </nav>

            <!-- Language Switcher -->
            <div class="language-switcher">
                <button
                    class="lang-btn active"
                    id="viBtn"
                    title="Tiếng Việt"
                >
                    <span class="flag-emoji">🇻🇳</span>
                    VI
                </button>
                <button
                    class="lang-btn"
                    id="enBtn"
                    title="English - Powered by Google Translate"
                >
                    <span class="flag-emoji">🇬🇧</span>
                    EN
                    <span class="google-icon" style="display: none;">🌐</span>
                </button>
                <div id="translationLoading" class="translation-loading" style="display: none;">
                    <span class="loading-spinner"></span>
                    <span>Translating...</span>
                </div>
            </div>

            <div class="hamburger" id="hamburger">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </header>

    <!-- Vue Services App -->
    <div id="services-app">
        <!-- Vue component will be mounted here -->
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="logo">
                        <h3>Phong Nha</h3>
                        <span>VALLEY GLAMPING</span>
                    </div>
                    <p>Experience nature-integrated glamping beside Chày River. Connect with nature and foster positive energy in our tranquil retreat.</p>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="/">Home</a></li>
                        <li><a href="/about.html">About Us</a></li>
                        <li><a href="/services.html">Services</a></li>
                        <li><a href="/gallery.html">Gallery</a></li>
                        <li><a href="/contact.html">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Contact Info</h4>
                    <div class="contact-item">
                        <i class="fas fa-phone"></i>
                        <span>+84 123 456 789</span>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-envelope"></i>
                        <span><EMAIL></span>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>Phong Nha, Quảng Bình, Vietnam</span>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>Follow Us</h4>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-youtube"></i></a>
                        <a href="#"><i class="fab fa-tripadvisor"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Phong Nha Valley Glamping. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="/assets/js/services.js"></script>

    <!-- Language Switcher Script -->
    <script>
        let currentLanguage = 'vi';
        let translating = false;

        // Language switcher functionality
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔍 DEBUG: Services page DOMContentLoaded');
            const viBtn = document.getElementById('viBtn');
            const enBtn = document.getElementById('enBtn');
            const translationLoading = document.getElementById('translationLoading');

            console.log('🔍 DEBUG: Services language switcher elements found:', {
                viBtn: !!viBtn,
                enBtn: !!enBtn,
                translationLoading: !!translationLoading
            });

            viBtn.addEventListener('click', () => switchLanguage('vi'));
            enBtn.addEventListener('click', () => switchLanguage('en'));

            function switchLanguage(lang) {
                if (translating || currentLanguage === lang) return;

                translating = true;
                translationLoading.style.display = 'flex';

                // Update button states
                viBtn.classList.toggle('active', lang === 'vi');
                enBtn.classList.toggle('active', lang === 'en');

                if (lang === 'en') {
                    translateToEnglish();
                } else {
                    restoreVietnamese();
                }

                currentLanguage = lang;
            }

            function translateToEnglish() {
                const translations = {
                    // Navigation
                    'Home': 'Home',
                    'About Us': 'About Us',
                    'Services': 'Services',
                    'Gallery': 'Gallery',
                    'Menu': 'Menu',
                    'Contact': 'Contact',
                    'Call': 'Call',

                    // Services specific
                    'Dịch vụ của chúng tôi': 'Our Services',
                    'Khám phá các dịch vụ tuyệt vời': 'Discover our amazing services',
                    'Đặt ngay': 'Book Now',
                    'Xem chi tiết': 'View Details',

                    // Footer
                    'Quick Links': 'Quick Links',
                    'Contact Info': 'Contact Info',
                    'Follow Us': 'Follow Us',
                    'All rights reserved': 'All rights reserved'
                };

                applyTranslations(translations);

                setTimeout(() => {
                    translating = false;
                    translationLoading.style.display = 'none';
                    document.querySelector('#enBtn .google-icon').style.display = 'inline';
                }, 1000);
            }

            function restoreVietnamese() {
                const reverseTranslations = {
                    // Navigation
                    'Home': 'Trang Chủ',
                    'About Us': 'Về Phong Nha',
                    'Services': 'Dịch Vụ',
                    'Gallery': 'Thư viện',
                    'Menu': 'Menu',
                    'Contact': 'Liên hệ',
                    'Call': 'Gọi ngay',

                    // Services specific
                    'Our Services': 'Dịch vụ của chúng tôi',
                    'Discover our amazing services': 'Khám phá các dịch vụ tuyệt vời',
                    'Book Now': 'Đặt ngay',
                    'View Details': 'Xem chi tiết',

                    // Footer
                    'Quick Links': 'Liên kết nhanh',
                    'Contact Info': 'Thông tin liên hệ',
                    'Follow Us': 'Theo dõi chúng tôi',
                    'All rights reserved': 'Bảo lưu mọi quyền'
                };

                applyTranslations(reverseTranslations);

                setTimeout(() => {
                    translating = false;
                    translationLoading.style.display = 'none';
                    document.querySelector('#enBtn .google-icon').style.display = 'none';
                }, 1000);
            }

            function applyTranslations(translations) {
                Object.keys(translations).forEach(originalText => {
                    const translatedText = translations[originalText];

                    const walker = document.createTreeWalker(
                        document.body,
                        NodeFilter.SHOW_TEXT,
                        null,
                        false
                    );

                    const textNodes = [];
                    let node;

                    while (node = walker.nextNode()) {
                        if (node.textContent.trim() === originalText.trim()) {
                            textNodes.push(node);
                        }
                    }

                    textNodes.forEach(textNode => {
                        textNode.textContent = translatedText;
                    });
                });
            }
        });

        // Mobile menu toggle function
        function toggleMenu() {
            const navMenu = document.getElementById('navMenu');
            const hamburger = document.querySelector('.hamburger');

            if (navMenu) navMenu.classList.toggle('active');
            if (hamburger) hamburger.classList.toggle('active');
        }

        // Setup hamburger menu handler
        document.addEventListener('DOMContentLoaded', function() {
            const hamburger = document.getElementById('hamburger');
            if (hamburger) {
                hamburger.addEventListener('click', toggleMenu);
            }
        });
    </script>
</body>
</html>
