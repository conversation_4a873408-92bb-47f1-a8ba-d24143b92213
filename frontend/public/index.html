<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Phong Nha Valley Glamping</title>
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">
    <link rel="stylesheet" href="/assets/css/style.css?v=20250721">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer">

</head>
<body>
    <!-- Vue.js App Container -->
    <div id="app"></div>

    <script>
        // Debug script to check Vue mounting
        console.log('🔍 Debug script loaded');

        // Check if DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔍 DOM loaded');

            // Check if #app exists
            const appElement = document.getElementById('app');
            console.log('🔍 #app element:', appElement);

            // Wait a bit then check Vue
            setTimeout(() => {
                console.log('🔍 Checking Vue after 2 seconds...');
                console.log('🔍 window.vueApp:', window.vueApp);
                console.log('🔍 Vue global:', window.Vue);


            }, 2000);
        });
    </script>
    <script src="/assets/js/app.js"></script>
</body>
</html>
