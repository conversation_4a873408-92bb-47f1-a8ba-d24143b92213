<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Menu - Phong Nha Valley Glamping</title>
    <link rel="icon" type="image/x-icon" href="/assets/images/favicon.ico">
    
    <!-- Meta tags for SEO -->
    <meta name="description" content="Discover our delicious menu featuring local Vietnamese specialties and international cuisine at Phong Nha Valley Glamping">
    <meta name="keywords" content="menu, food, restaurant, vietnamese cuisine, phong nha, valley, glamping">
    <meta name="author" content="Phong Nha Valley">
    
    <!-- Open Graph tags -->
    <meta property="og:title" content="Menu - Phong Nha Valley Glamping">
    <meta property="og:description" content="Discover our delicious menu featuring local Vietnamese specialties and international cuisine">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://phongnhavalley.com/menu">
    <meta property="og:image" content="/assets/images/menu-og-image.jpg">
    
    <!-- Twitter Card tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Menu - Phong Nha Valley Glamping">
    <meta name="twitter:description" content="Discover our delicious menu featuring local Vietnamese specialties and international cuisine">
    <meta name="twitter:image" content="/assets/images/menu-og-image.jpg">
    
    <!-- Preload critical resources -->
    <link rel="preload" href="/assets/css/style.css" as="style">
    
    <!-- CSS -->
    <link rel="stylesheet" href="/assets/css/style.css">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    

</head>
<body>
    <div id="menu-app"></div>

    <!-- Vue.js and app scripts -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script>
        // Global error handler
        window.addEventListener('error', function(event) {
            console.error('Global error:', event.error);
            showErrorState('Application Error', 'An unexpected error occurred. Please try refreshing the page.');
        });

        // Show error state
        function showErrorState(title, message) {
            const app = document.getElementById('menu-app');
            app.innerHTML = `
                <div class="error-container">
                    <div class="error-icon">🍽️</div>
                    <h1 class="error-title">${title}</h1>
                    <p class="error-message">${message}</p>
                    <div class="error-actions">
                        <button class="btn-retry" onclick="location.reload()">Retry</button>
                        <a href="/" class="btn-home">Go to Homepage</a>
                    </div>
                </div>
            `;
        }

        // Check if Vue is loaded
        if (typeof Vue === 'undefined') {
            showErrorState('Vue.js Not Loaded', 'Failed to load Vue.js framework. Please check your internet connection and try again.');
        }
    </script>
    
    <!-- Load the menu app -->
    <script src="/assets/js/menu.js"></script>
</body>
</html>
