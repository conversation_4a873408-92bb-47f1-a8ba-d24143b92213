<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Menu Management - Phong Nha Valley Admin</title>
    <link rel="stylesheet" href="/assets/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .menu-management {
            padding: 20px;
        }

        .menu-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .menu-categories {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .category-btn {
            padding: 10px 20px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 5px;
            transition: all 0.3s;
        }

        .category-btn.active {
            background: #C4A962;
            color: white;
            border-color: #C4A962;
        }

        .menu-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .menu-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .menu-card-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 10px;
        }

        .menu-card h3 {
            margin: 0 0 5px 0;
            color: #333;
        }

        .menu-card .price {
            font-weight: bold;
            color: #C4A962;
            font-size: 1.1em;
        }

        .menu-card .description {
            color: #666;
            margin-bottom: 10px;
            font-size: 0.9em;
        }

        .menu-images {
            display: flex;
            gap: 5px;
            margin-bottom: 10px;
            flex-wrap: wrap;
        }

        .menu-image {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 4px;
            border: 1px solid #ddd;
        }

        .menu-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }

        .btn {
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s;
        }

        .btn-primary {
            background: #C4A962;
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn:hover {
            opacity: 0.8;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
        }

        .status-available {
            background: #d4edda;
            color: #155724;
        }

        .status-unavailable {
            background: #f8d7da;
            color: #721c24;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 20px;
            border-radius: 8px;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ddd;
        }

        .close {
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            color: #aaa;
        }

        .close:hover {
            color: #000;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .form-group textarea {
            height: 80px;
            resize: vertical;
        }

        .image-upload-area {
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .image-upload-area:hover {
            border-color: #C4A962;
            background-color: #f9f9f9;
        }

        .image-upload-area.dragover {
            border-color: #C4A962;
            background-color: #f0f8ff;
        }

        .uploaded-images {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }

        .uploaded-image {
            position: relative;
            width: 100px;
            height: 100px;
        }

        .uploaded-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 4px;
            border: 1px solid #ddd;
        }

        .remove-image {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .loading {
            text-align: center;
            padding: 20px;
        }

        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
        }

        .success {
            color: #155724;
            background: #d4edda;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- Sidebar -->
        <nav class="admin-sidebar">
            <div class="admin-logo">
                <h2>Phong Nha Admin</h2>
            </div>
            <ul class="admin-nav">
                <li><a href="/admin"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                <li><a href="/admin/services.html"><i class="fas fa-concierge-bell"></i> Services</a></li>
                <li><a href="/admin/gallery.html"><i class="fas fa-images"></i> Gallery</a></li>
                <li><a href="/admin/content.html"><i class="fas fa-file-alt"></i> Content</a></li>
                <li><a href="/admin/menu-management.html" class="active"><i class="fas fa-utensils"></i> Menu Management</a></li>
                <li><a href="/admin/bookings.html"><i class="fas fa-calendar-check"></i> Bookings</a></li>
                <li><a href="/admin/settings.html"><i class="fas fa-cog"></i> Settings</a></li>
            </ul>
            <div class="admin-user">
                <button onclick="logout()" class="btn-logout">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </button>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="admin-main">
            <div class="menu-management">
                <div class="menu-header">
                    <h1>Menu Management</h1>
                    <button class="btn btn-primary" onclick="openCreateModal()">
                        <i class="fas fa-plus"></i> Add New Menu Item
                    </button>
                </div>

                <!-- Category Filter -->
                <div class="menu-categories">
                    <button class="category-btn active" onclick="filterMenus('all')">All Categories</button>
                    <button class="category-btn" onclick="filterMenus('restaurant')">Restaurant</button>
                    <button class="category-btn" onclick="filterMenus('accommodation')">Accommodation</button>
                    <button class="category-btn" onclick="filterMenus('afternoon_tea')">Afternoon Tea</button>
                </div>

                <!-- Loading State -->
                <div id="loading" class="loading">
                    <i class="fas fa-spinner fa-spin"></i> Loading menus...
                </div>

                <!-- Error State -->
                <div id="error" class="error" style="display: none;"></div>

                <!-- Menu Grid -->
                <div id="menuGrid" class="menu-grid" style="display: none;"></div>
            </div>
        </main>
    </div>

    <!-- Create/Edit Menu Modal -->
    <div id="menuModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalTitle">Add New Menu Item</h2>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            
            <form id="menuForm">
                <div id="modalError" class="error" style="display: none;"></div>
                <div id="modalSuccess" class="success" style="display: none;"></div>

                <div class="form-group">
                    <label for="menuName">Name *</label>
                    <input type="text" id="menuName" name="name" required>
                </div>

                <div class="form-group">
                    <label for="menuDescription">Description</label>
                    <textarea id="menuDescription" name="description" placeholder="Enter menu item description..."></textarea>
                </div>

                <div class="form-group">
                    <label for="menuCategory">Category *</label>
                    <select id="menuCategory" name="category" required>
                        <option value="">Select Category</option>
                        <option value="restaurant">Restaurant</option>
                        <option value="accommodation">Accommodation</option>
                        <option value="afternoon_tea">Afternoon Tea</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="menuPrice">Price (VND)</label>
                    <input type="number" id="menuPrice" name="price" min="0" step="1000">
                </div>

                <div class="form-group">
                    <label for="menuDisplayOrder">Display Order</label>
                    <input type="number" id="menuDisplayOrder" name="display_order" min="0" value="0">
                </div>

                <div class="form-group">
                    <label>
                        <input type="checkbox" id="menuIsAvailable" name="is_available" checked>
                        Available
                    </label>
                </div>

                <div class="form-group">
                    <label>Images</label>
                    <div class="image-upload-area" onclick="document.getElementById('imageInput').click()">
                        <i class="fas fa-cloud-upload-alt fa-2x"></i>
                        <p>Click to upload images or drag and drop</p>
                        <p><small>Maximum 10 images, 10MB each. Supported: JPEG, PNG, WebP</small></p>
                    </div>
                    <input type="file" id="imageInput" multiple accept="image/*" style="display: none;">
                    <div id="uploadedImages" class="uploaded-images"></div>
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Save Menu Item
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="closeModal()">Cancel</button>
                </div>
            </form>
        </div>
    </div>

    <script src="/assets/js/admin.js"></script>
    <script src="/assets/js/menu-management.js"></script>
</body>
</html>
