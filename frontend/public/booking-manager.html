<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Booking Manager - Phong Nha Valley</title>
    <link rel="icon" type="image/x-icon" href="/assets/images/favicon.ico">
    
    <!-- Meta tags for SEO -->
    <meta name="description" content="Booking Management Panel for Phong Nha Valley">
    <meta name="keywords" content="booking, management, phong nha, valley">
    <meta name="author" content="Phong Nha Valley">
    
    <!-- Open Graph tags -->
    <meta property="og:title" content="Booking Manager - Phong Nha Valley">
    <meta property="og:description" content="Booking Management Panel for Phong Nha Valley">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://phongnhavalley.com/booking-manager">
    <meta property="og:image" content="/assets/images/og-image.jpg">
    
    <!-- Twitter Card tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Booking Manager - Phong Nha Valley">
    <meta name="twitter:description" content="Booking Management Panel for Phong Nha Valley">
    <meta name="twitter:image" content="/assets/images/og-image.jpg">
    
    <!-- Preload critical resources -->
    <link rel="preload" href="/assets/css/style.css" as="style">
    
    <!-- CSS -->
    <link rel="stylesheet" href="/assets/css/style.css">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
        }
        
        #booking-manager-app {
            min-height: 100vh;
        }
        
        .loading-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: #f8f9fa;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #e3e3e3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        .loading-text {
            margin-top: 20px;
            color: #666;
            font-size: 16px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Error state */
        .error-container {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: #f8f9fa;
            text-align: center;
            padding: 20px;
        }
        
        .error-icon {
            font-size: 64px;
            margin-bottom: 20px;
        }
        
        .error-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }
        
        .error-message {
            font-size: 16px;
            color: #666;
            margin-bottom: 30px;
            max-width: 500px;
        }
        
        .error-actions {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .btn-retry {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }
        
        .btn-retry:hover {
            background: #5a6fd8;
        }
        
        .btn-home {
            background: #6c757d;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-home:hover {
            background: #5a6268;
        }
    </style>
</head>
<body>
    <div id="booking-manager-app">
        <!-- Loading state -->
        <div class="loading-container">
            <div>
                <div class="loading-spinner"></div>
                <div class="loading-text">Loading Booking Manager...</div>
            </div>
        </div>
    </div>

    <!-- App scripts -->
    <script>
        console.log('🚀 Booking Manager HTML loaded');

        // Global error handler
        window.addEventListener('error', function(event) {
            console.error('Global error:', event.error);
            showErrorState('Application Error', 'An unexpected error occurred. Please try refreshing the page.');
        });

        // Unhandled promise rejection handler
        window.addEventListener('unhandledrejection', function(event) {
            console.error('Unhandled promise rejection:', event.reason);
            showErrorState('Application Error', 'An unexpected error occurred. Please try refreshing the page.');
        });

        // Show error state
        function showErrorState(title, message) {
            console.log('📢 Showing error state:', title, message);
            const app = document.getElementById('booking-manager-app');
            app.innerHTML = `
                <div class="error-container">
                    <div class="error-icon">⚠️</div>
                    <h1 class="error-title">${title}</h1>
                    <p class="error-message">${message}</p>
                    <div class="error-actions">
                        <button class="btn-retry" onclick="location.reload()">Refresh Page</button>
                        <a href="/" class="btn-home">Go to Homepage</a>
                    </div>
                </div>
            `;
        }

        // Check if app loads after 5 seconds
        setTimeout(function() {
            const app = document.getElementById('booking-manager-app');
            const loadingContainer = app.querySelector('.loading-container');
            if (loadingContainer && loadingContainer.style.display !== 'none') {
                console.warn('⚠️ App still loading after 5 seconds, showing timeout error');
                showErrorState('Loading Timeout', 'The booking manager is taking too long to load. Please refresh the page and try again.');
            }
        }, 5000);
    </script>

    <!-- Load the booking manager app -->
    <script src="/assets/js/booking-manager.js"></script>
</body>
</html>
