<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Service Details - Phong Nha Valley Glamping</title>
    <meta name="description" content="Chi tiết dịch vụ tại Phong Nha Valley Glamping - trải nghiệm thiên nhiên độc đáo tại Quảng Bình">
    <link rel="stylesheet" href="/assets/css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="nav-brand">
                <a href="/" class="logo-link">
                    <div class="logo">
                        <span>VALLEY GLAMPING</span>
                    </div>
                </a>
            </div>
            <nav class="nav-menu" id="navMenu">
                <ul>
                    <li><a href="/">Trang Chủ</a></li>
                    <li><a href="/about.html" class="active">Về Phong Nha</a></li>
                    <li><a href="/services.html">Dịch Vụ</a></li>
                    <li><a href="/gallery.html">Thư Viện</a></li>
                    <li><a href="/menu.html">Menu</a></li>                    
                    <li><a href="/contact.html">Liên Hệ</a></li>
                    <li><a href="#" class="btn-call" onclick="handleCallAction()">Gọi Ngay</a></li>
                </ul>
            </nav>
            <div class="hamburger" onclick="toggleMenu()">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </header>

    <!-- Vue Service App -->
    <div id="service-app">
        <!-- Vue component will be mounted here -->
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-brand">
                    <h3>Phong Nha Valley</h3>
                    <p>Nature-integrated glamping experience</p>
                </div>
                <div class="footer-links">
                    <div class="footer-section">
                        <h4>Quick Links</h4>
                        <ul>
                            <li><a href="/">Home</a></li>
                            <li><a href="/about.html">About</a></li>
                            <li><a href="/#services">Services</a></li>
                            <li><a href="/#contact">Contact</a></li>
                        </ul>
                    </div>
                    <div class="footer-section">
                        <h4>Services</h4>
                        <ul>
                            <li><a href="/#services">Accommodation</a></li>
                            <li><a href="/#services">Water Activities</a></li>
                            <li><a href="/#services">Afternoon Tea</a></li>
                            <li><a href="/#services">Sightseeing</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Phong Nha Valley. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="/assets/js/service.js"></script>
    <script>
        // Function to handle call action - different behavior for mobile vs desktop
        function handleCallAction() {
            // Get phone number from call button data attribute or use default
            const callButton = document.querySelector('.btn-call');
            const phoneNumber = callButton?.getAttribute('data-phone') || '************';

            // Check if user is on mobile device
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

            if (isMobile) {
                // On mobile: open phone dialer
                window.location.href = `tel:${phoneNumber}`;
            } else {
                // On desktop: scroll to contact section
                scrollToSection('contact');
            }
        }

        // Function to toggle mobile menu
        function toggleMenu() {
            const navMenu = document.querySelector('.nav-menu');
            const hamburger = document.querySelector('.hamburger');

            if (navMenu && hamburger) {
                navMenu.classList.toggle('active');
                hamburger.classList.toggle('active');
            }
        }

        // Function to close mobile menu
        function closeMenu() {
            const navMenu = document.querySelector('.nav-menu');
            const hamburger = document.querySelector('.hamburger');

            if (navMenu && hamburger) {
                navMenu.classList.remove('active');
                hamburger.classList.remove('active');
            }
        }

        // Close menu when clicking on menu items
        document.addEventListener('DOMContentLoaded', function() {
            const menuLinks = document.querySelectorAll('.nav-menu a');
            menuLinks.forEach(link => {
                link.addEventListener('click', closeMenu);
            });
        });

        // Global function for scrolling to sections
        function scrollToSection(sectionId) {
            const element = document.getElementById(sectionId);
            if (element) {
                const elementPosition = element.getBoundingClientRect().top;
                const offsetPosition = elementPosition + window.pageYOffset - 80; // 80px offset for header

                window.scrollTo({
                    top: offsetPosition,
                    behavior: 'smooth'
                });
            }
        }
    </script>
</body>
</html>
