/* Custom Fonts */
@font-face {
    font-family: 'SVN-Megante';
    src: url('../../../font/SVN-Megante.otf') format('opentype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'SVN-Alluring';
    src: url('../../../font/SVN-Alluring.otf') format('opentype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

/* Color Palette Variables */
:root {
    /* Primary Brand Colors */
    --primary-green: #3f9772;      /* Main green from palette */
    --secondary-green: #90cab3;    /* Light green from palette */
    --accent-gold: #f4d054;        /* Golden yellow from palette */
    --dark-blue: #14485f;          /* Dark blue from palette */
    --cream: #fff0cd;              /* Cream/beige from palette */

    /* Extended Palette */
    --white: #ffffff;
    --black: #000000;
    --gray-light: #f8f9fa;
    --gray-medium: #6c757d;
    --gray-dark: #343a40;

    /* Semantic Colors */
    --success: var(--primary-green);
    --warning: var(--accent-gold);
    --info: var(--secondary-green);
    --danger: #dc3545;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'SVN-Alluring', serif;
    line-height: 1.6;
    color: var(--gray-dark);
    overflow-x: hidden;
    padding-top: 80px; /* Adjusted for smaller header */
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 0; /* No padding */
    transition: all 0.3s ease;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    font-family: 'Poppins', sans-serif;
}

.header .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1400px; /* Increased max-width */
    margin: 0 auto;
    padding: 0 20px; /* Equal padding */
    flex-wrap: nowrap; /* Prevent wrapping */
    overflow: visible; /* Ensure content is visible */
}

.nav-brand {
    flex: 0 0 auto;
    margin-right: 30px; /* Reduced space */
    margin-left: -40px; /* Less negative margin */
}

.logo {
    height: 135px !important; /* Increased to 1.5x from 90px */
    transition: transform 0.3s ease;
}

.logo:hover {
    transform: scale(1.05);
}

/* Logo Styling - Support both SVG and Text */
.nav-brand .logo {
    display: flex;
    align-items: center;
    height: 100%;
    padding: 8px 0;
}

/* SVG Logo Styling - Custom height */
.logo-svg {
    height: calc(100vh * 0.5); /* Custom dynamic height */
    min-height: 50px; /* Minimum height */
    max-height: 100px; /* Custom maximum height */
    width: auto;
    transition: all 0.3s ease;
    filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.12));
}

.logo-svg:hover {
    transform: scale(1.02);
    filter: drop-shadow(0 5px 12px rgba(63, 151, 114, 0.25));
}

.logo-text {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    transition: all 0.3s ease;
}

.logo-text h1 {
    font-family: 'SVN-Megante', serif;
    font-size: 1.8rem;
    font-weight: normal;
    color: var(--dark-blue);
    margin: 0;
    line-height: 1.1;
    transition: color 0.3s ease;
}

.logo-text span {
    font-family: 'SVN-Alluring', serif;
    font-size: 0.75rem;
    font-weight: normal;
    letter-spacing: 2px;
    text-transform: uppercase;
    color: var(--primary-green);
    margin-top: -2px;
    transition: color 0.3s ease;
}

.logo-text:hover h1 {
    color: var(--primary-green);
}

.logo-text:hover span {
    color: var(--dark-blue);
}

/* Responsive logo sizing */
@media (max-width: 768px) {
    body {
        padding-top: 70px; /* Adjusted for smaller mobile header */
    }

    .header {
        padding: 0;
    }

    .header .container {
        padding: 0 15px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .logo {
        height: 65px !important; /* Smaller on mobile */
    }

    .nav-brand {
        order: 0; /* Logo in center */
        flex: 1;
        display: flex;
        justify-content: center;
        margin: 0;
    }

    .nav-menu a {
        font-size: 1.1rem;
        padding: 14px 22px;
    }

    .btn-call {
        font-size: 0.9rem !important;
        padding: 10px 20px !important;
        min-width: auto !important;
        white-space: nowrap;
    }

    .logo-text h1 {
        font-size: 1.6rem;
    }

    .logo-text span {
        font-size: 0.7rem;
        letter-spacing: 1.5px;
    }
}

@media (max-width: 480px) {
    .header {
        min-height: 60px;
        padding: 0;
    }

    .header.scrolled {
        min-height: 55px;
        padding: 0;
    }

    .logo-svg {
        min-height: 40px;
        max-height: 50px;
    }

    .btn-call {
        font-size: 0.85rem !important;
        padding: 8px 16px !important;
        min-width: auto !important;
        white-space: nowrap;
    }

    .logo-text h1 {
        font-size: 1.4rem;
    }

    .logo-text span {
        font-size: 0.65rem;
        letter-spacing: 1px;
    }
}

.nav-menu {
    flex: 1 1 auto; /* Allow to grow and shrink */
    display: flex;
    justify-content: center;
}

.nav-menu ul {
    display: flex;
    list-style: none;
    align-items: center;
    gap: 20px; /* Reduced gap */
    margin: 0;
    padding: 0;
}

.nav-menu a {
    color: var(--dark-blue);
    text-decoration: none;
    font-family: 'Poppins', sans-serif !important;
    font-weight: 600 !important;
    font-size: 14px !important;
    transition: all 0.3s ease;
    position: relative;
    padding: 12px 18px;
    border-radius: 8px;
}

.nav-menu a:hover {
    color: var(--primary-green);
    background: rgba(63, 151, 114, 0.08);
}

.nav-menu a.active {
    color: var(--primary-green);
    background: rgba(63, 151, 114, 0.12);
    font-weight: 600;
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-green);
    transition: width 0.3s ease;
}

.nav-menu a:hover::after {
    width: 100%;
}

.btn-call {
    background: var(--primary-green) !important;
    color: var(--white) !important;
    padding: 16px 32px;
    border-radius: 35px;
    transition: all 0.3s ease;
    position: relative;
    font-family: 'SVN-Alluring', serif;
    font-weight: 600;
    font-size: 1.1rem;
    box-shadow: 0 4px 15px rgba(63, 151, 114, 0.3);
    border: 2px solid transparent;
}

.btn-call:hover {
    background: var(--white) !important;
    color: var(--primary-green) !important;
    border-color: var(--primary-green);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(63, 151, 114, 0.4);
}

.btn-call::before {
    content: "📞";
    margin-right: 5px;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 6px;
    flex: 0 0 auto; /* Don't grow or shrink */
    order: 3; /* After language switcher */
    margin-left: 15px;
}

.hamburger span {
    width: 37px; /* Increased from 25px (1.5x) */
    height: 4px; /* Increased from 3px */
    background: var(--dark-blue);
    transition: all 0.3s ease;
    border-radius: 2px;
}

.hamburger.active span:nth-child(1) {
    transform: rotate(45deg) translate(7px, 7px);
}

.hamburger.active span:nth-child(2) {
    opacity: 0;
}

.hamburger.active span:nth-child(3) {
    transform: rotate(-45deg) translate(9px, -8px);
}

/* Hero Section */
.hero {
    height: 100vh;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: #fff;
    overflow: hidden;
}

.hero-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)),
                linear-gradient(45deg, #2d2d2d, #1a1a1a);
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    z-index: -1;
    transition: all 0.5s ease;
}

.hero-content {
    z-index: 2;
    max-width: 800px;
    padding: 0 20px;
}

.hero-subtitle {
    font-family: 'Poppins', sans-serif;
    color: var(--accent-gold);
    font-size: 1rem;
    font-weight: normal;
    letter-spacing: 3px;
    text-transform: uppercase;
    margin-bottom: 20px;
}

.hero-title {
    font-family: 'SVN-Megante', serif !important;
    font-size: 3.5rem;
    font-weight: normal;
    line-height: 1.2;
    margin-bottom: 30px;
}

.hero-title .highlight {
    color: var(--accent-gold);
}

.hero-description {
    font-family: 'Poppins', sans-serif;
    font-size: 1.1rem;
    font-weight: normal;
    line-height: 1.8;
    margin-bottom: 40px;
    opacity: 0.9;
}

.btn-primary {
    background: var(--primary-green);
    color: var(--white);
    padding: 15px 40px;
    border: none;
    border-radius: 30px;
    font-size: 1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn-primary:hover {
    background: var(--dark-blue);
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(63, 151, 114, 0.4);
}

/* Services Section */
.services {
    padding: 100px 0;
    background: var(--cream);
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
}

.section-header-text {
    flex: 1;
    text-align: center;
}

.section-header-action {
    flex-shrink: 0;
    margin-left: 20px;
}

.section-header h2 {
    font-family: 'SVN-Megante', serif;
    font-size: 2.5rem;
    font-weight: normal;
    color: #2d2d2d;
    margin-bottom: 15px;
}

.section-header p {
    font-size: 1.1rem;
    color: #666;
    font-weight: 300;
}

.view-more-link {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: #C4A962;
    text-decoration: none;
    font-family: 'SVN-Alluring', serif;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
    padding: 8px 16px;
    border-radius: 25px;
    border: 2px solid #C4A962;
    background: transparent;
}

.view-more-link:hover {
    background: #C4A962;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(196, 169, 98, 0.3);
}

.view-more-link i {
    font-size: 0.9rem;
    transition: transform 0.3s ease;
}

.view-more-link:hover i {
    transform: translateX(3px);
}



/* Services Carousel */
.services-carousel-container {
    position: relative;
    display: flex;
    align-items: center;
    gap: 20px;
    width: 100%;
    max-width: 1200px; /* Reduce to fit 3 cards properly */
    margin: 0 auto;
}

.services-carousel-wrapper {
    flex: 1;
    overflow: hidden; /* Hide overflow to show only 3 cards */
    width: 100%;
}

.services-grid.carousel-track {
    display: flex;
    transition: transform 0.5s ease;
    gap: 0;
}

.service-group {
    display: flex;
    gap: 25px;
    min-width: 100%;
    width: 100%;
    flex-shrink: 0;
    align-items: stretch;
    /* For single card navigation, we need to position each group */
    position: relative;
}

.service-group .service-card {
    flex: 0 0 calc(33.333% - 17px); /* Fixed width for 3 cards with gap */
    max-width: calc(33.333% - 17px);
    min-height: 400px; /* Ensure consistent height */
}

.carousel-nav-btn {
    background: linear-gradient(135deg, #C4A962, #B8A055);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 55px;
    height: 55px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 20px;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(196, 169, 98, 0.3);
    z-index: 10;
    position: relative;
    overflow: hidden;
}

.carousel-nav-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), transparent);
    border-radius: 50%;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.carousel-nav-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #D4B972, #C4A962);
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(196, 169, 98, 0.5);
    border-color: rgba(255, 255, 255, 0.4);
}

.carousel-nav-btn:hover:not(:disabled)::before {
    opacity: 1;
}

.carousel-nav-btn:disabled {
    background: linear-gradient(135deg, #999, #777);
    cursor: not-allowed;
    opacity: 0.4;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    transform: none;
}

.carousel-nav-btn:active:not(:disabled) {
    transform: scale(0.95);
    box-shadow: 0 2px 10px rgba(196, 169, 98, 0.4);
}

.carousel-nav-btn i {
    font-size: 1.2rem;
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
}

.carousel-indicators {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 30px;
}

.carousel-indicators .indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: none;
    background: #ddd;
    cursor: pointer;
    transition: all 0.3s ease;
}

.carousel-indicators .indicator.active {
    background: #C4A962;
    transform: scale(1.2);
}

.carousel-indicators .indicator:hover {
    background: #B8A055;
}

/* Original services grid for fallback */
.services-grid:not(.carousel-track) {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 30px;
    align-items: stretch;
    grid-auto-rows: 1fr;
    justify-content: center;
    max-width: 1200px;
    margin: 0 auto;
}

.service-card {
    background: #fff;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    height: auto; /* Allow flexible height */
    min-height: 380px; /* Increased for consistent height across devices */
    flex: 1; /* Equal width for all cards in carousel */
    min-width: 280px; /* Smaller minimum width for better mobile fit */
    max-width: 350px; /* Maximum width for desktop */

}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
}

.service-image {
    height: 250px;
    background: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.3)),
                linear-gradient(45deg, #C4A962, #B8A055);
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
}

.service-status {
    font-family: 'Poppins', sans-serif;
    position: absolute;
    top: 15px;
    right: 15px;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.service-status.active {
    background: #28a745;
    color: #fff;
}

.service-status.inactive {
    background: #dc3545;
    color: #fff;
}

.service-content {
    padding: 30px;
    display: flex;
    flex-direction: column;
    flex: 1;
}

.service-type {
    font-family: 'Poppins', sans-serif;
    color: #C4A962;
    font-size: 0.9rem;
    font-weight: normal;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 10px;
}

.service-name {
    font-family: 'SVN-Megante', serif !important;
    font-size: 1.5rem;
    font-weight: normal;
    color: #2d2d2d;
    margin-bottom: 15px;
}

.service-description {
    font-family: 'Poppins', sans-serif;
    color: #666;
    line-height: 1.6;
    margin-bottom: 20px;
    flex: 1;
    min-height: 80px;
    /* Limit to 4 lines for service cards/slides */
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Full description for individual service pages */
.service-page .service-description,
.service-detail-description {
    display: block;
    -webkit-line-clamp: unset;
    -webkit-box-orient: unset;
    overflow: visible;
    text-overflow: unset;
    min-height: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.service-price {
    font-family: 'Poppins', sans-serif;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    min-height: 30px;
}

.price-adult {
    font-family: 'Poppins', sans-serif;
    font-size: 1.2rem;
    font-weight: 700;
    color: #C4A962;
}

.price-child {
    font-family: 'Poppins', sans-serif;
    font-size: 0.9rem;
    color: #666;
}

.service-hours {
    font-family: 'Poppins', sans-serif;
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 0;
    min-height: 25px;
}

.service-actions {
    display: flex;
    justify-content: center;
    margin-top: auto;
    padding-top: 20px;
}

.btn-view-details {
    flex: 1;
    background: #fff;
    color: #C4A962;
    padding: 12px;
    border: 2px solid #C4A962;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    text-align: center;
}

.btn-view-details:hover {
    background: #C4A962;
    color: #fff;
}

.btn-book {
    flex: 1;
    background: #C4A962;
    color: #fff;
    padding: 12px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-book:hover {
    background: #B8A055;
}

.btn-book {
    width: 100%;
    background: #C4A962;
    color: #fff;
    padding: 12px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-book:hover {
    background: #B8A055;
}

.btn-book:disabled {
    background: #ccc;
    cursor: not-allowed;
}

/* About image placeholders */
.about-image-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f0f0f0;
    border-radius: 15px;
    width: 100%;
    height: 100%;
}

.about-image-placeholder.small {
    width: 200px;
    height: 250px;
}

.about-image-placeholder .placeholder-box {
    background: #e0e0e0;
    padding: 20px;
    border-radius: 8px;
    color: #666;
    font-size: 0.9rem;
    text-align: center;
}

/* About Section */
.about {
    padding: 100px 0;
    background: #fff;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.about-text h2 {
    font-family: 'SVN-Megante', serif;
    font-size: 2.5rem;
    font-weight: normal;
    color: var(--dark-blue);
    margin-bottom: 30px;
}

.about-text p {
    font-family: 'Poppins', sans-serif;
    font-size: 1.1rem;
    color: var(--gray-medium);
    line-height: 1.8;
    margin-bottom: 30px;
}

.mission-vision {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 40px;
}

.mission h3,
.vision h3 {
    color: #C4A962;
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 15px;
}

.core-values h3 {
    font-family: 'Poppins', sans-serif;
    color: #2d2d2d;
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 20px;
}

.core-values ul {
    list-style: none;
}

.core-values li {
    font-family: 'Poppins', sans-serif;
    margin-bottom: 10px;
    color: #666;
}

.core-values strong {
    color: #C4A962;
}

.about-image {
    background: linear-gradient(45deg, #C4A962, #B8A055);
    border-radius: 15px;
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.1);
    min-height: 400px;
}

/* About Section - New Design */
.about-new {
    padding: 100px 0;
    background: #f8f9fa;
    position: relative;
    min-height: 400px;
    z-index: 2; /* Higher z-index than services section */
}

.about-new-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
    position: relative;
    max-width: 1000px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Images Container - Overlapping Layout */
.about-images-container {
    position: relative;
    width: 100%;
    max-width: 500px;
    margin: 0 auto;
}

.about-large-image {
    width: 100%;
    height: 350px;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
    position: relative;
}

.about-large-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.about-small-image {
    position: absolute;
    top: -20px;
    left: -20px;
    width: 180px;
    height: 140px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.25);
    background: #fff;
    border: 4px solid #fff;
    z-index: 2;
}

.about-small-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Fallback for missing images */
.about-small-image:empty::before,
.about-small-image img[src*="about-valley-small.jpg"]::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #C4A962, #B8A055);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.9rem;
    text-align: center;
}

.about-large-image:empty::before,
.about-large-image img[src*="about-valley-large.jpg"]::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #C4A962, #B8A055);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    text-align: center;
}

/* Content Container */
.about-content-container {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px 0;
}

.about-text-content {
    max-width: 450px;
}

.about-text-content {
    max-width: 450px;
    padding: 0;
}

.about-greeting {
    font-family: 'SVN-Alluring', serif;
    color: #999;
    font-size: 0.9rem;
    font-weight: normal;
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 1px;
    display: block;
}

.about-title {
    font-family: 'SVN-Megante', serif !important;
    font-size: 2.2rem;
    color: #2d2d2d;
    margin-bottom: 20px;
    line-height: 1.2;
    font-weight: normal;
}

.about-description {
    font-family: 'Poppins', sans-serif;
    font-size: 1rem;
    line-height: 1.6;
    color: #666;
    margin-bottom: 30px;
    text-align: left;
}

.about-cta {
    background: var(--primary-green);
    color: white;
    padding: 12px 30px;
    border: none;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    align-self: flex-start;
    box-shadow: 0 4px 15px rgba(196, 169, 98, 0.3);
}

.about-cta:hover {
    background: var(--dark-blue);
    transform: translateY(-2px);
}

/* Navigation arrows - Hidden */
.about-nav {
    display: none;
}

/* Gallery Section */
.gallery {
    padding: 100px 0;
    background: var(--gray-light);
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.gallery-item {
    aspect-ratio: 1;
    overflow: hidden;
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.gallery-item:hover {
    transform: scale(1.05);
}

.gallery-item {
    background: linear-gradient(45deg, #C4A962, #B8A055);
    transition: all 0.3s ease;
}

.gallery-item:hover img {
    transform: scale(1.1);
}

/* Contact Section */
.contact {
    padding: 100px 0;
    background: var(--dark-blue);
    color: var(--white);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
}

.contact-info h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 40px;
    color: var(--accent-gold);
}

.contact-details {
    margin-bottom: 40px;
}

.contact-item {
    margin-bottom: 30px;
}

.contact-item h3 {
    color: var(--accent-gold);
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 10px;
}

.contact-item p {
    font-family: 'Poppins', sans-serif;
    color: #ccc;
    line-height: 1.6;
}

.social-links {
    margin-top: 30px;
}

.social-links h3 {
    color: var(--accent-gold);
    font-size: 1.2rem;
    margin-bottom: 15px;
}

.social-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.social-link {
    color: var(--accent-gold);
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.social-link:hover {
    color: var(--white);
}

/* Social media specific colors */
.social-link.instagram {
    background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
    color: white;
    padding: 12px 20px;
    border-radius: 25px;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: transform 0.3s ease;
}

.social-link.facebook {
    background: #1877f2;
    color: white;
    padding: 12px 20px;
    border-radius: 25px;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: transform 0.3s ease;
}

.social-link.zalo {
    background: #0068ff;
    color: white;
    padding: 12px 20px;
    border-radius: 25px;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: transform 0.3s ease;
}

.social-link.instagram:hover,
.social-link.facebook:hover,
.social-link.zalo:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.booking-form {
    background: rgba(255, 255, 255, 0.1);
    padding: 40px;
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

.booking-form h3 {
    color: var(--accent-gold);
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 30px;
}

.form-group {
    margin-bottom: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.form-group label {
    font-family: 'Poppins', sans-serif;
    display: block;
    color: #C4A962;
    font-weight: 600;
    margin-bottom: 8px;
}

.form-group input,
.form-group select,
.form-group textarea {
    font-family: 'Poppins', sans-serif;
    width: 100%;
    padding: 12px 15px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #C4A962;
    background: rgba(255, 255, 255, 0.15);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

/* Select dropdown styling */
.form-group select {
    background-color: rgba(255, 255, 255, 0.9);
    color: #2d2d2d;
    font-weight: 500;
}

.form-group select:focus {
    background-color: rgba(255, 255, 255, 0.95);
    color: #2d2d2d;
}

.form-group select option {
    background-color: #fff;
    color: #2d2d2d;
    padding: 8px 12px;
    font-weight: 500;
}

.form-group select option:hover,
.form-group select option:focus {
    background-color: #C4A962;
    color: #fff;
}

.form-group select option:checked {
    background-color: #C4A962;
    color: #fff;
}

/* Footer */
.footer {
    background: #1a1a1a;
    color: #fff;
    padding: 60px 0 20px;
    font-family: 'Poppins', sans-serif;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 60px;
    margin-bottom: 40px;
    font-family: 'Poppins', sans-serif;
}

.footer-brand h3 {
    color: #C4A962;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 15px;
    font-family: 'Poppins', sans-serif;
}

.footer-brand p {
    color: #ccc;
    font-weight: 300;
    font-family: 'Poppins', sans-serif;
}

.footer-section .logo h3 {
    color: #C4A962;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 5px;
    font-family: 'Poppins', sans-serif;
}

.footer-section .logo span {
    color: #ccc;
    font-size: 0.9rem;
    font-weight: 300;
    letter-spacing: 2px;
    text-transform: uppercase;
    font-family: 'Poppins', sans-serif;
}

.footer-section p {
    color: #ccc;
    line-height: 1.6;
    margin-top: 15px;
    font-family: 'Poppins', sans-serif;
}

.footer-links {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
}

.footer-section h4 {
    color: #C4A962;
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 20px;
    font-family: 'Poppins', sans-serif;
}

.footer-section ul {
    list-style: none;
}

.footer-section li {
    margin-bottom: 10px;
}

.footer-section a {
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s ease;
    font-family: 'Poppins', sans-serif;
}

.footer-section a:hover {
    color: #C4A962;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 20px;
    text-align: center;
    color: #999;
    font-family: 'Poppins', sans-serif;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: #fff;
    margin: 5% auto;
    padding: 40px;
    border-radius: 15px;
    width: 90%;
    max-width: 500px;
    position: relative;
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.close {
    position: absolute;
    right: 20px;
    top: 20px;
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
    color: #999;
    transition: color 0.3s ease;
}

.close:hover {
    color: #C4A962;
}

.auth-tabs {
    display: flex;
    margin-bottom: 30px;
    border-bottom: 1px solid #eee;
}

.tab-btn {
    flex: 1;
    padding: 15px;
    border: none;
    background: none;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    color: #999;
    transition: all 0.3s ease;
}

.tab-btn.active {
    color: #C4A962;
    border-bottom: 2px solid #C4A962;
}

.auth-form h3 {
    color: #2d2d2d;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 30px;
    text-align: center;
}

.auth-form .form-group {
    margin-bottom: 20px;
}

.auth-form input {
    width: 100%;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.auth-form input:focus {
    outline: none;
    border-color: #C4A962;
}

/* Large Tablet Responsive (iPad Pro, iPad Air) */
@media (max-width: 1024px) and (min-width: 769px) {
    .services-grid:not(.carousel-track) {
        grid-template-columns: repeat(2, 1fr);
        gap: 25px;
        max-width: 900px;
        margin: 0 auto;
        padding: 0 20px;
    }

    .service-card {
        min-height: 380px; /* Consistent height with desktop */
        height: auto;
        min-width: 300px;
        max-width: 100%;
        margin-bottom: 25px;
    }

    .service-image {
        height: 180px; /* Good image size for tablet */
    }

    .service-content {
        padding: 18px; /* Comfortable padding */
    }

    .service-name {
        font-size: 1.3rem;
        margin-bottom: 8px;
    }

    .service-description {
        font-size: 0.95rem;
        line-height: 1.4;
        margin-bottom: 12px;
        /* Limit to 4 lines for service cards/slides */
        display: -webkit-box;
        -webkit-line-clamp: 4;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .service-group {
        gap: 20px;
        padding: 0 15px;
    }

    .service-group .service-card {
        flex: 0 0 calc(50% - 12px);
        max-width: calc(50% - 12px);
        min-height: 320px;
    }
}

    /* Hide navigation buttons on large tablet - all services are visible */
    .carousel-nav-btn {
        display: none; /* Hide navigation buttons since all services show */
    }

    /* Hide pagination dots on large tablet */
    .carousel-indicators {
        display: none;
    }

    .services-carousel-container {
        gap: 0; /* Remove gap since nav buttons are hidden */
    }
}

/* Small Tablet Responsive (iPad Mini, small tablets) */
@media (max-width: 768px) and (min-width: 481px) {
    .services-grid:not(.carousel-track) {
        grid-template-columns: 1fr; /* Single column for better readability */
        gap: 25px;
        max-width: 650px;
        margin: 0 auto;
        padding: 0 25px;
    }

    .service-card {
        min-height: 380px; /* Consistent height for better visual balance */
        max-width: 100%;
        margin-bottom: 30px; /* Increased margin for better separation */
    }

    .service-image {
        height: 160px; /* Good image size */
    }

    .service-content {
        padding: 16px; /* Comfortable padding */
    }

    .service-name {
        font-size: 1.2rem;
        margin-bottom: 8px;
    }

    .service-description {
        font-size: 0.9rem;
        line-height: 1.4;
        margin-bottom: 12px;
        /* Limit to 4 lines for service cards/slides */
        display: -webkit-box;
        -webkit-line-clamp: 4;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .service-group {
        gap: 20px;
        padding: 0 20px;
    }

    .service-group .service-card {
        flex: 0 0 100%;
        max-width: 100%;
        min-height: 240px;
    }

    /* Hide navigation buttons on small tablet */
    .carousel-nav-btn {
        display: none;
    }

    /* Hide pagination dots on small tablet */
    .carousel-indicators {
        display: none;
    }
}

/* Mobile and Small Tablet Responsive */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav-menu {
        position: fixed;
        top: 80px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 80px);
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(15px);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        transition: left 0.3s ease;
        padding: 50px 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-menu ul {
        flex-direction: column;
        gap: 30px;
        text-align: center;
    }

    .nav-menu a {
        font-family: 'Poppins', sans-serif !important;
        font-size: 18px !important;
        padding: 15px 25px;
        font-weight: 600 !important;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .about-content,
    .contact-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .mission-vision {
        grid-template-columns: 1fr;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .footer-links {
        grid-template-columns: 1fr;
        gap: 30px;
    }

    .services-grid:not(.carousel-track) {
        grid-template-columns: 1fr;
        gap: 20px;
        max-width: 100%;
        margin: 0 auto;
        padding: 0 10px;
    }

    .service-card {
        min-height: 350px;
        margin: 0 auto;
        max-width: 400px;
        width: 100%;
    }

    .gallery-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .section-header-content {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .section-header-action {
        margin-left: 0;
    }

    .view-more-link {
        font-size: 0.9rem;
        padding: 6px 12px;
    }

    /* About New - Mobile */
    .about-new-content {
        grid-template-columns: 1fr;
        gap: 40px;
        padding: 0 15px;
    }

    .about-images-container {
        max-width: 100%;
        order: 1;
    }

    .about-large-image {
        height: 250px;
    }

    .about-small-image {
        width: 140px;
        height: 110px;
        top: -15px;
        left: -15px;
        border: 3px solid #fff;
    }

    .about-content-container {
        order: 2;
        text-align: center;
        padding: 0;
    }

    .about-text-content {
        max-width: 100%;
    }

    .about-title {
        font-size: 1.8rem;
    }

    .about-description {
        font-size: 0.9rem;
        text-align: center;
    }

    .about-cta {
        align-self: center;
    }

    .about-nav {
        display: none;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .section-header h2 {
        font-size: 2rem;
    }

    .modal-content {
        margin: 10% auto;
        padding: 30px 20px;
    }

    .services-grid:not(.carousel-track) {
        grid-template-columns: 1fr; /* Single column on mobile */
        padding: 0 20px;
        gap: 40px; /* Increased gap for better separation */
        max-width: 100%;
        margin: 0 auto;
        /* Ensure proper spacing for mobile scroll */
        scroll-snap-type: y mandatory;
    }

    /* Extra small devices - improve services cards */
    .services-carousel-container {
        padding: 0 5px; /* Reduce padding for very small screens */
    }

    .service-group {
        gap: 20px; /* Slightly reduce gap for very small screens */
        padding: 0; /* Remove padding to maximize space */
    }

    .service-card {
        min-height: 380px; /* Consistent height for visual balance */
        max-width: calc(100vw - 30px); /* Adjusted for smaller screens */
        width: calc(100vw - 30px);
        margin-bottom: 40px; /* Increased margin */
        /* Scroll snap for better mobile experience */
        scroll-snap-align: start;
    }

    .service-name {
        font-size: 1.1rem; /* Smaller title for very small screens */
        margin-bottom: 8px;
    }

    .service-description {
        font-size: 0.85rem; /* Smaller text */
        min-height: 35px; /* Reduce min-height */
        line-height: 1.3; /* Tighter line height */
        margin-bottom: 15px;
        /* Limit to 4 lines for service cards/slides */
        display: -webkit-box;
        -webkit-line-clamp: 4;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .service-image {
        height: 140px; /* Further reduce image height for very small screens */
    }

    .service-content {
        padding: 12px; /* Further reduce content padding */
    }

    .service-price {
        margin-bottom: 15px; /* Reduce margin */
    }

    .service-hours {
        font-size: 0.8rem; /* Smaller hours text */
        margin-bottom: 15px;
    }
}

/* Extra small devices - prevent overlapping */
@media (max-width: 360px) {
    .service-card {
        min-height: 380px; /* Consistent height for all devices */
        max-width: calc(100vw - 20px); /* Minimal padding for very small screens */
        width: calc(100vw - 20px);
        margin-bottom: 40px; /* More separation */
        /* Scroll snap for smooth scrolling */
        scroll-snap-align: start;
    }

    .service-image {
        height: 120px; /* Reasonable image height */
    }

    .service-content {
        padding: 12px; /* Comfortable padding */
    }

    .service-name {
        font-size: 1rem; /* Readable title */
        margin-bottom: 6px;
        line-height: 1.3;
        font-weight: 600;
    }

    .service-description {
        font-size: 0.8rem; /* Readable text */
        min-height: 30px;
        margin-bottom: 10px;
        line-height: 1.4;
        /* Limit to 4 lines for service cards/slides */
        display: -webkit-box;
        -webkit-line-clamp: 4;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .service-price {
        margin-bottom: 10px;
        font-size: 0.85rem;
        font-weight: 600;
    }

    .service-hours {
        font-size: 0.75rem;
        margin-bottom: 10px;
    }

    .btn-book {
        padding: 10px 16px; /* Comfortable button size */
        font-size: 0.85rem;
        font-weight: 600;
    }
}

/* Map Section */
.map-section {
    padding: 80px 0;
    background: #f8f9fa;
}

.map-container {
    max-width: 1000px;
    margin: 0 auto;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.map-header {
    padding: 25px 30px;
    color: white;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
}

.map-header:hover {
    background: linear-gradient(135deg, #3f9772 0%, #1D485D 100%);
}

.map-header-content h3 {
    font-family: 'Poppins', sans-serif;
    font-size: 1.4rem;
    font-weight: 600;
    margin: 0 0 5px 0;
}

.map-header-content p {
    font-family: 'Poppins', sans-serif;
    font-size: 0.9rem;
    margin: 0;
    opacity: 0.9;
}

.map-toggle-icon {
    font-size: 1.2rem;
    transition: transform 0.3s ease;
}

.map-toggle-icon.expanded {
    transform: rotate(180deg);
}

.map-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.4s ease;
    background: #f5f5f5;
}

.map-content.expanded {
    max-height: 600px;
    overflow-y: auto;
}

/* Custom scrollbar for policy content */
.map-content::-webkit-scrollbar {
    width: 8px;
}

.map-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.map-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.map-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.map-iframe-container {
    padding: 0;
    background: #f5f5f5;
}

.map-iframe-container iframe {
    width: 100%;
    height: 450px;
    border: none;
    display: block;
}

.map-placeholder {
    padding: 50px;
    text-align: center;
    color: #666;
    font-style: italic;
}

/* Policy and Notes Content Styles */
.policy-content, .notes-content {
    font-family: 'Poppins', sans-serif;
    padding: 30px;
    background: white;
    color: #333;
}

.policy-section, .notes-section {
    max-width: 800px;
    margin: 0 auto;
}

.policy-section h4, .notes-section h4 {
    font-family: 'Poppins', sans-serif;
    color: #C4A962;
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 20px;
    border-bottom: 2px solid #C4A962;
    padding-bottom: 10px;
}

.policy-item {
    margin-bottom: 25px;
}

.policy-item h5 {
    font-family: 'Poppins', sans-serif;
    color: #2d2d2d;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 10px;
}

.policy-item ul, .notes-section ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.policy-item li, .notes-section li {
    font-family: 'Poppins', sans-serif;
    padding: 8px 0;
    padding-left: 20px;
    position: relative;
    line-height: 1.6;
    color: #555;
}

.policy-item li:before, .notes-section li:before {
    content: "•";
    color: #C4A962;
    font-weight: bold;
    position: absolute;
    left: 0;
    font-size: 1.2rem;
}

/* Responsive adjustments for policy and notes */
@media (max-width: 768px) {
    .policy-content, .notes-content {
        padding: 20px;
    }

    .policy-section h4, .notes-section h4 {
        font-size: 1.2rem;
    }

    .policy-item h5 {
        font-size: 1rem;
    }
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .map-section {
        padding: 60px 0;
    }

    .map-container {
        margin: 0 15px;
        border-radius: 10px;
    }

    .map-header {
        padding: 20px;
    }

    .map-header-content h3 {
        font-size: 1.2rem;
    }

    .map-header-content p {
        font-size: 0.85rem;
    }

    .map-iframe-container iframe {
        height: 350px;
    }

    .map-content.expanded {
        max-height: 400px;
        overflow-y: auto;
    }
}

/* Loading and Animation Classes */
.fade-in {
    animation: fadeIn 0.6s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.loading {
    opacity: 0.6;
    pointer-events: none;
}

.error {
    color: #dc3545;
    font-size: 0.9rem;
    margin-top: 5px;
}

.success {
    color: #28a745;
    font-size: 0.9rem;
    margin-top: 5px;
}

/* Lightbox Styles */
.lightbox-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: fadeIn 0.3s ease;
}

.lightbox-content {
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.lightbox-content img {
  width: 100%;
  height: auto;
  max-height: 70vh;
  object-fit: contain;
  display: block;
}

.lightbox-info {
  padding: 20px;
  background: white;
}

.lightbox-info h3 {
  margin: 0 0 10px 0;
  font-size: 1.3rem;
  color: #2d2d2d;
}

.lightbox-info p {
  margin: 0;
  color: #666;
  line-height: 1.5;
}

.lightbox-close {
  position: absolute;
  top: 15px;
  right: 15px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  font-size: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s ease;
}

.lightbox-close:hover {
  background: rgba(0, 0, 0, 0.9);
}

/* Enhanced Booking Form Styles */

/* Price Estimate */
.price-estimate {
    background: rgba(196, 169, 98, 0.1);
    border: 1px solid rgba(196, 169, 98, 0.3);
    border-radius: 8px;
    padding: 1rem;
    margin: 1rem 0;
}

.price-breakdown {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.price-line {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.25rem 0;
    color: #fff;
}

.price-total {
    border-top: 1px solid rgba(196, 169, 98, 0.5);
    padding-top: 0.5rem;
    margin-top: 0.5rem;
    font-weight: 600;
    font-size: 1.1rem;
    color: #C4A962;
}

.price-deposit {
    background: rgba(196, 169, 98, 0.2);
    padding: 0.5rem;
    border-radius: 4px;
    font-weight: 500;
    color: #C4A962;
    margin-top: 0.5rem;
}

/* Accommodation dates styling */
#accommodation-dates {
    background: rgba(196, 169, 98, 0.1);
    border: 1px solid rgba(196, 169, 98, 0.3);
    border-radius: 8px;
    padding: 1rem;
    margin: 0.5rem 0;
}

#accommodation-dates .form-group label {
    color: #C4A962;
    font-weight: 600;
}

/* Small Tablet and Mobile Responsive */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }

    .hero h1 {
        font-size: 2.5rem;
    }

    .hero p {
        font-size: 1.1rem;
    }

    /* Services section mobile adjustments */
    .services {
        padding: 30px 0 60px 0; /* Increased bottom padding */
        margin-bottom: 60px; /* Increased margin to separate from next section */
        overflow: hidden; /* Prevent cards from overflowing */
        position: relative; /* Ensure proper positioning */
        z-index: 1; /* Lower z-index than about section */
        /* Optimize for mobile scrolling */
        scroll-behavior: smooth;
        /* Ensure section contains its content */
        contain: layout;
    }

    /* Services carousel mobile adjustments */
    .services-carousel-container {
        flex-direction: column;
        gap: 0; /* Remove gap since nav buttons are hidden */
        padding: 0 10px; /* Reduced padding to prevent overflow */
        margin-bottom: 30px; /* Increased bottom margin */
        overflow: visible; /* Allow content to show */
        position: relative; /* Ensure proper positioning */
        z-index: auto; /* Reset z-index */
        /* Ensure proper box sizing */
        box-sizing: border-box;
        width: 100%;
    }

    .carousel-nav-btn {
        display: none; /* Hide navigation buttons on mobile */
    }

    /* Hide pagination dots on mobile */
    .carousel-indicators {
        display: none;
    }

    .services-carousel-wrapper {
        overflow: visible; /* Allow all services to show on mobile */
        width: 100%;
        position: relative; /* Ensure proper positioning */
        /* Ensure all content is visible */
        height: auto !important;
        max-height: none !important;
    }

    /* Disable carousel transform on mobile - show all services */
    .services-grid.carousel-track {
        transform: none !important; /* Override carousel transform */
        flex-direction: column; /* Stack service groups vertically */
        gap: 0;
    }

    .service-group {
        flex-direction: column; /* Stack cards vertically in each group */
        gap: 20px; /* Comfortable gap between cards */
        padding: 0 15px; /* Comfortable padding to prevent edge cutoff */
        max-height: none; /* Remove height constraints */
        overflow: visible; /* Allow all cards to show */
        margin-bottom: 20px; /* Reduced margin since we're stacking */
        position: relative; /* Ensure proper positioning */
        /* Ensure all cards are displayed */
        display: flex !important;
        width: 100% !important;
        /* Override desktop carousel constraints */
        min-width: auto !important;
        flex-shrink: 1 !important;
        /* Ensure proper box sizing */
        box-sizing: border-box;
    }

    .service-card {
        min-width: auto;
        max-width: 100%; /* Use container width instead of viewport */
        width: 100%; /* Full container width */
        flex: none;
        margin: 0 0 20px 0; /* Remove auto margins that might cause overflow */
        height: auto;
        min-height: 480px; /* Increased to accommodate button */
        max-height: none;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
        overflow: visible; /* Allow button to show */
        position: static; /* Changed from relative to static */
        z-index: auto; /* Reset z-index */
        /* Maintain desktop proportions */
        display: flex;
        flex-direction: column;
        /* Ensure proper box sizing */
        box-sizing: border-box;
    }

    .service-image {
        height: 200px; /* Maintain good proportion like desktop */
        flex-shrink: 0; /* Prevent image from shrinking */
    }

    .service-content {
        padding: 20px; /* Comfortable padding like desktop */
        flex: 1; /* Allow content to fill available space */
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        min-height: 250px; /* Increased to accommodate button */
        max-height: none; /* Remove height limit */
    }

    .service-name {
        font-size: 1.2rem; /* Larger title for mobile */
        margin-bottom: 8px;
        font-weight: 600;
        line-height: 1.3;
    }

    .service-description {
        font-size: 0.9rem; /* Readable text size */
        line-height: 1.5;
        margin-bottom: 15px;
        color: #666;
        flex: 1; /* Allow description to expand */
        /* Limit to 4 lines for service cards/slides */
        display: -webkit-box;
        -webkit-line-clamp: 4;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .service-price {
        margin-bottom: 12px;
        font-weight: 600;
        font-size: 1rem;
    }

    .service-hours {
        font-size: 0.85rem;
        margin-bottom: 15px;
        color: #888;
    }

    .btn-book {
        padding: 14px 24px; /* Larger touch target */
        font-size: 1rem;
        font-weight: 600;
        margin-top: auto; /* Push button to bottom */
        margin-bottom: 0; /* Ensure no bottom margin */
        width: 100%; /* Full width for better mobile UX */
        box-sizing: border-box; /* Include padding in width calculation */
        flex-shrink: 0; /* Prevent button from shrinking */
    }

    /* Add scroll snap for mobile cards */
    .service-card {
        scroll-snap-align: start;
    }

    .service-description {
        min-height: 30px; /* Further reduce description min-height */
        max-height: 60px; /* Limit description height */
        font-size: 0.85rem;
        line-height: 1.3;
        overflow: hidden; /* Hide overflow text */
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 3; /* Limit to 3 lines */
        -webkit-box-orient: vertical;
    }

    .service-name {
        font-size: 1.1rem; /* Smaller title on mobile */
        margin-bottom: 6px;
        max-height: 45px; /* Limit title height */
        overflow: hidden;
        line-height: 1.2;
    }

    .service-price, .service-time {
        font-size: 0.85rem; /* Smaller font for mobile */
        margin-bottom: 4px;
        line-height: 1.2;
    }

    .service-price {
        font-weight: 600;
        color: #C4A962;
    }

    .service-time {
        color: #666;
        font-style: italic;
    }

    .service-group .service-card {
        flex: 0 0 100%; /* Full width for mobile carousel */
        max-width: 100%;
        margin: 0 auto;
    }

    .services-grid:not(.carousel-track) {
        grid-template-columns: 1fr;
    }

    .about-content {
        flex-direction: column;
    }

    .about-image {
        margin-top: 2rem;
    }

    .gallery-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .contact-content {
        flex-direction: column;
    }

    .booking-form {
        margin-top: 2rem;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .nav-links {
        display: none;
    }

    .mobile-menu {
        display: block;
    }

    .mobile-menu.active + .nav-links {
        display: flex;
        flex-direction: column;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: rgba(0, 0, 0, 0.95);
        padding: 1rem;
    }
}

/* Desktop - Ensure navigation buttons and pagination dots are visible */
@media (min-width: 1025px) {
    .carousel-nav-btn {
        display: flex !important; /* Show navigation buttons on desktop */
    }

    .carousel-indicators {
        display: flex !important; /* Show pagination dots on desktop */
    }
}

/* Language Switcher Styles */
.language-switcher {
    display: flex !important;
    align-items: center;
    gap: 8px;
    flex: 0 0 auto; /* Don't grow or shrink */
    order: 2; /* After nav-menu */
    position: relative;
    visibility: visible !important;
    opacity: 1 !important;
    z-index: 1000;
    margin-left: 15px;
}

.lang-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    border: 2px solid var(--primary-green);
    background: transparent;
    color: var(--primary-green);
    border-radius: 6px;
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 60px;
    justify-content: center;
}

.lang-btn:hover {
    background: var(--primary-green);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(63, 151, 114, 0.3);
}

.lang-btn.active {
    background: var(--primary-green);
    color: white;
    box-shadow: 0 2px 8px rgba(63, 151, 114, 0.4);
}

.lang-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.flag-emoji {
    font-size: 16px;
    line-height: 1;
}

.google-icon {
    font-size: 12px;
    margin-left: 2px;
    opacity: 0.8;
}

.translation-loading {
    display: flex;
    align-items: center;
    gap: 6px;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.95);
    padding: 8px 12px;
    border-radius: 6px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
    font-size: 12px;
    color: var(--primary-green);
    font-weight: 500;
    white-space: nowrap;
    z-index: 10;
}

.loading-spinner {
    width: 12px;
    height: 12px;
    border: 2px solid var(--secondary-green);
    border-top: 2px solid var(--primary-green);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Mobile Language Switcher */
@media (max-width: 768px) {
    .language-switcher {
        margin-left: 10px;
        gap: 6px;
    }

    .lang-btn {
        padding: 6px 8px;
        font-size: 12px;
        min-width: 50px;
    }

    .flag-emoji {
        font-size: 14px;
    }

    .translation-loading {
        font-size: 11px;
        padding: 6px 10px;
    }
}

@media (max-width: 480px) {
    .language-switcher {
        margin-left: 8px;
        gap: 4px;
    }

    .lang-btn {
        padding: 5px 6px;
        font-size: 11px;
        min-width: 45px;
        gap: 4px;
    }

    .flag-emoji {
        font-size: 12px;
    }
}
