<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gallery - Phong Nha Valley Glamping</title>
    <meta name="description" content="Khám phá bộ sưu tập hình <PERSON>nh tuyệt đẹp của Phong Nha Valley Glamping - trải nghiệm thiên nhiên hoang dã tại Quảng Bình">
    <link rel="stylesheet" href="/assets/css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!--
    ========================================
    GALLERY PAGE STRUCTURE:
    ========================================

    1. HEADER (Static HTML) - Handles:
       - Navigation menu
       - Language switcher (VI/EN buttons)
       - Mobile hamburger menu
       - Logo and branding

    2. GALLERY APP (Vue.js Component) - Handles:
       - Hero section with background image
       - Gallery grid with images from API
       - Lightbox/popup functionality
       - Image navigation and pagination
       - Loading states and error handling

    ========================================
    -->

    <!-- Header - Static HTML with Navigation & Language Switcher -->
    <!-- Header is now handled by Vue GalleryApp component -->

    <!-- Vue Gallery App - Handles Gallery Content & Functionality -->
    <div id="gallery-app">
        <!--
        Vue GalleryApp component will be mounted here and handles:
        - Hero section with dynamic background image
        - Gallery grid with images loaded from API
        - Lightbox/popup functionality for image viewing
        - Image navigation (prev/next/dots)
        - Loading states and error handling
        - Pagination for large image collections
        -->
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="logo">
                        <h3>Phong Nha</h3>
                        <span>VALLEY GLAMPING</span>
                    </div>
                    <p>Experience nature-integrated glamping beside Chày River. Connect with nature and foster positive energy in our tranquil retreat.</p>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <ul>
                        <li><a href="/">Home</a></li>
                        <li><a href="/about.html">About Us</a></li>
                        <li><a href="/#services">Services</a></li>
                        <li><a href="/gallery.html">Gallery</a></li>
                        <li><a href="/contact.html">Contact</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Contact Info</h4>
                    <div class="contact-item">
                        <i class="fas fa-phone"></i>
                        <span>+84 123 456 789</span>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-envelope"></i>
                        <span><EMAIL></span>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>Phong Nha, Quảng Bình, Vietnam</span>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>Follow Us</h4>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-youtube"></i></a>
                        <a href="#"><i class="fab fa-tripadvisor"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 Phong Nha Valley Glamping. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="/assets/js/gallery.js"></script>

    <!-- Language Switcher Script -->
    <script>
        let currentLanguage = 'vi';
        let translating = false;

        // Language switcher functionality
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔍 DEBUG: Gallery page DOMContentLoaded');
            const viBtn = document.getElementById('viBtn');
            const enBtn = document.getElementById('enBtn');
            const translationLoading = document.getElementById('translationLoading');

            console.log('🔍 DEBUG: Language switcher elements found:', {
                viBtn: !!viBtn,
                enBtn: !!enBtn,
                translationLoading: !!translationLoading
            });

            viBtn.addEventListener('click', () => {
                console.log('🔍 DEBUG: VI button clicked');
                switchLanguage('vi');
            });
            enBtn.addEventListener('click', () => {
                console.log('🔍 DEBUG: EN button clicked');
                switchLanguage('en');
            });

            function switchLanguage(lang) {
                console.log('🔍 DEBUG: switchLanguage called with:', lang);
                if (translating || currentLanguage === lang) {
                    console.log('🔍 DEBUG: Switch blocked - translating:', translating, 'currentLanguage:', currentLanguage);
                    return;
                }

                translating = true;
                translationLoading.style.display = 'flex';

                // Update button states
                viBtn.classList.toggle('active', lang === 'vi');
                enBtn.classList.toggle('active', lang === 'en');

                if (lang === 'en') {
                    translateToEnglish();
                } else {
                    restoreVietnamese();
                }

                currentLanguage = lang;
            }

            function translateToEnglish() {
                const translations = {
                    // Navigation
                    'Home': 'Home',
                    'About Us': 'About Us',
                    'Services': 'Services',
                    'Gallery': 'Gallery',
                    'Menu': 'Menu',
                    'Contact': 'Contact',
                    'Call': 'Call',

                    // Gallery specific
                    'Thư viện ảnh': 'Photo Gallery',
                    'Khám phá vẻ đẹp của thung lũng': 'Discover the beauty of the valley',
                    'Xem thêm': 'View More',
                    'Tải thêm': 'Load More',

                    // Footer
                    'Quick Links': 'Quick Links',
                    'Contact Info': 'Contact Info',
                    'Follow Us': 'Follow Us',
                    'All rights reserved': 'All rights reserved'
                };

                applyTranslations(translations);

                setTimeout(() => {
                    translating = false;
                    translationLoading.style.display = 'none';
                    document.querySelector('#enBtn .google-icon').style.display = 'inline';
                }, 1000);
            }

            function restoreVietnamese() {
                const reverseTranslations = {
                    // Navigation
                    'Home': 'Trang Chủ',
                    'About Us': 'Về Phong Nha',
                    'Services': 'Dịch Vụ',
                    'Gallery': 'Thư viện',
                    'Menu': 'Menu',
                    'Contact': 'Liên hệ',
                    'Call': 'Gọi ngay',

                    // Gallery specific
                    'Photo Gallery': 'Thư viện ảnh',
                    'Discover the beauty of the valley': 'Khám phá vẻ đẹp của thung lũng',
                    'View More': 'Xem thêm',
                    'Load More': 'Tải thêm',

                    // Footer
                    'Quick Links': 'Liên kết nhanh',
                    'Contact Info': 'Thông tin liên hệ',
                    'Follow Us': 'Theo dõi chúng tôi',
                    'All rights reserved': 'Bảo lưu mọi quyền'
                };

                applyTranslations(reverseTranslations);

                setTimeout(() => {
                    translating = false;
                    translationLoading.style.display = 'none';
                    document.querySelector('#enBtn .google-icon').style.display = 'none';
                }, 1000);
            }

            function applyTranslations(translations) {
                Object.keys(translations).forEach(originalText => {
                    const translatedText = translations[originalText];

                    const walker = document.createTreeWalker(
                        document.body,
                        NodeFilter.SHOW_TEXT,
                        null,
                        false
                    );

                    const textNodes = [];
                    let node;

                    while (node = walker.nextNode()) {
                        if (node.textContent.trim() === originalText.trim()) {
                            textNodes.push(node);
                        }
                    }

                    textNodes.forEach(textNode => {
                        textNode.textContent = translatedText;
                    });
                });
            }
        });
    </script>
</body>
</html>
