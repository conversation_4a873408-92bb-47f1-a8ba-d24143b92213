<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>About Us - Phong Nha Valley Glamping</title>
    <meta name="description" content="Tìm hiểu về Phong Nha Valley Glamping - điểm đến lý tưởng cho những trải nghiệm thiên nhiên độc đáo tại Quảng Bình">
    <link rel="stylesheet" href="/assets/css/style.css?v=20250721">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="nav-brand">
                <a href="/" class="logo-link">
                    <div class="logo">
                        <span>VALLEY GLAMPING</span>
                    </div>
                </a>
            </div>
            <nav class="nav-menu" id="navMenu">
                <ul>
                    <li><a href="/">Trang Chủ</a></li>
                    <li><a href="/about.html" class="active">Về Phong Nha</a></li>
                    <li><a href="/services.html">Dịch Vụ</a></li>
                    <li><a href="/gallery.html">Thư Viện</a></li>
                    <li><a href="/menu.html">Menu</a></li>                    
                    <li><a href="/contact.html">Liên Hệ</a></li>
                    <li><a href="#" class="btn-call" id="callButton">Gọi Ngay</a></li>
                </ul>
            </nav>

            <!-- Language Switcher -->
            <div class="language-switcher">
                <button
                    class="lang-btn active"
                    id="viBtn"
                    title="Tiếng Việt"
                >
                    <span class="flag-emoji">🇻🇳</span>
                    VI
                </button>
                <button
                    class="lang-btn"
                    id="enBtn"
                    title="English - Powered by Google Translate"
                >
                    <span class="flag-emoji">🇬🇧</span>
                    EN
                    <span class="google-icon" style="display: none;">🌐</span>
                </button>
                <div id="translationLoading" class="translation-loading" style="display: none;">
                    <span class="loading-spinner"></span>
                    <span>Translating...</span>
                </div>
            </div>

            <div class="hamburger" id="hamburgerMenu">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </header>

    <!-- Vue About App -->
    <div id="about-app">
        <!-- Vue component will be mounted here -->
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <img src="https://via.placeholder.com/150x60/27ae60/ffffff?text=PHONG+NHA" alt="Phong Nha Valley" class="logo">
                </div>
                <p>&copy; 2024 Phong Nha Valley Glamping. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <button id="backToTop" class="back-to-top">
        <i class="icon-arrow-up"></i>
    </button>

    <!-- Load Vue About App -->
    <script src="/assets/js/about.js"></script>

    <!-- Common JavaScript for header/footer functionality -->
    <script>
        // Setup event listeners when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Setup call button handler
            const callButton = document.getElementById('callButton');
            if (callButton) {
                callButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    handleCallAction();
                });
            }

            // Setup hamburger menu handler
            const hamburgerMenu = document.getElementById('hamburgerMenu');
            if (hamburgerMenu) {
                hamburgerMenu.addEventListener('click', function() {
                    toggleMenu();
                });
            }

            // Setup back to top button handler
            const backToTopButton = document.getElementById('backToTop');
            if (backToTopButton) {
                backToTopButton.addEventListener('click', function() {
                    scrollToTop();
                });
            }
        });

        // Mobile menu toggle
        function toggleMenu() {
            const navMenu = document.getElementById('navMenu');
            const hamburger = document.querySelector('.hamburger');

            if (navMenu) navMenu.classList.toggle('active');
            if (hamburger) hamburger.classList.toggle('active');
        }

        // Back to top functionality
        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        // Handle call action (mobile vs desktop)
        function handleCallAction() {
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

            if (isMobile) {
                // On mobile, trigger phone call
                window.location.href = 'tel:+84123456789';
            } else {
                // On desktop, scroll to contact section on homepage
                window.location.href = '/#contact';
            }
        }

        // Show/hide back to top button
        window.addEventListener('scroll', () => {
            const backToTop = document.getElementById('backToTop');
            if (backToTop) {
                if (window.scrollY > 300) {
                    backToTop.style.display = 'block';
                } else {
                    backToTop.style.display = 'none';
                }
            }

            // Header scroll effect
            const header = document.querySelector('.header');
            if (header) {
                if (window.scrollY > 100) {
                    header.style.background = 'rgba(45, 45, 45, 0.98)';
                } else {
                    header.style.background = 'rgba(45, 45, 45, 0.95)';
                }
            }
        });

        // Placeholder auth modal function
        function openAuthModal() {
            alert('Authentication modal would open here. Please go to homepage for full functionality.');
        }

        // Language switcher functionality
        let currentLanguage = 'vi';
        let translating = false;

        console.log('🔍 DEBUG: About page script loaded');
        const viBtn = document.getElementById('viBtn');
        const enBtn = document.getElementById('enBtn');
        const translationLoading = document.getElementById('translationLoading');

        console.log('🔍 DEBUG: About language switcher elements found:', {
            viBtn: !!viBtn,
            enBtn: !!enBtn,
            translationLoading: !!translationLoading
        });

        if (viBtn && enBtn) {
            viBtn.addEventListener('click', () => switchLanguage('vi'));
            enBtn.addEventListener('click', () => switchLanguage('en'));
        }

        function switchLanguage(lang) {
            if (translating || currentLanguage === lang) return;

            translating = true;
            translationLoading.style.display = 'flex';

            // Update button states
            viBtn.classList.toggle('active', lang === 'vi');
            enBtn.classList.toggle('active', lang === 'en');

            if (lang === 'en') {
                translateToEnglish();
            } else {
                restoreVietnamese();
            }

            currentLanguage = lang;
        }

        function translateToEnglish() {
            const translations = {
                // Navigation
                'Home': 'Home',
                'About Us': 'About Us',
                'Services': 'Services',
                'Gallery': 'Gallery',
                'Contact': 'Contact',
                'Call': 'Call',

                // About specific
                'Về chúng tôi': 'About Us',
                'Thung lũng xanh': 'Green Valley',
                'Xem thêm': 'View More',
                'Đặt ngay': 'Book Now',

                // Footer
                'Quick Links': 'Quick Links',
                'Contact Info': 'Contact Info',
                'Follow Us': 'Follow Us',
                'All rights reserved': 'All rights reserved'
            };

            applyTranslations(translations);

            setTimeout(() => {
                translating = false;
                translationLoading.style.display = 'none';
                document.querySelector('#enBtn .google-icon').style.display = 'inline';
            }, 1000);
        }

        function restoreVietnamese() {
            const reverseTranslations = {
                // Navigation
                'Home': 'Trang Chủ',
                'About Us': 'Về Phong Nha',
                'Services': 'Dịch Vụ',
                'Gallery': 'Thư viện',
                'Contact': 'Liên hệ',
                'Call': 'Gọi ngay',

                // About specific
                'About Us': 'Về chúng tôi',
                'Green Valley': 'Thung lũng xanh',
                'View More': 'Xem thêm',
                'Book Now': 'Đặt ngay',

                // Footer
                'Quick Links': 'Liên kết nhanh',
                'Contact Info': 'Thông tin liên hệ',
                'Follow Us': 'Theo dõi chúng tôi',
                'All rights reserved': 'Bảo lưu mọi quyền'
            };

            applyTranslations(reverseTranslations);

            setTimeout(() => {
                translating = false;
                translationLoading.style.display = 'none';
                document.querySelector('#enBtn .google-icon').style.display = 'none';
            }, 1000);
        }

        function applyTranslations(translations) {
            Object.keys(translations).forEach(originalText => {
                const translatedText = translations[originalText];

                const walker = document.createTreeWalker(
                    document.body,
                    NodeFilter.SHOW_TEXT,
                    null,
                    false
                );

                const textNodes = [];
                let node;

                while (node = walker.nextNode()) {
                    if (node.textContent.trim() === originalText.trim()) {
                        textNodes.push(node);
                    }
                }

                textNodes.forEach(textNode => {
                    textNode.textContent = translatedText;
                });
            });
        }
    </script>
</body>
</html>
