<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Us - Phong Nha Valley</title>
    <!-- Cache Buster: 20250714-v2 -->
    <meta name="description" content="Get in touch with Phong Nha Valley. Contact us for bookings, inquiries, and assistance for your perfect adventure in Phong Nha-Ke Bang National Park.">
    <meta name="keywords" content="contact, phong nha, booking, inquiry, adventure tours, vietnam">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/assets/images/favicon.ico">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Vue.js -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/assets/css/style.css?v=20250721">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="Contact Us - Phong Nha Valley">
    <meta property="og:description" content="Get in touch with Phong Nha Valley for your perfect adventure in Phong Nha-Ke Bang National Park.">
    <meta property="og:image" content="/assets/images/hero-bg.jpg">
    <meta property="og:url" content="/contact.html">
    <meta property="og:type" content="website">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Contact Us - Phong Nha Valley">
    <meta name="twitter:description" content="Get in touch with Phong Nha Valley for your perfect adventure.">
    <meta name="twitter:image" content="/assets/images/hero-bg.jpg">
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "TouristAttraction",
      "name": "Phong Nha Valley",
      "description": "Adventure tours and experiences in Phong Nha-Ke Bang National Park",
      "url": "/contact.html",
      "telephone": "************",
      "email": "<EMAIL>",
      "address": {
        "@type": "PostalAddress",
        "streetAddress": "Chày Lập Hamlet, Phúc Trạch Commune",
        "addressLocality": "Bố Trạch District",
        "addressRegion": "Quảng Bình Province",
        "addressCountry": "Vietnam"
      },
      "openingHours": "Mo-Su 06:30-21:00",
      "contactPoint": {
        "@type": "ContactPoint",
        "telephone": "************",
        "contactType": "customer service",
        "email": "<EMAIL>"
      }
    }
    </script>
</head>
<body>


    <!-- Main Content -->
    <main>
        <div id="contact-app">
            <!-- Vue.js ContactApp component will be mounted here -->
        </div>
    </main>

    <!-- Scripts -->
    <script src="/assets/js/contact.js?v=20250714"></script>
    <script>
        // Initialize navigation (same as homepage)
        function toggleMenu() {
            const navMenu = document.getElementById('navMenu');
            const hamburger = document.querySelector('.hamburger');

            navMenu.classList.toggle('active');
            hamburger.classList.toggle('active');
        }

        // Handle call button click (same as homepage)
        function handleCallClick() {
            const phoneNumber = '************';
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

            if (isMobile) {
                window.location.href = `tel:${phoneNumber}`;
            } else {
                alert(`Please call us at: ${phoneNumber}`);
            }
        }

        // Setup event listeners when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Setup hamburger menu handler
            const hamburgerMenu = document.getElementById('hamburgerMenu');
            if (hamburgerMenu) {
                hamburgerMenu.addEventListener('click', function() {
                    toggleMenu();
                });
            }

            // Setup call button handler
            const callButton = document.getElementById('callButton');
            if (callButton) {
                callButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    handleCallClick();
                });
            }

            // Close mobile menu when clicking on links
            const navLinks = document.querySelectorAll('.nav-menu a');
            const navMenu = document.getElementById('navMenu');
            const hamburger = document.querySelector('.hamburger');

            navLinks.forEach(link => {
                link.addEventListener('click', () => {
                    navMenu.classList.remove('active');
                    hamburger.classList.remove('active');
                });
            });
        });
    </script>
</body>
</html>
