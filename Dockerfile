# Phong Nha Valley - Local Development Dockerfile
# Simplified single-stage build for local development

FROM golang:1.21-alpine

# Install runtime dependencies
RUN apk add --no-cache \
    curl \
    ca-certificates \
    tzdata \
    postgresql-client \
    netcat-openbsd

# Set timezone
ENV TZ=Asia/Ho_Chi_Minh
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Create application directory
WORKDIR /app

# Copy go mod files
COPY backend/go.mod backend/go.sum ./backend/
WORKDIR /app/backend
RUN go mod download

# Copy backend source
COPY backend/ ./

# Build backend binary
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o phongnha-server main.go

# Copy frontend build (already built by make build)
WORKDIR /app
COPY frontend/public ./frontend/public

# Copy migrations
COPY backend/migrations ./migrations

# Create necessary directories
RUN mkdir -p /app/uploads /app/logs

# Environment variables
ENV PORT=8080
ENV GIN_MODE=release
ENV UPLOAD_DIR=/app/uploads
ENV LOG_FILE=/app/logs/app.log

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Start the application
CMD ["./backend/phongnha-server"]
