name: Deploy to Fly.io

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]

env:
  FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}
  FLY_APP_NAME: phongnha-2
  FLY_DB_APP_NAME: phongnha-db

jobs:
  deploy:
    name: Deploy app
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Fly CLI
      uses: superfly/flyctl-actions/setup-flyctl@master

    - name: Build and deploy
      run: |
        echo "🚀 Starting deployment to Fly.io"
        flyctl deploy --remote-only --app $FLY_APP_NAME
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

    - name: Run database migrations
      run: |
        echo "📊 Running database migrations"
        flyctl ssh console --app $FLY_APP_NAME -C "/usr/local/bin/phongnha-server migrate"
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

    - name: Seed database (if needed)
      run: |
        echo "🌱 Seeding database with initial data"
        flyctl ssh console --app $FLY_APP_NAME -C "/usr/local/bin/phongnha-server seed"
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}
      continue-on-error: true

    - name: Create admin user
      run: |
        echo "👤 Creating admin user"
        flyctl postgres connect --app $FLY_DB_APP_NAME -c "
        INSERT INTO users (id, email, password, first_name, last_name, phone, role, created_at, updated_at)
        VALUES (
          gen_random_uuid()::text,
          '<EMAIL>',
          crypt('admin123', gen_salt('bf')),
          'Admin',
          'User',
          '',
          'admin',
          NOW(),
          NOW()
        )
        ON CONFLICT (email) DO UPDATE SET
          password = crypt('admin123', gen_salt('bf')),
          role = 'admin',
          updated_at = NOW();
        "
      env:
        FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}
      continue-on-error: true

    - name: Health check
      run: |
        echo "🔍 Running health check"
        sleep 30
        curl -f https://$FLY_APP_NAME.fly.dev/health || exit 1
        curl -f https://$FLY_APP_NAME.fly.dev/api/v1/health || exit 1
      continue-on-error: true

    - name: Test admin access
      run: |
        echo "🔑 Testing admin access"
        response=$(curl -s -o /dev/null -w "%{http_code}" https://$FLY_APP_NAME.fly.dev/admin)
        if [ "$response" = "200" ]; then
          echo "✅ Admin panel accessible"
        else
          echo "⚠️ Admin panel response: HTTP $response"
        fi
      continue-on-error: true

    - name: Deployment summary
      run: |
        echo "🎉 Deployment completed!"
        echo "🌐 Website: https://$FLY_APP_NAME.fly.dev"
        echo "🔧 Admin: https://$FLY_APP_NAME.fly.dev/admin"
        echo "📊 Booking: https://$FLY_APP_NAME.fly.dev/booking-manager"
