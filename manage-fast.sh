#!/bin/bash

# Fast build management script - optimized for development
# Does not interfere with existing manage.sh

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
COMPOSE_FILE="$SCRIPT_DIR/docker-compose.fast.yml"
PROJECT_NAME="phongnha_fast"

# Enable Docker BuildKit for faster builds
export DOCKER_BUILDKIT=1
export COMPOSE_DOCKER_CLI_BUILD=1

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Dock<PERSON> is running
check_docker() {
    if ! docker info >/dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi
}

# Function to build frontend only
build_frontend() {
    print_status "Building frontend..."
    cd frontend
    if [ -f "package.json" ]; then
        npm install --silent
        npm run build
        print_success "Frontend build completed"

        # Copy built files to container
        print_status "Copying frontend files to container..."
        cd ..

        # Check if container is running
        if docker ps | grep -q "phongnha_app_fast"; then
            # Copy JavaScript files
            docker cp frontend/public/assets/js/ phongnha_app_fast:/app/frontend/public/assets/

            # Copy HTML files
            docker cp frontend/public/index.html phongnha_app_fast:/app/frontend/public/
            docker cp frontend/public/about.html phongnha_app_fast:/app/frontend/public/ 2>/dev/null || true
            docker cp frontend/public/contact.html phongnha_app_fast:/app/frontend/public/ 2>/dev/null || true
            docker cp frontend/public/service.html phongnha_app_fast:/app/frontend/public/ 2>/dev/null || true
            docker cp frontend/public/admin.html phongnha_app_fast:/app/frontend/public/ 2>/dev/null || true

            # Copy CSS files
            docker cp frontend/public/assets/css/ phongnha_app_fast:/app/frontend/public/assets/ 2>/dev/null || true

            print_success "Frontend files copied to container"
        else
            print_warning "Container not running, files will be available on next start"
        fi
    else
        print_error "package.json not found in frontend directory"
        exit 1
    fi
    cd .. 2>/dev/null || true
}

# Function to build with cache optimization
fast_build() {
    print_status "Starting fast build process..."
    
    # Clean up old images to free space
    print_status "Cleaning up old images..."
    docker system prune -f --filter "until=24h" >/dev/null 2>&1 || true
    
    # Build with cache
    print_status "Building Docker image with cache optimization..."
    docker compose -f $COMPOSE_FILE -p $PROJECT_NAME build \
        --build-arg BUILDKIT_INLINE_CACHE=1 \
        --parallel
    
    print_success "Fast build completed!"
}

# Function to start services
start() {
    check_docker
    print_status "Starting services..."
    docker compose -f $COMPOSE_FILE -p $PROJECT_NAME up -d
    
    # Wait for services to be ready
    print_status "Waiting for services to be ready..."
    sleep 5
    
    # Check if app is responding
    for i in {1..30}; do
        if curl -s http://localhost:8080/health >/dev/null 2>&1; then
            print_success "Application is ready at http://localhost:8080"
            break
        fi
        if [ $i -eq 30 ]; then
            print_warning "Application may still be starting up..."
        fi
        sleep 2
    done
}

# Function to stop services
stop() {
    print_status "Stopping services..."
    docker compose -f $COMPOSE_FILE -p $PROJECT_NAME down
    print_success "Services stopped"
}

# Function to rebuild and restart
rebuild() {
    print_status "Rebuilding application..."
    
    # Stop existing containers
    docker compose -f $COMPOSE_FILE -p $PROJECT_NAME down >/dev/null 2>&1 || true
    
    # Build frontend first (faster feedback)
    build_frontend
    
    # Fast Docker build
    fast_build
    
    # Start services
    start
    
    print_success "Rebuild completed!"
}

# Function to show status
status() {
    print_status "Service Status:"
    echo
    
    # Check database
    if docker compose -f $COMPOSE_FILE -p $PROJECT_NAME ps db | grep -q "Up"; then
        echo -e "Database: ${GREEN}Running${NC}"
    else
        echo -e "Database: ${RED}Stopped${NC}"
    fi

    # Check application
    if docker compose -f $COMPOSE_FILE -p $PROJECT_NAME ps app | grep -q "Up"; then
        echo -e "Application: ${GREEN}Running${NC}"
    else
        echo -e "Application: ${RED}Stopped${NC}"
    fi
    
    echo
    print_status "Volumes:"
    docker volume ls | grep "${PROJECT_NAME}" || echo "No volumes found"
}

# Function to show logs
logs() {
    service=${2:-app}
    print_status "Showing logs for $service..."
    docker compose -f $COMPOSE_FILE -p $PROJECT_NAME logs -f $service
}

# Function to clean up everything
clean() {
    print_warning "This will remove all containers, images, and volumes for the fast build."
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_status "Cleaning up..."
        docker compose -f $COMPOSE_FILE -p $PROJECT_NAME down -v --rmi all
        print_success "Cleanup completed"
    else
        print_status "Cleanup cancelled"
    fi
}

# Function to show help
show_help() {
    echo "Fast Build Management Script"
    echo "Usage: $0 [COMMAND]"
    echo
    echo "Commands:"
    echo "  start       Start the application"
    echo "  stop        Stop the application"
    echo "  rebuild     Rebuild and restart the application"
    echo "  status      Show service status"
    echo "  logs        Show application logs"
    echo "  clean       Clean up all containers and volumes"
    echo "  frontend    Build frontend only"
    echo "  help        Show this help message"
    echo
    echo "Examples:"
    echo "  $0 rebuild    # Fast rebuild and restart"
    echo "  $0 logs app   # Show app logs"
    echo "  $0 frontend   # Build frontend only"
}

# Main script logic
case "${1:-help}" in
    start)
        start
        ;;
    stop)
        stop
        ;;
    rebuild)
        rebuild
        ;;
    status)
        status
        ;;
    logs)
        logs "$@"
        ;;
    clean)
        clean
        ;;
    frontend)
        build_frontend
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        print_error "Unknown command: $1"
        show_help
        exit 1
        ;;
esac
