# Phong Nha Valley - Production Dockerfile for Fly.io
# Multi-stage build for optimal performance and size

# Stage 1: Frontend Build
FROM node:18-alpine AS frontend-builder

LABEL stage=frontend-builder
LABEL description="Build Vue.js frontend for production"

WORKDIR /app/frontend

# Copy package files
COPY frontend/package*.json ./

# Install dependencies (including dev dependencies for build)
RUN npm ci --silent

# Copy source code
COPY frontend/ ./

# Build for production
RUN npm run build

# Verify build output
RUN ls -la public/ && echo "Frontend build completed"

# Stage 2: Backend Build
FROM golang:1.21-alpine AS backend-builder

LABEL stage=backend-builder
LABEL description="Build Go backend for production"

# Install build dependencies
RUN apk add --no-cache git ca-certificates tzdata

WORKDIR /app/backend

# Copy go mod files
COPY backend/go.mod ./
COPY backend/go.sum ./

# Download dependencies and update go.sum
RUN go mod download
RUN go mod tidy

# Copy source code
COPY backend/ ./

# Build binary with optimizations
RUN CGO_ENABLED=0 \
    GOOS=linux \
    GOARCH=amd64 \
    go build -a -installsuffix cgo \
    -ldflags="-w -s -X main.version=$(date +%Y%m%d-%H%M%S) -X main.buildTime=$(date -u +%Y-%m-%dT%H:%M:%SZ)" \
    -o phongnha-server main.go

# Copy migrations
COPY backend/migrations ./migrations

# Verify binary
RUN ls -la phongnha-server

# Stage 3: Production Runtime
FROM alpine:latest

LABEL maintainer="Phong Nha Valley Team"
LABEL description="Production container for Phong Nha Valley"
LABEL version="1.0"

# Install runtime dependencies
RUN apk add --no-cache \
    supervisor \
    curl \
    ca-certificates \
    tzdata \
    postgresql-client \
    netcat-openbsd \
    && rm -rf /var/cache/apk/*

# Set timezone
ENV TZ=Asia/Ho_Chi_Minh
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# Create application user
RUN addgroup -g 1001 -S appgroup && \
    adduser -u 1001 -S appuser -G appgroup

# Create necessary directories
RUN mkdir -p \
    /app/uploads \
    /app/logs \
    /var/log/supervisor \
    /var/log/nginx \
    /var/cache/nginx \
    /var/run/nginx \
    && chown -R appuser:appgroup /app \
    && chown -R appuser:appgroup /var/log/nginx \
    && chown -R appuser:appgroup /var/cache/nginx \
    && chown -R appuser:appgroup /var/run/nginx

# Copy frontend build from builder stage for Go server
COPY --from=frontend-builder /app/frontend/public /app/frontend/public

# Copy backend binary from builder stage
COPY --from=backend-builder /app/backend/phongnha-server /usr/local/bin/phongnha-server

# Copy migrations from builder stage
COPY --from=backend-builder /app/backend/migrations /app/migrations

# Make binary executable
RUN chmod +x /usr/local/bin/phongnha-server

# Copy configuration files
COPY deploy/supervisord.conf /etc/supervisor/conf.d/supervisord.conf
COPY deploy/auto-setup.sh /app/auto-setup.sh

# Set proper permissions for frontend files
RUN chown -R appuser:appgroup /app/frontend
RUN chmod +x /app/auto-setup.sh

# Environment variables
ENV PORT=8080
ENV GIN_MODE=release
ENV UPLOAD_DIR=/app/uploads
ENV LOG_FILE=/app/logs/app.log

# Expose port
EXPOSE 8080

# Health check - simplified for Fly.io
HEALTHCHECK --interval=60s --timeout=30s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Switch to non-root user for security
USER appuser

# Copy startup script
USER root
COPY deploy/start.sh /usr/local/bin/start.sh
RUN chmod +x /usr/local/bin/start.sh

# Final setup
WORKDIR /app

# Start the application
CMD ["/usr/local/bin/start.sh"]

# Metadata
LABEL org.opencontainers.image.title="Phong Nha Valley"
LABEL org.opencontainers.image.description="Glamping website with Vue.js frontend and Go backend"
LABEL org.opencontainers.image.version="1.0"
LABEL org.opencontainers.image.created="$(date -u +%Y-%m-%dT%H:%M:%SZ)"
LABEL org.opencontainers.image.source="https://github.com/your-repo/phongnha-valley"
