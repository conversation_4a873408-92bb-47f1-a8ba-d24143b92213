# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# Environment variables
.env

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Build artifacts
phongnha-valley
dist/
build/

# Database files
*.db
*.sqlite
*.sqlite3

# Temporary files
tmp/
temp/

# Node modules (if frontend is added)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Frontend build files
web/dist/
web/build/

# Docker volumes
postgres_data/

# Coverage reports
coverage.txt
coverage.html

# Air live reload tool
tmp/
