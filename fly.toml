# Phong Nha Valley - Fly.io Configuration
# Documentation: https://fly.io/docs/reference/configuration/

app = "phongnha-2"
primary_region = "sin"  # Singapore - closest to Vietnam

# Build configuration
[build]
  dockerfile = "Dockerfile.fly"

# Environment variables (non-sensitive)
[env]
  GIN_MODE = "release"
  PORT = "8080"
  TZ = "Asia/Ho_Chi_Minh"
  UPLOAD_DIR = "/app/uploads"
  LOG_FILE = "/app/logs/app.log"
  MAX_UPLOAD_SIZE = "10485760"
  ALLOWED_EXTENSIONS = "jpg,jpeg,png,gif,webp,svg"

  # Database configuration (matches local setup)
  DB_HOST = "phongnha-db.internal"
  DB_PORT = "5432"
  DB_USER = "postgres"
  DB_NAME = "phongnha_valley"
  DB_SSLMODE = "disable"

  # CORS configuration
  CORS_ORIGINS = "*"

  # JWT Secret (matches local setup)
  JWT_SECRET = "phongnha_valley_production_jwt_secret_key_2024_secure_random_string_for_production_use_only"

# HTTP service configuration
[http_service]
  internal_port = 8080
  force_https = true
  auto_stop_machines = true
  auto_start_machines = true
  min_machines_running = 1

  # Health checks
  [[http_service.checks]]
    grace_period = "30s"
    interval = "60s"
    method = "GET"
    timeout = "15s"
    path = "/health"

# VM configuration
[vm]
  cpu_kind = "shared"
  cpus = 1
  memory_mb = 512

# Volume mounts for persistent storage
[[mounts]]
  source = "phongnha_uploads"
  destination = "/app/uploads"

# Deploy configuration
[deploy]
  strategy = "rolling"

# Kill configuration
kill_signal = "SIGINT"
kill_timeout = 30
