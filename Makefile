.PHONY: all build run test clean migrate-up migrate-down

# Go parameters
GOCMD=go
GOBUILD=$(GOCMD) build
GORUN=$(GOCMD) run
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
BINARY_NAME=phongnha-valley

all: test build

build:
	$(GOBUILD) -o $(BINARY_NAME) -v ./backend

run:
	$(GORUN) ./backend/main.go

test:
	$(GOTEST) -v ./...

clean:
	$(GOCLEAN)
	rm -f $(BINARY_NAME)

deps:
	$(GOGET) -v ./...

migrate-up:
	$(GORUN) scripts/migrate.go up

migrate-down:
	$(GORUN) scripts/migrate.go down

# Create a new migration file
create-migration:
	@read -p "Enter migration name: " name; \
	timestamp=`date +%Y%m%d%H%M%S`; \
	echo "Creating migration $$timestamp_$$name"; \
	touch migrations/$$timestamp_$$name.up.sql; \
	touch migrations/$$timestamp_$$name.down.sql

# Start development server
dev:
	$(GORUN) ./backend/main.go

# Build and run in production mode
prod: build
	GIN_MODE=release ./$(BINARY_NAME)

# Install all dependencies
setup:
	cp .env.example .env
	$(GOGET) -v ./...
	$(MAKE) migrate-up

# Docker commands
docker-build:
	docker build -t $(BINARY_NAME) .

docker-run:
	docker run -p 8080:8080 $(BINARY_NAME)
