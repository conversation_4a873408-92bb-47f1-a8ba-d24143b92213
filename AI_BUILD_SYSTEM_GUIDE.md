# 🤖 AI Build System Guide

**Hướng dẫn cho AI Assistant khi làm việc với dự án Phong Nha Valley**

## 🎯 Khi nào sử dụng Fast Build vs Normal Build

### 🚀 **SỬ DỤNG FAST BUILD KHI:**

1. **Development & Testing**
   - Thay đổi code frontend/backend thường xuyên
   - Test tính năng mới
   - Debug và fix bugs
   - Thời gian build quan trọng (< 3 phút)

2. **Các thay đổi nhỏ**
   - Sửa CSS/JavaScript
   - Thay đổi Vue components
   - Cập nhật API endpoints
   - Sửa lỗi logic

3. **Khi user yêu cầu "nhanh"**
   - User nói "build nhanh", "test ngay"
   - Cần demo tính năng ngay lập tức
   - Đang trong quá trình development

### 🏗️ **SỬ DỤNG NORMAL BUILD KHI:**

1. **Production & Deployment**
   - Deploy lên server production
   - Build final cho release
   - Cần đảm bảo stability tối đa

2. **Thay đổi lớn**
   - Thêm dependencies mới
   - Thay đổi database schema
   - Cập nhật Docker configuration
   - Thay đổi environment variables

3. **Testing cuối cùng**
   - Trước khi commit code
   - Kiểm tra compatibility
   - Performance testing

## 📋 **Commands Reference**

### Fast Build System:
```bash
# Setup (chỉ chạy 1 lần)
bash setup-fast-build.sh

# Build và start
./manage-fast.sh rebuild

# Các lệnh khác
./manage-fast.sh start     # Start services
./manage-fast.sh stop      # Stop services
./manage-fast.sh status    # Check status
./manage-fast.sh logs      # View logs
./manage-fast.sh frontend  # Build frontend only
./manage-fast.sh clean     # Clean up
```

### Normal Build System:
```bash
./manage.sh rebuild
./manage.sh start
./manage.sh stop
./manage.sh status
```

## 🔄 **Switching Between Systems**

### Chuyển sang Fast Build:
```bash
cp .dockerignore.fast .dockerignore
./manage-fast.sh rebuild
```

### Quay về Normal Build:
```bash
cp .dockerignore.backup .dockerignore
./manage.sh rebuild
```

## 📊 **Performance Comparison**

| Aspect | Normal Build | Fast Build | Improvement |
|--------|-------------|------------|-------------|
| First Build | 10-15 min | 3-5 min | 60-70% faster |
| Rebuild | 8-12 min | 1-3 min | 75-85% faster |
| Frontend Only | 2-3 min | 30-60s | 50-75% faster |

## 🌐 **URLs & Ports**

- **Fast Build**: http://localhost:8080 (DB: 5433)
- **Normal Build**: http://localhost:3000 (DB: 5432)

## 🚨 **AI Decision Logic**

```
IF user_request.contains("nhanh", "fast", "test ngay", "demo") 
   OR development_phase = true
   OR change_type = "small"
THEN
   RECOMMEND: Fast Build System
   NOTIFY: "Tôi sẽ sử dụng Fast Build System để tiết kiệm thời gian (2-3 phút thay vì 10+ phút)"

ELSE IF production_deployment = true
   OR change_type = "major"
   OR final_testing = true
THEN
   RECOMMEND: Normal Build System
   NOTIFY: "Tôi sẽ sử dụng Normal Build System để đảm bảo stability"
```

## 🔧 **Troubleshooting**

### Fast Build không hoạt động:
```bash
# Check Docker
docker --version
docker info

# Clean và rebuild
./manage-fast.sh clean
./manage-fast.sh rebuild

# Check logs
./manage-fast.sh logs
```

### Port conflicts:
```bash
# Check ports
netstat -tulpn | grep :8080
netstat -tulpn | grep :5433

# Stop services
./manage-fast.sh stop
```

## 📝 **AI Response Templates**

### Khi sử dụng Fast Build:
```
🚀 Tôi sẽ sử dụng Fast Build System để tiết kiệm thời gian:
- Build time: 2-3 phút (thay vì 10+ phút)
- URL: http://localhost:8080
- Phù hợp cho development và testing
```

### Khi sử dụng Normal Build:
```
🏗️ Tôi sẽ sử dụng Normal Build System để đảm bảo stability:
- Build time: 8-12 phút
- URL: http://localhost:3000  
- Phù hợp cho production và major changes
```

## 🎯 **Best Practices cho AI**

1. **Luôn thông báo** build system nào đang sử dụng
2. **Giải thích lý do** tại sao chọn fast/normal build
3. **Cung cấp URL chính xác** (8080 vs 3000)
4. **Monitor build time** và thông báo nếu bất thường
5. **Suggest switching** nếu build system không phù hợp

## 📁 **File Structure**

```
├── Dockerfile.fast              # Fast build Dockerfile
├── docker-compose.fast.yml      # Fast build compose
├── manage-fast.sh               # Fast build manager
├── .dockerignore.fast           # Fast build ignore
├── setup-fast-build.sh         # Setup script
└── AI_BUILD_SYSTEM_GUIDE.md     # This guide
```
