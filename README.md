# Phong Nha Valley - Tourism Management System

A comprehensive tourism management system for Phong Nha Valley, featuring service booking, content management, and admin dashboard.

## 🌐 Live Application

- **Homepage**: https://phongnha-2.fly.dev
- **Admin Panel**: https://phongnha-2.fly.dev/admin
- **Admin Login**: `<EMAIL>` / `password`

## ✨ Features

- **Frontend**: Vue.js with responsive design using custom SVN fonts (SVN-Megante, SVN-Alluring)
- **Backend**: Go with Gin framework and PostgreSQL
- **Admin Panel**: Full content management system
- **Services**: Booking system with image galleries
- **Content**: Dynamic homepage and content blocks
- **Deployment**: Fly.io with PostgreSQL database

## 🏕️ About Phong Nha Valley

Phong Nha Valley is an experimental tourism project offering nature camping and glamping experiences at Trằm Mé – Chày Lập. We provide memorable accommodation and recreation experiences that connect visitors with nature.

## 🚀 Features

### Services Available
- **Accommodation**: Luxury glamping tents (750,000 VND/adult)
- **Afternoon Tea**: Sunset Tea (120,000 VND) & Premium Tea (200,000 VND)
- **Water Activities**: Kayak, SUP, water bikes, and more (150,000 VND)
- **Sightseeing**: Valley tours (100,000 VND) - Currently inactive
- **Go-Kart Racing**: Electric go-kart track (150,000 VND) - Currently inactive
- **Kids Water Park**: Family-friendly water playground (50,000 VND) - Currently inactive
- **Paragliding**: Aerial adventure experience (2,250,000 VND) - Currently inactive

### System Features
- User registration and authentication
- Service browsing and detailed information
- Real-time availability checking
- Online booking system with deposit management
- User dashboard for booking management
- Admin panel for service and booking management
- Responsive design matching the original template

## 🛠️ Tech Stack

### Backend
- **Go 1.21+** - Programming language
- **Gin** - Web framework
- **PostgreSQL** - Database
- **JWT** - Authentication
- **bcrypt** - Password hashing

### Frontend
- **HTML5** - Semantic markup
- **CSS3** - Modern styling with responsive design
- **Vanilla JavaScript** - Interactive functionality
- **Modular Architecture** - Organized asset structure

### Architecture
- Clean separation of Frontend and Backend
- RESTful API design
- Database migrations
- Middleware for authentication and authorization
- Organized asset management and build process

## 📁 Project Structure

```
phongnha-valley/
├── frontend/              # Frontend application
│   ├── public/           # Built/served files
│   │   ├── index.html    # Main website
│   │   ├── admin/        # Admin panel
│   │   └── assets/       # Built static assets
│   ├── src/              # Source files
│   │   ├── assets/       # Source CSS, JS, images
│   │   ├── components/   # Reusable components (future)
│   │   └── pages/        # Page templates (future)
│   ├── package.json      # Frontend build configuration
│   ├── Makefile         # Frontend build commands
│   └── README.md        # Frontend documentation
├── backend/              # Backend application (future organization)
│   ├── cmd/             # Application entry points
│   ├── internal/        # Internal packages
│   ├── pkg/             # Public packages
│   ├── configs/         # Configuration files
│   └── migrations/      # Database migrations
├── config/               # Configuration files
├── internal/
│   ├── controllers/      # HTTP handlers
│   ├── middleware/       # HTTP middleware
│   ├── models/          # Data models
│   └── routes/          # Route definitions
├── migrations/          # Database migrations
├── pkg/
│   └── database/        # Database utilities
├── scripts/             # Utility scripts
├── web/                 # Legacy frontend files (deprecated)
├── .env.example        # Environment variables template
├── go.mod              # Go module file
├── main.go             # Application entry point
├── Makefile            # Build and development commands
└── README.md           # This file
```

## 🚀 Getting Started

### Prerequisites
- Go 1.21 or higher
- PostgreSQL 12 or higher
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd phongnha-valley
   ```

2. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your database credentials and other settings
   ```

3. **Install dependencies**
   ```bash
   make deps
   ```

4. **Set up the database**
   ```bash
   # Create PostgreSQL database
   createdb phongnha_valley
   
   # Run migrations
   make migrate-up
   ```

5. **Build frontend assets**
   ```bash
   cd frontend
   make build
   cd ..
   ```

6. **Run the application**
   ```bash
   make dev
   ```

The application will be available at `http://localhost:8080`
- Main website: `http://localhost:8080/`
- Admin panel: `http://localhost:8080/admin/`
- API: `http://localhost:8080/api/v1/`

### Quick Setup (All-in-one)
```bash
make setup
```

## 📚 API Documentation

### Authentication Endpoints

#### Register User
```http
POST /api/v1/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123",
  "first_name": "John",
  "last_name": "Doe",
  "phone": "0123456789"
}
```

#### Login
```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

### Service Endpoints

#### Get All Services
```http
GET /api/v1/services
GET /api/v1/services?type=ACCOMMODATION
GET /api/v1/services?active=true
```

#### Get Service Details
```http
GET /api/v1/services/{service_id}
```

### Booking Endpoints (Authenticated)

#### Create Booking
```http
POST /api/v1/bookings
Authorization: Bearer {jwt_token}
Content-Type: application/json

{
  "service_id": "uuid",
  "booking_date": "2024-01-15",
  "check_in_date": "2024-01-15",
  "check_out_date": "2024-01-16",
  "adults": 2,
  "children": 1,
  "special_notes": "Vegetarian meals please"
}
```

#### Get User Bookings
```http
GET /api/v1/bookings
Authorization: Bearer {jwt_token}
```

#### Check Availability
```http
GET /api/v1/availability?service_id={uuid}&date=2024-01-15
```

### Admin Endpoints (Admin Role Required)

#### Update Service
```http
PUT /api/v1/admin/services/{service_id}
Authorization: Bearer {admin_jwt_token}
```

#### Update Booking Status
```http
PUT /api/v1/admin/bookings/{booking_id}/status
Authorization: Bearer {admin_jwt_token}

{
  "status": "CONFIRMED",
  "payment_status": "COMPLETED"
}
```

## 🗄️ Database Schema

### Users Table
- User authentication and profile information
- Roles: `customer`, `admin`

### Services Table
- Service information (accommodation, activities, etc.)
- Pricing, capacity, and availability settings

### Service Details Table
- Extended service information
- Inclusions, requirements, age restrictions
- Booking policies

### Bookings Table
- Booking records with status tracking
- Payment status and pricing information
- Guest details and special requirements

## 🔧 Development

### Available Make Commands

#### Backend Commands
```bash
make build          # Build the application
make run            # Run the application
make dev            # Run in development mode
make test           # Run tests
make clean          # Clean build artifacts
make migrate-up     # Run database migrations
make migrate-down   # Rollback last migration
make create-migration # Create new migration files
make setup          # Complete project setup
```

#### Frontend Commands
```bash
cd frontend
make build          # Build frontend assets
make clean          # Clean built assets
make dev            # Build for development
make help           # Show available commands
```

### Frontend Development Workflow

1. **Make changes** to files in `frontend/src/assets/`
2. **Build assets** with `cd frontend && make build`
3. **Test changes** by running the Go backend with `make dev`
4. **Repeat** as needed

The Go backend automatically serves the built frontend files from `frontend/public/`.

### Environment Variables

```env
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=your_password
DB_NAME=phongnha_valley
DB_SSLMODE=disable

# Server Configuration
PORT=8080
GIN_MODE=debug

# JWT Configuration
JWT_SECRET=your_jwt_secret_key_here

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
```

### Default Admin User

After running migrations, a default admin user is created:
- **Email**: <EMAIL>
- **Password**: password (change this immediately!)

## 🏗️ Deployment

### Production Build
```bash
make prod
```

### Docker Deployment
```bash
make docker-build
make docker-run
```

## 📞 Contact Information

- **Phone**: ************
- **Email**: <EMAIL>
- **Instagram**: @phongnhavalley
- **Address**: Chày Lập Hamlet, Phúc Trạch Commune, Bố Trạch District, Quảng Bình Province
- **Hours**: 6:30 – 21:00

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Inspired by the Rovers Glamour Camping Adventure template
- Built for Phong Nha Valley tourism project
- Designed to connect visitors with nature through technology
