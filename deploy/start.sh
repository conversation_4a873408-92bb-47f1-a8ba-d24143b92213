#!/bin/sh

# Ensure directories exist and have correct permissions
mkdir -p /app/uploads /app/logs /app/frontend/public
chown -R appuser:appgroup /app/uploads /app/logs /app/frontend

# Run auto setup in background if script exists
if [ -f "/app/auto-setup.sh" ]; then
    echo "🚀 Starting auto setup in background..."
    chmod +x /app/auto-setup.sh
    nohup /app/auto-setup.sh > /app/logs/auto-setup.log 2>&1 &
    echo "✅ Auto setup started in background"
fi

# Start supervisor (single Go server, no nginx)
echo "🚀 Starting Go server via supervisor..."
exec /usr/bin/supervisord -c /etc/supervisor/conf.d/supervisord.conf
