# Example environment file for testing
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=password
DB_NAME=phongnha_test
DB_SSLMODE=disable

PORT=3000
GIN_MODE=release

CORS_ORIGINS=*

UPLOAD_DIR=/app/uploads
MAX_UPLOAD_SIZE=10485760
ALLOWED_EXTENSIONS=jpg,jpeg,png,gif,webp,svg

JWT_SECRET=test_jwt_secret_key_for_development_only
ADMIN_PASSWORD=admin123

LOG_LEVEL=info
LOG_FILE=/app/logs/app.log
