[supervisord]
nodaemon=true
user=root
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid
childlogdir=/var/log/supervisor
loglevel=info

[unix_http_server]
file=/var/run/supervisor.sock
chmod=0700

[supervisorctl]
serverurl=unix:///var/run/supervisor.sock

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[program:phongnha-api]
command=/usr/local/bin/phongnha-server
directory=/app
autostart=true
autorestart=true
startretries=3
environment=PORT=8080,GIN_MODE=release,UPLOAD_DIR=/app/uploads,LOG_FILE=/app/logs/app.log
stderr_logfile=/var/log/supervisor/api.err.log
stdout_logfile=/var/log/supervisor/api.out.log
user=appuser
