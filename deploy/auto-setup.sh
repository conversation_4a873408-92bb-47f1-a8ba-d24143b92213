#!/bin/bash

# Auto Setup Script for Container Startup (Background)
echo "🚀 Starting Auto Setup - Phong Nha Valley"

# Create log file
LOG_FILE="/app/logs/auto-setup.log"
mkdir -p /app/logs

# Function to log with timestamp
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> "$LOG_FILE"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Simple setup function (no blocking)
run_setup() {
    log "🚀 Auto Setup - Phong Nha Valley"
    log "================================"

    # Wait for services to be ready
    log "⏳ Waiting for services to start..."
    sleep 30

    # Check if database is reachable
    log "🔍 Checking database connection..."
    if nc -z ${DB_HOST:-phongnha-db.internal} ${DB_PORT:-5432} 2>/dev/null; then
        log "✅ Database is reachable"
    else
        log "❌ Database not reachable, will retry later"
        sleep 60
        if ! nc -z ${DB_HOST:-phongnha-db.internal} ${DB_PORT:-5432} 2>/dev/null; then
            log "❌ Database still not reachable, skipping setup"
            return 1
        fi
    fi

    # Try simple migration first
    log "📊 Attempting database setup..."

    # Simple approach: just try to create admin user
    log "👤 Creating admin user..."
    if command -v psql >/dev/null 2>&1 && [ -n "${DB_PASSWORD}" ]; then
        PGPASSWORD="${DB_PASSWORD}" psql -h "${DB_HOST:-phongnha-db.internal}" -p "${DB_PORT:-5432}" -U "${DB_USER:-postgres}" -d "${DB_NAME:-phongnha_valley}" << 'EOF' >> "$LOG_FILE" 2>&1
-- Just try to create extensions and basic admin user
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create users table if not exists
CREATE TABLE IF NOT EXISTS users (
    id TEXT PRIMARY KEY DEFAULT uuid_generate_v4()::text,
    email VARCHAR(255) UNIQUE NOT NULL,
    password TEXT NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    phone VARCHAR(20),
    role VARCHAR(20) DEFAULT 'user',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Create admin user
INSERT INTO users (id, email, password, first_name, last_name, phone, role, created_at, updated_at)
VALUES (
    uuid_generate_v4()::text,
    '<EMAIL>',
    crypt('password', gen_salt('bf')),
    'Admin',
    'User',
    '+84 123 456 789',
    'admin',
    NOW(),
    NOW()
)
ON CONFLICT (email) DO UPDATE SET
    password = crypt('password', gen_salt('bf')),
    role = 'admin',
    updated_at = NOW();
EOF
        if [ $? -eq 0 ]; then
            log "✅ Basic setup completed"
        else
            log "❌ Basic setup failed, but continuing..."
        fi
    else
        log "❌ No database password or psql not available, skipping setup"
    fi

    log "✅ Auto setup completed"

    # Create success marker
    touch /app/logs/setup-completed
}

# Run setup in background
log "🚀 Starting background setup..."
run_setup &

# Don't wait for setup to complete - let container start
log "✅ Background setup started, container continuing..."
