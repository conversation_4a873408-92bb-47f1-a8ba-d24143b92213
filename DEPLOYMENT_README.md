# 🚀 Phong Nha Valley - Deployment Documentation

## 📚 Tài liệu hướng dẫn

Dự án này bao gồm các tài liệu hướng dẫn deployment chi tiết:

### 1. **DOCKER_VOLUMES_GUIDE.md** 
📖 **G<PERSON><PERSON>i pháp Persistent Storage**
- Giải quyết vấn đề mất data khi rebuild container
- Hướng dẫn sử dụng Docker Volumes
- Script quản lý `manage.sh`

### 2. **CLOUD_DEPLOYMENT_GUIDE.md**
☁️ **Hướng dẫn Deploy lên Cloud**
- Các cloud platform được hỗ trợ
- So sánh chi phí và tính năng
- Hướng dẫn từng bước cho từng scenario

### 3. **deploy.sh**
🤖 **Script tự động deployment**
- Tự động hóa toàn bộ quá trình deploy
- Hỗ trợ cả fresh install và migration
- Setup SSL và monitoring tự động

---

## 🎯 Quick Start

### Scenario 1: Deploy lần đầu (Chưa có data)

```bash
# 1. Clone project
git clone https://github.com/your-username/phongnha_2.git
cd phongnha_2

# 2. Deploy tự động
./deploy.sh fresh-install

# 3. Truy cập ứng dụng
# Homepage: http://your-server-ip:8080
# Admin: http://your-server-ip:8080/admin
# Login: <EMAIL> / password
```

### Scenario 2: Migrate từ server có data

```bash
# 1. Backup từ server cũ
./manage.sh backup

# 2. Copy backup files lên server mới
scp backup_database.sql user@new-server:/home/<USER>/phongnha_2/migration_backup/
scp -r uploads_backup user@new-server:/home/<USER>/phongnha_2/migration_backup/

# 3. Deploy với migration
./deploy.sh migrate --backup-dir ./migration_backup
```

### Scenario 3: Setup Production với SSL

```bash
# 1. Deploy cơ bản
./deploy.sh fresh-install

# 2. Setup SSL
./deploy.sh setup-ssl --domain your-domain.com --email <EMAIL>

# 3. Setup monitoring
./deploy.sh setup-monitoring
```

---

## 🛠️ Scripts và Tools

### `manage.sh` - Quản lý local development
```bash
./manage.sh start      # Khởi động services
./manage.sh rebuild    # Rebuild với preserved data
./manage.sh backup     # Backup database + uploads
./manage.sh status     # Kiểm tra trạng thái
./manage.sh logs       # Xem logs
```

### `deploy.sh` - Cloud deployment automation
```bash
./deploy.sh fresh-install              # Deploy lần đầu
./deploy.sh migrate                    # Deploy với data migration
./deploy.sh setup-ssl --domain=...    # Setup SSL certificate
./deploy.sh setup-monitoring          # Setup auto-backup & health check
./deploy.sh update                     # Update project
```

---

## 🌐 Cloud Platforms

| Platform | Chi phí/tháng | Ưu điểm | Phù hợp cho |
|----------|---------------|---------|-------------|
| **DigitalOcean** | $5-20 | Đơn giản, Docker-friendly | Startup, SME |
| **AWS EC2** | $10-50 | Nhiều tính năng, scalable | Enterprise |
| **Google Cloud** | $0-30 | Free tier tốt | Budget thấp |
| **Vultr** | $3-15 | Giá rẻ, performance tốt | Budget-conscious |
| **Linode** | $5-20 | Developer-friendly | Developers |

---

## 📋 System Requirements

### Minimum (Testing)
- **CPU**: 1 vCPU
- **RAM**: 1GB
- **Storage**: 20GB SSD
- **OS**: Ubuntu 20.04/22.04 LTS

### Recommended (Production)
- **CPU**: 2 vCPU
- **RAM**: 2GB
- **Storage**: 50GB SSD
- **Bandwidth**: 1TB/month

---

## 🔒 Security Features

### Tự động setup
- ✅ Firewall configuration
- ✅ SSL certificate (Let's Encrypt)
- ✅ Nginx reverse proxy
- ✅ Environment variables protection

### Best practices
- ✅ Non-root user deployment
- ✅ Docker security
- ✅ Regular backups
- ✅ Health monitoring

---

## 📊 Monitoring & Maintenance

### Tự động monitoring
- **Health check**: Mỗi 5 phút
- **Auto restart**: Khi service down
- **Daily backup**: 2:00 AM
- **Log rotation**: Tự động cleanup

### Manual commands
```bash
# Check logs
sudo tail -f /var/log/phongnha_health.log
sudo tail -f /var/log/phongnha_backup.log

# Manual backup
./manage.sh backup

# Update project
./deploy.sh update
```

---

## 🆘 Troubleshooting

### Common Issues

**Port already in use**
```bash
sudo lsof -i :8080
sudo kill -9 PID
```

**Docker permission denied**
```bash
sudo usermod -aG docker $USER
# Logout and login again
```

**Service not starting**
```bash
./manage.sh logs
./manage.sh status
```

**SSL certificate issues**
```bash
sudo certbot renew --dry-run
sudo nginx -t
sudo systemctl restart nginx
```

---

## 📞 Support

### Health Check URLs
- **API Health**: `http://your-domain.com/api/v1/health`
- **Homepage**: `http://your-domain.com`
- **Admin Panel**: `http://your-domain.com/admin`

### Default Credentials
- **Email**: `<EMAIL>`
- **Password**: `password`
- ⚠️ **Đổi password ngay sau khi deploy!**

### Log Files
- **Application**: `./manage.sh logs`
- **Health Check**: `/var/log/phongnha_health.log`
- **Backup**: `/var/log/phongnha_backup.log`
- **Nginx**: `/var/log/nginx/access.log`, `/var/log/nginx/error.log`

---

## 🔄 Update Workflow

### Regular Updates
```bash
# Pull latest code
git pull origin main

# Update with preserved data
./deploy.sh update

# Verify
curl http://localhost:8080/api/v1/health
```

### Emergency Rollback
```bash
# Rollback to previous commit
git checkout previous_working_commit
./deploy.sh update
```

---

## 💡 Tips & Best Practices

1. **Always backup before updates**
   ```bash
   ./manage.sh backup
   ```

2. **Test on staging first**
   - Deploy on test server
   - Verify functionality
   - Then deploy to production

3. **Monitor regularly**
   ```bash
   ./manage.sh status
   tail -f /var/log/phongnha_health.log
   ```

4. **Keep system updated**
   ```bash
   sudo apt update && sudo apt upgrade -y
   ```

5. **Use strong passwords**
   - Change default admin password
   - Use environment variables for secrets

---

## 📈 Scaling Considerations

### Vertical Scaling (Upgrade server)
- Increase CPU/RAM when needed
- Monitor resource usage
- Backup before scaling

### Horizontal Scaling (Multiple servers)
- Use external database (RDS, Cloud SQL)
- Shared file storage for uploads
- Load balancer setup

### Performance Optimization
- Enable Nginx caching
- Optimize images
- Use CDN for static assets
- Database indexing

---

**🎉 Happy Deploying!**

Với các scripts và hướng dẫn này, bạn có thể deploy Phong Nha Valley project lên bất kỳ cloud platform nào một cách dễ dàng và an toàn!
