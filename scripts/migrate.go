package main

import (
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"path/filepath"
	"sort"
	"strings"

	"github.com/jmoiron/sqlx"
	"github.com/joho/godotenv"
	_ "github.com/lib/pq"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Printf("Warning: .env file not found")
	}

	// Get command line argument
	if len(os.Args) < 2 {
		log.Fatal("Usage: go run migrate.go [up|down]")
	}
	command := os.Args[1]

	// Connect to database
	db, err := connectDB()
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	// Create migrations table if not exists
	if err := createMigrationsTable(db); err != nil {
		log.Fatalf("Failed to create migrations table: %v", err)
	}

	switch command {
	case "up":
		if err := migrateUp(db); err != nil {
			log.Fatalf("Migration up failed: %v", err)
		}
		fmt.Println("Migration up completed successfully")
	case "down":
		if err := migrateDown(db); err != nil {
			log.Fatalf("Migration down failed: %v", err)
		}
		fmt.Println("Migration down completed successfully")
	default:
		log.Fatal("Invalid command. Use 'up' or 'down'")
	}
}

func connectDB() (*sqlx.DB, error) {
	dbHost := os.Getenv("DB_HOST")
	dbPort := os.Getenv("DB_PORT")
	dbUser := os.Getenv("DB_USER")
	dbPassword := os.Getenv("DB_PASSWORD")
	dbName := os.Getenv("DB_NAME")
	sslMode := os.Getenv("DB_SSLMODE")

	connStr := fmt.Sprintf(
		"host=%s port=%s user=%s password=%s dbname=%s sslmode=%s",
		dbHost, dbPort, dbUser, dbPassword, dbName, sslMode,
	)

	return sqlx.Connect("postgres", connStr)
}

func createMigrationsTable(db *sqlx.DB) error {
	query := `
		CREATE TABLE IF NOT EXISTS schema_migrations (
			version VARCHAR(255) PRIMARY KEY,
			applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
		)
	`
	_, err := db.Exec(query)
	return err
}

func migrateUp(db *sqlx.DB) error {
	// Get applied migrations
	applied, err := getAppliedMigrations(db)
	if err != nil {
		return err
	}

	// Get migration files
	files, err := getMigrationFiles("up")
	if err != nil {
		return err
	}

	// Apply pending migrations
	for _, file := range files {
		version := extractVersion(file)
		if _, exists := applied[version]; exists {
			fmt.Printf("Migration %s already applied, skipping\n", version)
			continue
		}

		fmt.Printf("Applying migration %s\n", version)
		if err := applyMigration(db, file, version); err != nil {
			return fmt.Errorf("failed to apply migration %s: %v", version, err)
		}
	}

	return nil
}

func migrateDown(db *sqlx.DB) error {
	// Get applied migrations
	applied, err := getAppliedMigrations(db)
	if err != nil {
		return err
	}

	if len(applied) == 0 {
		fmt.Println("No migrations to rollback")
		return nil
	}

	// Get the latest applied migration
	var latest string
	for version := range applied {
		if version > latest {
			latest = version
		}
	}

	// Find corresponding down file
	downFile := fmt.Sprintf("backend/migrations/%s.down.sql", latest)
	if _, err := os.Stat(downFile); os.IsNotExist(err) {
		return fmt.Errorf("down migration file not found: %s", downFile)
	}

	fmt.Printf("Rolling back migration %s\n", latest)
	return rollbackMigration(db, downFile, latest)
}

func getAppliedMigrations(db *sqlx.DB) (map[string]bool, error) {
	applied := make(map[string]bool)
	rows, err := db.Query("SELECT version FROM schema_migrations")
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	for rows.Next() {
		var version string
		if err := rows.Scan(&version); err != nil {
			return nil, err
		}
		applied[version] = true
	}

	return applied, nil
}

func getMigrationFiles(direction string) ([]string, error) {
	pattern := fmt.Sprintf("backend/migrations/*.%s.sql", direction)
	files, err := filepath.Glob(pattern)
	if err != nil {
		return nil, err
	}
	sort.Strings(files)
	return files, nil
}

func extractVersion(filename string) string {
	base := filepath.Base(filename)
	return strings.TrimSuffix(base, ".up.sql")
}

func applyMigration(db *sqlx.DB, filename, version string) error {
	content, err := ioutil.ReadFile(filename)
	if err != nil {
		return err
	}

	// Execute migration
	if _, err := db.Exec(string(content)); err != nil {
		return err
	}

	// Record migration
	_, err = db.Exec("INSERT INTO schema_migrations (version) VALUES ($1)", version)
	return err
}

func rollbackMigration(db *sqlx.DB, filename, version string) error {
	content, err := ioutil.ReadFile(filename)
	if err != nil {
		return err
	}

	// Execute rollback
	if _, err := db.Exec(string(content)); err != nil {
		return err
	}

	// Remove migration record
	_, err = db.Exec("DELETE FROM schema_migrations WHERE version = $1", version)
	return err
}
