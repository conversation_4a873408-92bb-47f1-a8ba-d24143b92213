#!/bin/bash

# 🖼️ Image Optimization Script for Phong Nha Valley
# This script optimizes images in the uploads directory

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
UPLOAD_DIR="/app/frontend/public/assets/uploads"
BACKUP_DIR="/app/frontend/public/assets/uploads_backup"
MAX_SIZE_KB=300
QUALITY=80

echo -e "${BLUE}🖼️ Starting Image Optimization...${NC}"

# Check if running inside Docker container
if [ ! -d "$UPLOAD_DIR" ]; then
    echo -e "${RED}❌ Upload directory not found: $UPLOAD_DIR${NC}"
    echo -e "${YELLOW}💡 Run this script inside the Docker container:${NC}"
    echo -e "${YELLOW}   docker exec phongnha_app /app/scripts/optimize_images.sh${NC}"
    exit 1
fi

# Install required tools if not present
echo -e "${BLUE}📦 Checking required tools...${NC}"
if ! command -v convert &> /dev/null; then
    echo -e "${YELLOW}⚙️ Installing ImageMagick...${NC}"
    apk add --no-cache imagemagick
fi

if ! command -v cwebp &> /dev/null; then
    echo -e "${YELLOW}⚙️ Installing WebP tools...${NC}"
    apk add --no-cache libwebp-tools
fi

# Create backup directory
echo -e "${BLUE}💾 Creating backup...${NC}"
mkdir -p "$BACKUP_DIR"

# Function to get file size in KB
get_file_size_kb() {
    local file="$1"
    local size_bytes
    size_bytes=$(stat -c%s "$file" 2>/dev/null || echo "0")
    echo $((size_bytes / 1024))
}

# Function to optimize single image
optimize_image() {
    local input_file="$1"
    local filename=$(basename "$input_file")
    local name_without_ext="${filename%.*}"
    local ext="${filename##*.}"
    
    # Skip if already optimized or not an image
    case "$ext" in
        jpg|jpeg|png|JPG|JPEG|PNG)
            ;;
        *)
            return 0
            ;;
    esac
    
    local original_size=$(get_file_size_kb "$input_file")
    
    # Skip if already small enough
    if [ "$original_size" -le "$MAX_SIZE_KB" ]; then
        echo -e "${GREEN}✅ $filename already optimized (${original_size}KB)${NC}"
        return 0
    fi
    
    echo -e "${YELLOW}🔧 Optimizing $filename (${original_size}KB)...${NC}"
    
    # Backup original
    cp "$input_file" "$BACKUP_DIR/"
    
    # Determine appropriate dimensions based on current size
    local target_width=800
    local target_height=600
    
    if [ "$original_size" -gt 10000 ]; then  # > 10MB
        target_width=1200
        target_height=800
    elif [ "$original_size" -gt 5000 ]; then  # > 5MB
        target_width=1000
        target_height=700
    fi
    
    # Create temporary file
    local temp_file="${input_file}.tmp"
    
    # Optimize with ImageMagick
    convert "$input_file" \
        -resize "${target_width}x${target_height}>" \
        -quality $QUALITY \
        -strip \
        -interlace Plane \
        "$temp_file"
    
    # Check if optimization was successful
    if [ -f "$temp_file" ]; then
        local new_size=$(get_file_size_kb "$temp_file")
        
        # If still too large, reduce quality further
        if [ "$new_size" -gt "$MAX_SIZE_KB" ]; then
            local reduced_quality=$((QUALITY - 20))
            echo -e "${YELLOW}📉 Still too large, reducing quality to ${reduced_quality}%...${NC}"
            
            convert "$input_file" \
                -resize "${target_width}x${target_height}>" \
                -quality $reduced_quality \
                -strip \
                -interlace Plane \
                "$temp_file"
            
            new_size=$(get_file_size_kb "$temp_file")
        fi
        
        # Replace original with optimized version
        mv "$temp_file" "$input_file"
        
        local reduction=$((100 - (new_size * 100 / original_size)))
        echo -e "${GREEN}✅ $filename: ${original_size}KB → ${new_size}KB (${reduction}% reduction)${NC}"
        
        # Create WebP version for modern browsers
        local webp_file="${input_file%.*}.webp"
        if cwebp -q $QUALITY "$input_file" -o "$webp_file" 2>/dev/null; then
            local webp_size=$(get_file_size_kb "$webp_file")
            echo -e "${GREEN}🌐 Created WebP: ${webp_size}KB${NC}"
        fi
    else
        echo -e "${RED}❌ Failed to optimize $filename${NC}"
    fi
}

# Main optimization loop
echo -e "${BLUE}🔍 Scanning for images to optimize...${NC}"

total_original=0
total_optimized=0
count=0

for ext in jpg jpeg png JPG JPEG PNG; do
    for file in "$UPLOAD_DIR"/*."$ext"; do
        if [ -f "$file" ]; then
            original_size=$(get_file_size_kb "$file")
            total_original=$((total_original + original_size))

            optimize_image "$file"

            optimized_size=$(get_file_size_kb "$file")
            total_optimized=$((total_optimized + optimized_size))
            count=$((count + 1))
        fi
    done
done

# Summary
echo -e "\n${BLUE}📊 Optimization Summary:${NC}"
echo -e "${GREEN}📁 Files processed: $count${NC}"
echo -e "${GREEN}📉 Total size before: ${total_original}KB ($((total_original/1024))MB)${NC}"
echo -e "${GREEN}📈 Total size after: ${total_optimized}KB ($((total_optimized/1024))MB)${NC}"

if [ "$total_original" -gt 0 ]; then
    local total_reduction=$((100 - (total_optimized * 100 / total_original)))
    echo -e "${GREEN}🎯 Total reduction: ${total_reduction}%${NC}"
fi

echo -e "${GREEN}💾 Backups saved to: $BACKUP_DIR${NC}"
echo -e "${GREEN}✅ Image optimization completed!${NC}"

# Clean up empty backup directory
if [ -z "$(ls -A "$BACKUP_DIR" 2>/dev/null)" ]; then
    rmdir "$BACKUP_DIR"
fi
