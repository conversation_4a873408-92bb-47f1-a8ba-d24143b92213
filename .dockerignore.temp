# Fast build dockerignore - minimal context for speed

# Version control
.git
.gitignore
.gitattributes

# Documentation
*.md
README*
docs/
documentation/

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Logs
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
.nyc_output/

# Dependency directories
node_modules/
frontend/node_modules/
**/node_modules/

# Build outputs (will be generated in container)
frontend/public/assets/js/*.js
frontend/public/assets/js/*.map
frontend/public/assets/css/*.css
frontend/dist/
frontend/build/
build/
dist/

# Environment files
.env
.env.local
.env.development
.env.test
.env.production
.env.*

# Docker files (except the ones we need)
Dockerfile
docker-compose.yml
docker-compose.*.yml
!docker-compose.fast.yml
!Dockerfile.fast

# Scripts (except the ones we need)
manage.sh
build-optimized.sh
!manage-fast.sh

# Temporary files
tmp/
temp/
*.tmp
*.temp

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Go specific
*.exe
*.exe~
*.dll
*.so
*.dylib
*.test
*.out
go.work
go.work.sum

# Test files
*_test.go
test/
tests/
__tests__/

# Backup files
*.bak
*.backup
backup_*

# Cache directories
.cache/
.npm/
.yarn/
.pnpm-store/

# Editor directories and files
.vscode/
!.vscode/extensions.json
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
