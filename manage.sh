#!/bin/bash

# Phong Nha Valley Project Management Script
# Sử dụng Docker Volumes để persistent storage

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Project configuration
PROJECT_NAME="phongnha_2"
APP_IMAGE="${PROJECT_NAME}-app"
DB_CONTAINER="phongnha_db"
APP_CONTAINER="phongnha_app"
NETWORK_NAME="phongnha_network"
DB_VOLUME="phongnha_db_data"
UPLOADS_VOLUME="phongnha_uploads"

print_usage() {
    echo -e "${BLUE}Phong Nha Valley Project Management${NC}"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  setup     - Tạo volumes và network lần đầu"
    echo "  start     - Start tất cả services"
    echo "  stop      - Stop tất cả services"
    echo "  restart   - Restart tất cả services"
    echo "  rebuild   - Rebuild và restart app (giữ nguyên data)"
    echo "  logs      - Xem logs của app"
    echo "  backup    - Backup database và uploads"
    echo "  restore   - Restore database và uploads từ backup"
    echo "  clean     - Dọn dẹp containers (KHÔNG xóa volumes)"
    echo "  status    - Kiểm tra trạng thái services"
    echo "  shell     - Vào shell của app container"
    echo "  help      - Hiển thị help này"
}

setup() {
    echo -e "${YELLOW}Setting up project...${NC}"
    
    # Create network if not exists
    if ! docker network ls | grep -q $NETWORK_NAME; then
        echo "Creating network: $NETWORK_NAME"
        docker network create $NETWORK_NAME
    fi
    
    # Create volumes if not exist
    if ! docker volume ls | grep -q $DB_VOLUME; then
        echo "Creating database volume: $DB_VOLUME"
        docker volume create $DB_VOLUME
    fi
    
    if ! docker volume ls | grep -q $UPLOADS_VOLUME; then
        echo "Creating uploads volume: $UPLOADS_VOLUME"
        docker volume create $UPLOADS_VOLUME
    fi
    
    echo -e "${GREEN}Setup completed!${NC}"
}

start() {
    echo -e "${YELLOW}Starting services...${NC}"
    
    # Start database
    if ! docker ps | grep -q $DB_CONTAINER; then
        echo "Starting database..."
        docker run -d --name $DB_CONTAINER \
            --network $NETWORK_NAME \
            -e POSTGRES_DB=phongnha_valley \
            -e POSTGRES_USER=postgres \
            -e POSTGRES_PASSWORD=password \
            -p 5432:5432 \
            -v $DB_VOLUME:/var/lib/postgresql/data \
            postgres:15
        
        echo "Waiting for database to be ready..."
        sleep 10
    fi
    
    # Start app
    if ! docker ps | grep -q $APP_CONTAINER; then
        echo "Starting application..."
        docker run -d --name $APP_CONTAINER \
            --network $NETWORK_NAME \
            -e DB_HOST=$DB_CONTAINER \
            -e DB_PORT=5432 \
            -e DB_USER=postgres \
            -e DB_PASSWORD=password \
            -e DB_NAME=phongnha_valley \
            -e DB_SSLMODE=disable \
            -e GIN_MODE=release \
            -e JWT_SECRET=phongnha_valley_production_jwt_secret_key_2024_secure_random_string_for_production_use_only \
            -p 8080:8080 \
            -v $UPLOADS_VOLUME:/app/frontend/public/assets/uploads \
            $APP_IMAGE
    fi
    
    echo -e "${GREEN}Services started!${NC}"
    echo "Application: http://localhost:8080"
    echo "Admin Panel: http://localhost:8080/admin"
}

stop() {
    echo -e "${YELLOW}Stopping services...${NC}"
    docker stop $APP_CONTAINER $DB_CONTAINER 2>/dev/null || true
    echo -e "${GREEN}Services stopped!${NC}"
}

restart() {
    stop
    sleep 2
    start
}

rebuild() {
    echo -e "${YELLOW}Rebuilding application...${NC}"
    
    # Stop and remove app container
    docker stop $APP_CONTAINER 2>/dev/null || true
    docker rm $APP_CONTAINER 2>/dev/null || true
    
    # Build frontend
    echo "Building frontend..."
    cd frontend && make build && cd ..
    
    # Build new image
    echo "Building Docker image..."
    docker build -t $APP_IMAGE .
    
    # Start app with preserved volumes
    echo "Starting application with preserved data..."
    docker run -d --name $APP_CONTAINER \
        --network $NETWORK_NAME \
        -e DB_HOST=$DB_CONTAINER \
        -e DB_PORT=5432 \
        -e DB_USER=postgres \
        -e DB_PASSWORD=password \
        -e DB_NAME=phongnha_valley \
        -e DB_SSLMODE=disable \
        -e GIN_MODE=release \
        -e JWT_SECRET=phongnha_valley_production_jwt_secret_key_2024_secure_random_string_for_production_use_only \
        -p 8080:8080 \
        -v $UPLOADS_VOLUME:/app/frontend/public/assets/uploads \
        $APP_IMAGE
    
    echo -e "${GREEN}Rebuild completed! Data preserved.${NC}"
}

backup() {
    echo -e "${YELLOW}Creating backup...${NC}"
    
    BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p $BACKUP_DIR
    
    # Backup database
    echo "Backing up database..."
    docker exec $DB_CONTAINER pg_dump -U postgres phongnha_valley > $BACKUP_DIR/database.sql
    
    # Backup uploads
    echo "Backing up uploads..."
    docker run --rm -v $UPLOADS_VOLUME:/uploads -v $(pwd)/$BACKUP_DIR:/backup alpine cp -r /uploads /backup/
    
    echo -e "${GREEN}Backup created: $BACKUP_DIR${NC}"
}

status() {
    echo -e "${BLUE}Service Status:${NC}"
    echo ""
    
    if docker ps | grep -q $DB_CONTAINER; then
        echo -e "Database: ${GREEN}Running${NC}"
    else
        echo -e "Database: ${RED}Stopped${NC}"
    fi
    
    if docker ps | grep -q $APP_CONTAINER; then
        echo -e "Application: ${GREEN}Running${NC}"
        echo "URL: http://localhost:8080"
    else
        echo -e "Application: ${RED}Stopped${NC}"
    fi
    
    echo ""
    echo "Volumes:"
    docker volume ls | grep phongnha || echo "No volumes found"
}

logs() {
    docker logs -f $APP_CONTAINER
}

shell() {
    docker exec -it $APP_CONTAINER /bin/sh
}

clean() {
    echo -e "${YELLOW}Cleaning up containers...${NC}"
    docker stop $APP_CONTAINER $DB_CONTAINER 2>/dev/null || true
    docker rm $APP_CONTAINER $DB_CONTAINER 2>/dev/null || true
    echo -e "${GREEN}Containers cleaned! (Volumes preserved)${NC}"
}

# Main script logic
case "${1:-help}" in
    setup)
        setup
        ;;
    start)
        setup
        start
        ;;
    stop)
        stop
        ;;
    restart)
        restart
        ;;
    rebuild)
        rebuild
        ;;
    backup)
        backup
        ;;
    logs)
        logs
        ;;
    status)
        status
        ;;
    shell)
        shell
        ;;
    clean)
        clean
        ;;
    help|*)
        print_usage
        ;;
esac
