package main

import (
	"fmt"
	"log"
	"os"
	"phongnha-valley/backend/pkg/database"
)

func seedDatabase() {
	// Initialize database connection
	db, err := database.InitDB()
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	fmt.Println("🌱 Seeding database with initial data...")

	// Seed services
	seedServices(db)

	// Seed content
	seedContent(db)

	// Seed settings
	seedSettings(db)

	// Seed admin user
	seedAdminUser(db)

	fmt.Println("✅ Database seeding completed successfully")
}

func seedServices(db *database.DB) {
	fmt.Println("📋 Seeding services...")

	services := []map[string]interface{}{
		{
			"name":        "Glamping Experience",
			"description": "Luxury camping experience in the heart of Phong Nha National Park",
			"price":       1500000,
			"duration":    "2 days 1 night",
			"max_guests":  4,
			"is_active":   true,
		},
		{
			"name":        "Cave Adventure Tour",
			"description": "Explore the magnificent caves of Phong Nha-Ke Bang",
			"price":       800000,
			"duration":    "1 day",
			"max_guests":  8,
			"is_active":   true,
		},
		{
			"name":        "Jungle Trekking",
			"description": "Discover the pristine jungle and wildlife",
			"price":       600000,
			"duration":    "Half day",
			"max_guests":  6,
			"is_active":   true,
		},
	}

	for _, service := range services {
		query := `
		INSERT INTO services (id, name, description, price, duration, max_guests, is_active, created_at, updated_at)
		VALUES (gen_random_uuid()::text, $1, $2, $3, $4, $5, $6, NOW(), NOW())
		ON CONFLICT (name) DO UPDATE SET
			description = $2,
			price = $3,
			duration = $4,
			max_guests = $5,
			is_active = $6,
			updated_at = NOW()
		`
		_, err := db.Exec(query, service["name"], service["description"], service["price"], 
			service["duration"], service["max_guests"], service["is_active"])
		if err != nil {
			log.Printf("Failed to seed service %s: %v", service["name"], err)
		}
	}
}

func seedContent(db *database.DB) {
	fmt.Println("📝 Seeding content...")

	contents := []map[string]interface{}{
		{
			"key":   "hero_title",
			"value": "Welcome to Phong Nha Valley",
			"type":  "text",
		},
		{
			"key":   "hero_subtitle",
			"value": "Experience luxury glamping in Vietnam's most beautiful national park",
			"type":  "text",
		},
		{
			"key":   "about_title",
			"value": "About Phong Nha Valley",
			"type":  "text",
		},
		{
			"key":   "about_description",
			"value": "Discover the perfect blend of adventure and comfort in the heart of Phong Nha-Ke Bang National Park.",
			"type":  "text",
		},
	}

	for _, content := range contents {
		query := `
		INSERT INTO content_blocks (id, key, value, type, created_at, updated_at)
		VALUES (gen_random_uuid()::text, $1, $2, $3, NOW(), NOW())
		ON CONFLICT (key) DO UPDATE SET
			value = $2,
			type = $3,
			updated_at = NOW()
		`
		_, err := db.Exec(query, content["key"], content["value"], content["type"])
		if err != nil {
			log.Printf("Failed to seed content %s: %v", content["key"], err)
		}
	}
}

func seedSettings(db *database.DB) {
	fmt.Println("⚙️ Seeding settings...")

	settings := []map[string]interface{}{
		{
			"key":   "site_name",
			"value": "Phong Nha Valley",
			"type":  "text",
		},
		{
			"key":   "contact_email",
			"value": "<EMAIL>",
			"type":  "email",
		},
		{
			"key":   "contact_phone",
			"value": "+84 123 456 789",
			"type":  "phone",
		},
		{
			"key":   "address",
			"value": "Phong Nha-Ke Bang National Park, Quang Binh, Vietnam",
			"type":  "text",
		},
	}

	for _, setting := range settings {
		query := `
		INSERT INTO settings (id, key, value, type, created_at, updated_at)
		VALUES (gen_random_uuid()::text, $1, $2, $3, NOW(), NOW())
		ON CONFLICT (key) DO UPDATE SET
			value = $2,
			type = $3,
			updated_at = NOW()
		`
		_, err := db.Exec(query, setting["key"], setting["value"], setting["type"])
		if err != nil {
			log.Printf("Failed to seed setting %s: %v", setting["key"], err)
		}
	}
}

func seedAdminUser(db *database.DB) {
	fmt.Println("👤 Seeding admin user...")

	query := `
	INSERT INTO users (id, email, password, first_name, last_name, phone, role, created_at, updated_at)
	VALUES (
		gen_random_uuid()::text,
		'<EMAIL>',
		crypt('admin123', gen_salt('bf')),
		'Admin',
		'User',
		'+84 123 456 789',
		'admin',
		NOW(),
		NOW()
	)
	ON CONFLICT (email) DO UPDATE SET
		password = crypt('admin123', gen_salt('bf')),
		first_name = 'Admin',
		last_name = 'User',
		role = 'admin',
		updated_at = NOW()
	`

	_, err := db.Exec(query)
	if err != nil {
		log.Printf("Failed to seed admin user: %v", err)
	}
}

func main() {
	if len(os.Args) > 1 && os.Args[1] == "seed" {
		seedDatabase()
		return
	}

	fmt.Println("Use 'seed' command to seed database")
}
