package utils

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"github.com/jmoiron/sqlx"
)

// ImageCleanupService handles orphaned image cleanup
type ImageCleanupService struct {
	DB *sqlx.DB
}

// NewImageCleanupService creates a new image cleanup service
func NewImageCleanupService(db *sqlx.DB) *ImageCleanupService {
	return &ImageCleanupService{DB: db}
}

// CleanupOrphanedImages removes image files that are not referenced in database
func (ics *ImageCleanupService) CleanupOrphanedImages() error {
	fmt.Println("🧹 Starting orphaned images cleanup...")

	// Get working directory
	workingDir, err := os.Getwd()
	if err != nil {
		return fmt.Errorf("failed to get working directory: %v", err)
	}

	uploadDir := filepath.Join(workingDir, "frontend", "public", "assets", "uploads")

	// Get all image files from uploads directory
	files, err := os.ReadDir(uploadDir)
	if err != nil {
		return fmt.Errorf("failed to read uploads directory: %v", err)
	}

	// Get all referenced images from database
	referencedImages, err := ics.getAllReferencedImages()
	if err != nil {
		return fmt.Errorf("failed to get referenced images: %v", err)
	}

	// Create map for faster lookup
	referencedMap := make(map[string]bool)
	for _, img := range referencedImages {
		// Handle both filename and path formats
		filename := filepath.Base(img)
		referencedMap[filename] = true
	}

	// Check each file in uploads directory
	deletedCount := 0
	for _, file := range files {
		if file.IsDir() {
			continue
		}

		filename := file.Name()
		
		// Skip system files
		if strings.HasPrefix(filename, ".") {
			continue
		}

		// Check if file is referenced in database
		if !referencedMap[filename] {
			filePath := filepath.Join(uploadDir, filename)
			if err := os.Remove(filePath); err != nil {
				fmt.Printf("❌ Failed to delete orphaned image %s: %v\n", filename, err)
			} else {
				fmt.Printf("✅ Deleted orphaned image: %s\n", filename)
				deletedCount++
			}
		}
	}

	fmt.Printf("🧹 Cleanup completed. Deleted %d orphaned images.\n", deletedCount)
	return nil
}

// getAllReferencedImages gets all image filenames referenced in database
func (ics *ImageCleanupService) getAllReferencedImages() ([]string, error) {
	var images []string

	// Get images from services table
	serviceImages, err := ics.getServiceImages()
	if err != nil {
		return nil, fmt.Errorf("failed to get service images: %v", err)
	}
	images = append(images, serviceImages...)

	// Get images from gallery table
	galleryImages, err := ics.getGalleryImages()
	if err != nil {
		return nil, fmt.Errorf("failed to get gallery images: %v", err)
	}
	images = append(images, galleryImages...)

	// Get images from homepage content (hero, about, etc.)
	homepageImages, err := ics.getHomepageImages()
	if err != nil {
		return nil, fmt.Errorf("failed to get homepage images: %v", err)
	}
	images = append(images, homepageImages...)

	return images, nil
}

// getServiceImages gets all images from services table
func (ics *ImageCleanupService) getServiceImages() ([]string, error) {
	var images []string
	
	query := `SELECT UNNEST(images) as image FROM services WHERE images IS NOT NULL`
	rows, err := ics.DB.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	for rows.Next() {
		var image string
		if err := rows.Scan(&image); err != nil {
			continue
		}
		if image != "" {
			images = append(images, image)
		}
	}

	return images, nil
}

// getGalleryImages gets all images from gallery table
func (ics *ImageCleanupService) getGalleryImages() ([]string, error) {
	var images []string
	
	query := `SELECT image_url FROM gallery WHERE image_url IS NOT NULL AND image_url != ''`
	rows, err := ics.DB.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	for rows.Next() {
		var imageURL string
		if err := rows.Scan(&imageURL); err != nil {
			continue
		}
		if imageURL != "" {
			// Extract filename from URL
			filename := filepath.Base(imageURL)
			if strings.HasPrefix(imageURL, "/assets/uploads/") {
				filename = strings.TrimPrefix(imageURL, "/assets/uploads/")
			}
			images = append(images, filename)
		}
	}

	return images, nil
}

// getHomepageImages gets all images from homepage content
func (ics *ImageCleanupService) getHomepageImages() ([]string, error) {
	var images []string
	
	// This would need to be implemented based on your homepage content structure
	// For now, return empty slice
	// TODO: Implement based on homepage_content table structure
	
	return images, nil
}

// DeleteImageFile deletes a single image file
func DeleteImageFile(filename string) error {
	if filename == "" || strings.Contains(filename, "placeholder") {
		return nil
	}
	
	workingDir, err := os.Getwd()
	if err != nil {
		return fmt.Errorf("failed to get working directory: %v", err)
	}
	
	uploadDir := filepath.Join(workingDir, "frontend", "public", "assets", "uploads")
	
	// Extract filename if it's a path
	filename = filepath.Base(filename)
	filePath := filepath.Join(uploadDir, filename)
	
	// Check if file exists and delete
	if _, err := os.Stat(filePath); err == nil {
		if err := os.Remove(filePath); err != nil {
			return fmt.Errorf("failed to delete file %s: %v", filename, err)
		}
		fmt.Printf("✅ Successfully deleted image: %s\n", filename)
	} else {
		fmt.Printf("⚠️ Image file not found: %s\n", filename)
	}
	
	return nil
}

// IsValidImageType checks if the content type is a valid image type
func IsValidImageType(contentType string) bool {
	validTypes := map[string]bool{
		"image/jpeg": true,
		"image/jpg":  true,
		"image/png":  true,
		"image/webp": true,
		"image/gif":  true,
	}
	return validTypes[contentType]
}
