package controllers

import (
	"fmt"
	"io"
	"math"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"phongnha-valley/backend/internal/models"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/jmoiron/sqlx"
)

type MenuController struct {
	db *sqlx.DB
}

func NewMenuController(db *sqlx.DB) *MenuController {
	return &MenuController{db: db}
}

// ListMenus handles GET /api/v1/menus and GET /api/v1/admin/menus
func (mc *MenuController) ListMenus(c *gin.Context) {
	type MenuRow struct {
		ID           string  `db:"id"`
		Title        string  `db:"title"`
		Description  *string `db:"description"`
		ImageURL     string  `db:"image_url"`
		AltText      *string `db:"alt_text"`
		Category     string  `db:"category"`
		MenuImage    *string `db:"menu_image"`
		DisplayOrder int     `db:"display_order"`
		IsActive     bool    `db:"is_active"`
		CreatedAt    string  `db:"created_at"`
		UpdatedAt    string  `db:"updated_at"`
	}

	// Parse pagination parameters
	page := 1
	limit := 12 // Default items per page

	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	if limitStr := c.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 100 {
			limit = l
		}
	}

	// Parse filters
	category := c.Query("category")
	availableStr := c.Query("available")

	// Calculate offset
	offset := (page - 1) * limit

	// Build WHERE clause
	whereClause := "WHERE 1=1"
	args := []interface{}{}
	argIndex := 1

	// Add category filter
	if category != "" && category != "all" {
		whereClause += fmt.Sprintf(" AND category = $%d", argIndex)
		args = append(args, category)
		argIndex++
	}

	// Add availability filter (for public API, only show active items)
	if availableStr != "" {
		available, err := strconv.ParseBool(availableStr)
		if err == nil {
			whereClause += fmt.Sprintf(" AND is_active = $%d", argIndex)
			args = append(args, available)
			argIndex++
		}
	} else {
		// For public API (non-admin), only show active items
		if !strings.Contains(c.Request.URL.Path, "/admin/") {
			whereClause += fmt.Sprintf(" AND is_active = $%d", argIndex)
			args = append(args, true)
			argIndex++
		}
	}

	// Get total count first
	var totalCount int
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM menus %s", whereClause)
	err := mc.db.Get(&totalCount, countQuery, args...)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to count menu items",
		})
		return
	}

	// Get paginated results
	var rows []MenuRow
	query := fmt.Sprintf(`
		SELECT
			id::text as id, title, description, image_url, alt_text,
			category, menu_image, display_order, is_active,
			created_at::text as created_at,
			updated_at::text as updated_at
		FROM menus
		%s
		ORDER BY display_order ASC, created_at ASC
		LIMIT $%d OFFSET $%d`, whereClause, argIndex, argIndex+1)

	args = append(args, limit, offset)
	err = mc.db.Select(&rows, query, args...)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to fetch menu items",
		})
		return
	}

	// Convert rows to response format
	menus := make([]map[string]interface{}, len(rows))
	for i, row := range rows {
		menus[i] = map[string]interface{}{
			"id":            row.ID,
			"title":         row.Title,
			"description":   row.Description,
			"image_url":     row.ImageURL,
			"alt_text":      row.AltText,
			"category":      row.Category,
			"menu_image":    row.MenuImage,
			"display_order": row.DisplayOrder,
			"is_active":     row.IsActive,
			"created_at":    row.CreatedAt,
			"updated_at":    row.UpdatedAt,
		}
	}

	// Calculate pagination metadata
	totalPages := int(math.Ceil(float64(totalCount) / float64(limit)))
	hasNext := page < totalPages
	hasPrev := page > 1

	c.JSON(http.StatusOK, gin.H{
		"menus": menus,
		"pagination": gin.H{
			"current_page":  page,
			"per_page":      limit,
			"total_count":   totalCount,
			"total_pages":   totalPages,
			"has_next":      hasNext,
			"has_previous":  hasPrev,
		},
	})
}

// GetMenu handles GET /api/v1/menus/:id
func (mc *MenuController) GetMenu(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Menu item ID is required"})
		return
	}

	var menu models.Menu
	query := `SELECT id, title, description, image_url, alt_text, category, menu_image, display_order, is_active, created_at, updated_at
			  FROM menus WHERE id = $1`
	
	err := mc.db.Get(&menu, query, id)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Menu item not found"})
		return
	}

	c.JSON(http.StatusOK, menu)
}

// CreateMenu handles POST /api/v1/admin/menus
func (mc *MenuController) CreateMenu(c *gin.Context) {
	var req models.CreateMenuRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate category
	if err := req.Validate(); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Insert menu item
	menuID := uuid.New()
	query := `
		INSERT INTO menus (id, title, description, image_url, alt_text, category, menu_image)
		VALUES ($1, $2, $3, $4, $5, $6, $7)`

	_, err := mc.db.Exec(query, menuID, req.Title, req.Description, req.ImageURL, req.AltText, req.Category, req.MenuImage)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create menu item"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"id":      menuID,
		"message": "Menu item created successfully",
	})
}

// UpdateMenu handles PUT /api/v1/admin/menus/:id
func (mc *MenuController) UpdateMenu(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Menu item ID is required"})
		return
	}

	var req models.UpdateMenuRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate category if provided
	if err := req.Validate(); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Build dynamic update query
	setParts := []string{}
	args := []interface{}{}
	argIndex := 1

	if req.Title != nil {
		setParts = append(setParts, fmt.Sprintf("title = $%d", argIndex))
		args = append(args, *req.Title)
		argIndex++
	}

	if req.Description != nil {
		setParts = append(setParts, fmt.Sprintf("description = $%d", argIndex))
		args = append(args, *req.Description)
		argIndex++
	}

	if req.ImageURL != nil {
		setParts = append(setParts, fmt.Sprintf("image_url = $%d", argIndex))
		args = append(args, *req.ImageURL)
		argIndex++
	}

	if req.AltText != nil {
		setParts = append(setParts, fmt.Sprintf("alt_text = $%d", argIndex))
		args = append(args, *req.AltText)
		argIndex++
	}

	if req.Category != nil {
		setParts = append(setParts, fmt.Sprintf("category = $%d", argIndex))
		args = append(args, *req.Category)
		argIndex++
	}

	if req.MenuImage != nil {
		setParts = append(setParts, fmt.Sprintf("menu_image = $%d", argIndex))
		args = append(args, *req.MenuImage)
		argIndex++
	}

	if req.IsActive != nil {
		setParts = append(setParts, fmt.Sprintf("is_active = $%d", argIndex))
		args = append(args, *req.IsActive)
		argIndex++
	}

	if len(setParts) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No fields to update"})
		return
	}

	// Add updated_at
	setParts = append(setParts, fmt.Sprintf("updated_at = $%d", argIndex))
	args = append(args, time.Now())
	argIndex++

	// Add WHERE clause
	args = append(args, id)

	query := fmt.Sprintf("UPDATE menus SET %s WHERE id = $%d", strings.Join(setParts, ", "), argIndex)

	result, err := mc.db.Exec(query, args...)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update menu item"})
		return
	}

	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "Menu item not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Menu item updated successfully"})
}

// DeleteMenu handles DELETE /api/v1/admin/menus/:id
func (mc *MenuController) DeleteMenu(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Menu item ID is required"})
		return
	}

	// Get menu item image URL before deletion for cleanup
	var imageURL string
	err := mc.db.Get(&imageURL, "SELECT COALESCE(image_url, '') FROM menus WHERE id = $1", id)
	if err != nil {
		// Continue with deletion even if we can't get image URL
	}

	// Delete menu item
	result, err := mc.db.Exec("DELETE FROM menus WHERE id = $1", id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete menu item"})
		return
	}

	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "Menu item not found"})
		return
	}

	// Clean up image file if it exists and is in uploads directory
	if imageURL != "" && strings.Contains(imageURL, "/assets/uploads/") {
		filename := strings.TrimPrefix(imageURL, "/assets/uploads/")
		if filename != "" {
			filePath := filepath.Join("./frontend/public/assets/uploads", filename)
			os.Remove(filePath) // Ignore errors
		}
	}

	c.JSON(http.StatusOK, gin.H{"message": "Menu item deleted successfully"})
}

// UploadMenuImages handles POST /api/v1/admin/menus/upload
func (mc *MenuController) UploadMenuImages(c *gin.Context) {
	// Parse multipart form
	form, err := c.MultipartForm()
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to parse form data"})
		return
	}

	files := form.File["images"]
	if len(files) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No images provided"})
		return
	}

	// Validate file count
	if len(files) > 10 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Maximum 10 images allowed"})
		return
	}

	var filenames []string
	var urls []string

	// Create uploads directory if it doesn't exist
	uploadDir := "./frontend/public/assets/uploads"
	if err := os.MkdirAll(uploadDir, 0755); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create upload directory"})
		return
	}

	for _, file := range files {
		// Validate file type
		if !strings.HasPrefix(file.Header.Get("Content-Type"), "image/") {
			c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("File %s is not an image", file.Filename)})
			return
		}

		// Validate file size (10MB limit)
		if file.Size > 10*1024*1024 {
			c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("File %s is too large (max 10MB)", file.Filename)})
			return
		}

		// Generate unique filename
		ext := filepath.Ext(file.Filename)
		timestamp := time.Now().Unix()
		filename := fmt.Sprintf("menu_%d_%s%s", timestamp, uuid.New().String()[:8], ext)
		filePath := filepath.Join(uploadDir, filename)

		// Save file
		src, err := file.Open()
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to open uploaded file"})
			return
		}
		defer src.Close()

		dst, err := os.Create(filePath)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create file"})
			return
		}
		defer dst.Close()

		if _, err := io.Copy(dst, src); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save file"})
			return
		}

		filenames = append(filenames, filename)
		urls = append(urls, "/assets/uploads/"+filename)
	}

	c.JSON(http.StatusOK, gin.H{
		"message":   fmt.Sprintf("%d image(s) uploaded successfully", len(filenames)),
		"filenames": filenames,
		"urls":      urls,
	})
}
