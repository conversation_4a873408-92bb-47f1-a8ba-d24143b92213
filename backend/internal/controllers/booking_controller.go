package controllers

import (
	"database/sql"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"strings"
	"time"

	"phongnha-valley/backend/internal/models"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/jmoiron/sqlx"
)

type BookingController struct {
	DB *sqlx.DB
}

func NewBookingController(db *sqlx.DB) *BookingController {
	return &BookingController{DB: db}
}

// CreateBooking creates a new booking
func (bc *BookingController) CreateBooking(c *gin.Context) {
	var req models.CreateBookingRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get service details for pricing calculation
	service, err := bc.getServiceByID(req.ServiceID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Service not found"})
		return
	}

	if !service.IsActive {
		c.J<PERSON>(http.StatusBadRequest, gin.H{"error": "Service is not available"})
		return
	}

	// Calculate total price
	totalPrice := bc.calculateTotalPrice(service, req.Adults, req.ChildrenUnder6, req.Children6To11)

	// Calculate deposit (50% for accommodation, 30% for others)
	depositPercentage := 0.3 // Default 30%
	if service.Type == "ACCOMMODATION" {
		depositPercentage = 0.5 // 50% for accommodation
	}
	depositAmount := totalPrice * depositPercentage

	// Calculate total children for backward compatibility
	totalChildren := req.ChildrenUnder6 + req.Children6To11

	// Create booking
	bookingID := uuid.New()
	booking := models.Booking{
		ID:               bookingID,
		ServiceID:        req.ServiceID,
		BookingDate:      req.BookingDate,
		CheckInDate:      req.CheckInDate,
		CheckOutDate:     req.CheckOutDate,
		CustomerName:     req.CustomerName,
		CustomerEmail:    nil, // Set to null since frontend doesn't provide email
		CustomerPhone:    req.CustomerPhone,
		Adults:           req.Adults,
		Children:         totalChildren,
		ChildrenUnder6:   req.ChildrenUnder6,
		Children6To11:    req.Children6To11,
		TotalPrice:       totalPrice,
		DepositAmount:    depositAmount,
		Status:           "PENDING",
		PaymentStatus:    "PENDING",
		SpecialNotes:     req.SpecialNotes,
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
	}

	// Insert booking into database
	query := `
		INSERT INTO bookings (
			id, service_id, booking_date, check_in_date, check_out_date,
			customer_name, customer_email, customer_phone, adults, children, children_under_6, children_6_to_11,
			total_price, deposit_amount, status, payment_status, special_notes,
			created_at, updated_at
		) VALUES (
			:id, :service_id, :booking_date, :check_in_date, :check_out_date,
			:customer_name, :customer_email, :customer_phone, :adults, :children, :children_under_6, :children_6_to_11,
			:total_price, :deposit_amount, :status, :payment_status, :special_notes,
			:created_at, :updated_at
		)`

	// Create a struct for database insertion with string IDs
	dbBooking := struct {
		ID               string     `db:"id"`
		ServiceID        string     `db:"service_id"`
		BookingDate      time.Time  `db:"booking_date"`
		CheckInDate      *time.Time `db:"check_in_date"`
		CheckOutDate     *time.Time `db:"check_out_date"`
		CustomerName     string     `db:"customer_name"`
		CustomerEmail    *string    `db:"customer_email"`
		CustomerPhone    string     `db:"customer_phone"`
		Adults           int        `db:"adults"`
		Children         int        `db:"children"`
		ChildrenUnder6   int        `db:"children_under_6"`
		Children6To11    int        `db:"children_6_to_11"`
		TotalPrice       float64    `db:"total_price"`
		DepositAmount    float64    `db:"deposit_amount"`
		Status           string     `db:"status"`
		PaymentStatus    string     `db:"payment_status"`
		SpecialNotes     *string    `db:"special_notes"`
		CreatedAt        time.Time  `db:"created_at"`
		UpdatedAt        time.Time  `db:"updated_at"`
	}{
		ID:               booking.ID.String(),
		ServiceID:        booking.ServiceID.String(),
		BookingDate:      booking.BookingDate,
		CheckInDate:      booking.CheckInDate,
		CheckOutDate:     booking.CheckOutDate,
		CustomerName:     booking.CustomerName,
		CustomerEmail:    booking.CustomerEmail,
		CustomerPhone:    booking.CustomerPhone,
		Adults:           booking.Adults,
		Children:         booking.Children,
		ChildrenUnder6:   booking.ChildrenUnder6,
		Children6To11:    booking.Children6To11,
		TotalPrice:       booking.TotalPrice,
		DepositAmount:    booking.DepositAmount,
		Status:           booking.Status,
		PaymentStatus:    booking.PaymentStatus,
		SpecialNotes:     booking.SpecialNotes,
		CreatedAt:        booking.CreatedAt,
		UpdatedAt:        booking.UpdatedAt,
	}

	_, err = bc.DB.NamedExec(query, dbBooking)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create booking"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "Booking created successfully",
		"booking": booking,
	})
}

// GetBooking retrieves a specific booking
func (bc *BookingController) GetBooking(c *gin.Context) {
	bookingID := c.Param("id")

	var booking models.Booking
	query := `SELECT * FROM bookings WHERE id = $1`

	err := bc.DB.Get(&booking, query, bookingID)
	if err != nil {
		if err == sql.ErrNoRows {
			c.JSON(http.StatusNotFound, gin.H{"error": "Booking not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve booking"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"booking": booking})
}

// ListUserBookings lists bookings for the authenticated user
func (bc *BookingController) ListUserBookings(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var bookings []models.Booking
	query := `SELECT * FROM bookings WHERE user_id = $1 ORDER BY created_at DESC`

	err := bc.DB.Select(&bookings, query, userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve bookings"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"bookings": bookings})
}

// ListAllBookings lists all bookings (admin only)
func (bc *BookingController) ListAllBookings(c *gin.Context) {
	// Parse query parameters for pagination and filtering
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	status := c.Query("status")

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	offset := (page - 1) * limit

	// Build query with filters
	var queryBuilder strings.Builder
	var args []interface{}
	argIndex := 1

	queryBuilder.WriteString("SELECT * FROM bookings WHERE 1=1")

	if status != "" {
		queryBuilder.WriteString(fmt.Sprintf(" AND status = $%d", argIndex))
		args = append(args, status)
		argIndex++
	}

	queryBuilder.WriteString(" ORDER BY created_at DESC")
	queryBuilder.WriteString(fmt.Sprintf(" LIMIT $%d OFFSET $%d", argIndex, argIndex+1))
	args = append(args, limit, offset)

	// Create a struct for database query with string IDs and nullable fields
	type BookingDB struct {
		ID               string     `db:"id"`
		UserID           *string    `db:"user_id"`
		ServiceID        string     `db:"service_id"`
		BookingDate      time.Time  `db:"booking_date"`
		CheckInDate      *time.Time `db:"check_in_date"`
		CheckOutDate     *time.Time `db:"check_out_date"`
		CustomerName     string     `db:"customer_name"`
		CustomerEmail    *string    `db:"customer_email"`
		CustomerPhone    string     `db:"customer_phone"`
		Guests           *int       `db:"guests"`           // Legacy field, nullable
		Adults           *int       `db:"adults"`           // Nullable for backward compatibility
		Children         *int       `db:"children"`         // Nullable for backward compatibility
		ChildrenUnder6   *int       `db:"children_under_6"` // Nullable for backward compatibility
		Children6To11    *int       `db:"children_6_to_11"` // Nullable for backward compatibility
		TotalPrice       *float64   `db:"total_price"`      // Nullable for backward compatibility
		DepositAmount    *float64   `db:"deposit_amount"`   // Nullable for backward compatibility
		Status           string     `db:"status"`
		PaymentStatus    *string    `db:"payment_status"`   // Nullable for backward compatibility
		Notes            *string    `db:"notes"`            // Legacy field
		SpecialNotes     *string    `db:"special_notes"`
		CreatedAt        time.Time  `db:"created_at"`
		UpdatedAt        time.Time  `db:"updated_at"`
	}

	var dbBookings []BookingDB
	err := bc.DB.Select(&dbBookings, queryBuilder.String(), args...)
	if err != nil {
		log.Printf("Error retrieving bookings: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve bookings"})
		return
	}

	// Convert to models.Booking for response
	var bookings []models.Booking
	for _, dbBooking := range dbBookings {
		// Parse UUIDs
		id, err := uuid.Parse(dbBooking.ID)
		if err != nil {
			log.Printf("Error parsing booking ID %s: %v", dbBooking.ID, err)
			continue
		}

		serviceID, err := uuid.Parse(dbBooking.ServiceID)
		if err != nil {
			log.Printf("Error parsing service ID %s: %v", dbBooking.ServiceID, err)
			continue
		}

		var userID *uuid.UUID
		if dbBooking.UserID != nil {
			parsedUserID, err := uuid.Parse(*dbBooking.UserID)
			if err == nil {
				userID = &parsedUserID
			}
		}

		// Handle nullable integer fields with defaults
		adults := 1
		if dbBooking.Adults != nil {
			adults = *dbBooking.Adults
		} else if dbBooking.Guests != nil {
			adults = *dbBooking.Guests // Use legacy guests field if adults is null
		}

		children := 0
		if dbBooking.Children != nil {
			children = *dbBooking.Children
		}

		childrenUnder6 := 0
		if dbBooking.ChildrenUnder6 != nil {
			childrenUnder6 = *dbBooking.ChildrenUnder6
		}

		children6To11 := 0
		if dbBooking.Children6To11 != nil {
			children6To11 = *dbBooking.Children6To11
		}

		totalPrice := 0.0
		if dbBooking.TotalPrice != nil {
			totalPrice = *dbBooking.TotalPrice
		}

		depositAmount := 0.0
		if dbBooking.DepositAmount != nil {
			depositAmount = *dbBooking.DepositAmount
		}

		paymentStatus := "PENDING"
		if dbBooking.PaymentStatus != nil {
			paymentStatus = *dbBooking.PaymentStatus
		}

		// Use special_notes if available, otherwise fall back to notes
		var specialNotes *string
		if dbBooking.SpecialNotes != nil {
			specialNotes = dbBooking.SpecialNotes
		} else if dbBooking.Notes != nil {
			specialNotes = dbBooking.Notes
		}

		booking := models.Booking{
			ID:               id,
			UserID:           userID,
			ServiceID:        serviceID,
			BookingDate:      dbBooking.BookingDate,
			CheckInDate:      dbBooking.CheckInDate,
			CheckOutDate:     dbBooking.CheckOutDate,
			CustomerName:     dbBooking.CustomerName,
			CustomerEmail:    dbBooking.CustomerEmail,
			CustomerPhone:    dbBooking.CustomerPhone,
			Adults:           adults,
			Children:         children,
			ChildrenUnder6:   childrenUnder6,
			Children6To11:    children6To11,
			TotalPrice:       totalPrice,
			DepositAmount:    depositAmount,
			Status:           dbBooking.Status,
			PaymentStatus:    paymentStatus,
			SpecialNotes:     specialNotes,
			CreatedAt:        dbBooking.CreatedAt,
			UpdatedAt:        dbBooking.UpdatedAt,
		}
		bookings = append(bookings, booking)
	}

	// Get total count for pagination
	countQuery := "SELECT COUNT(*) FROM bookings WHERE 1=1"
	countArgs := []interface{}{}
	if status != "" {
		countQuery += " AND status = $1"
		countArgs = append(countArgs, status)
	}

	var total int
	err = bc.DB.Get(&total, countQuery, countArgs...)
	if err != nil {
		total = 0
	}

	c.JSON(http.StatusOK, gin.H{
		"bookings": bookings,
		"pagination": gin.H{
			"page":  page,
			"limit": limit,
			"total": total,
		},
	})
}

// UpdateBooking updates a booking
func (bc *BookingController) UpdateBooking(c *gin.Context) {
	bookingID := c.Param("id")

	// Validate UUID
	if _, err := uuid.Parse(bookingID); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid booking ID"})
		return
	}

	// Parse request body
	var req struct {
		Status        *string `json:"status,omitempty"`
		PaymentStatus *string `json:"payment_status,omitempty"`
		SpecialNotes  *string `json:"special_notes,omitempty"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Check if booking exists using string ID
	var count int
	err := bc.DB.Get(&count, "SELECT COUNT(*) FROM bookings WHERE id = $1", bookingID)
	if err != nil {
		log.Printf("Error checking booking existence: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check booking"})
		return
	}
	if count == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "Booking not found"})
		return
	}

	// Build dynamic update query
	var setParts []string
	var args []interface{}
	argIndex := 1

	if req.Status != nil {
		// Validate status
		validStatuses := []string{"PENDING", "CONFIRMED", "COMPLETED", "CANCELLED"}
		isValid := false
		for _, status := range validStatuses {
			if *req.Status == status {
				isValid = true
				break
			}
		}
		if !isValid {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid status. Must be one of: PENDING, CONFIRMED, COMPLETED, CANCELLED"})
			return
		}
		setParts = append(setParts, fmt.Sprintf("status = $%d", argIndex))
		args = append(args, *req.Status)
		argIndex++
	}

	if req.PaymentStatus != nil {
		// Validate payment status
		validPaymentStatuses := []string{"PENDING", "PARTIAL", "COMPLETED", "REFUNDED"}
		isValid := false
		for _, status := range validPaymentStatuses {
			if *req.PaymentStatus == status {
				isValid = true
				break
			}
		}
		if !isValid {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid payment status. Must be one of: PENDING, PARTIAL, COMPLETED, REFUNDED"})
			return
		}
		setParts = append(setParts, fmt.Sprintf("payment_status = $%d", argIndex))
		args = append(args, *req.PaymentStatus)
		argIndex++
	}

	if req.SpecialNotes != nil {
		setParts = append(setParts, fmt.Sprintf("special_notes = $%d", argIndex))
		args = append(args, *req.SpecialNotes)
		argIndex++
	}

	if len(setParts) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No fields to update"})
		return
	}

	// Add updated_at
	setParts = append(setParts, fmt.Sprintf("updated_at = $%d", argIndex))
	args = append(args, time.Now())
	argIndex++

	// Add booking ID for WHERE clause
	args = append(args, bookingID)

	// Execute update
	query := fmt.Sprintf("UPDATE bookings SET %s WHERE id = $%d", strings.Join(setParts, ", "), argIndex)
	result, err := bc.DB.Exec(query, args...)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update booking"})
		return
	}

	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "Booking not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Booking updated successfully",
		"booking_id": bookingID,
	})
}

// CancelBooking cancels a booking (sets status to CANCELLED)
func (bc *BookingController) CancelBooking(c *gin.Context) {
	bookingID := c.Param("id")

	// Validate UUID
	if _, err := uuid.Parse(bookingID); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid booking ID"})
		return
	}

	// Check if booking exists and get current status
	var currentStatus string
	err := bc.DB.Get(&currentStatus, "SELECT status FROM bookings WHERE id = $1", bookingID)
	if err != nil {
		if err == sql.ErrNoRows {
			c.JSON(http.StatusNotFound, gin.H{"error": "Booking not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve booking"})
		return
	}

	// Check if booking can be cancelled
	if currentStatus == "COMPLETED" || currentStatus == "CANCELLED" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Cannot cancel a completed or already cancelled booking"})
		return
	}

	// Update booking status to CANCELLED
	query := `UPDATE bookings SET status = 'CANCELLED', updated_at = NOW() WHERE id = $1`
	_, err = bc.DB.Exec(query, bookingID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to cancel booking"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Booking cancelled successfully"})
}

// DeleteBooking permanently deletes a booking (admin only)
func (bc *BookingController) DeleteBooking(c *gin.Context) {
	bookingID := c.Param("id")

	// Validate UUID
	if _, err := uuid.Parse(bookingID); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid booking ID"})
		return
	}

	// Check if booking exists and get current status
	var booking models.Booking
	err := bc.DB.Get(&booking, "SELECT * FROM bookings WHERE id = $1", bookingID)
	if err != nil {
		if err == sql.ErrNoRows {
			c.JSON(http.StatusNotFound, gin.H{"error": "Booking not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve booking"})
		return
	}

	// Only allow deletion of COMPLETED or CANCELLED bookings
	if booking.Status != "COMPLETED" && booking.Status != "CANCELLED" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Can only delete completed or cancelled bookings",
			"current_status": booking.Status,
		})
		return
	}

	// Permanently delete the booking
	query := `DELETE FROM bookings WHERE id = $1`
	result, err := bc.DB.Exec(query, bookingID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete booking"})
		return
	}

	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "Booking not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Booking deleted permanently",
		"booking_id": bookingID,
	})
}

// Helper methods

// getServiceByID retrieves a service by ID
func (bc *BookingController) getServiceByID(serviceID uuid.UUID) (*models.Service, error) {
	var service models.Service
	query := `
		SELECT
			id, type, name, description, price, child_price, is_active, capacity,
			open_time, close_time,
			COALESCE(array_to_json(images)::text, '[]') as images,
			created_at,
			COALESCE(array_to_json(inclusions)::text, '[]') as inclusions,
			COALESCE(array_to_json(requirements)::text, '[]') as requirements,
			COALESCE(age_restriction::text, '{}') as age_restriction,
			COALESCE(booking_policy::text, '{}') as booking_policy
		FROM services
		WHERE id = $1 AND is_active = true`

	err := bc.DB.Get(&service, query, serviceID)
	if err != nil {
		return nil, err
	}
	return &service, nil
}

// calculateTotalPrice calculates the total price for a booking
func (bc *BookingController) calculateTotalPrice(service *models.Service, adults, childrenUnder6, children6To11 int) float64 {
	totalPrice := float64(adults) * service.Price

	// Children under 6 are free
	// Children 6-11 get 50% discount
	if service.ChildPrice != nil {
		totalPrice += float64(children6To11) * (*service.ChildPrice)
	} else {
		// If no child price specified, use 50% of adult price
		totalPrice += float64(children6To11) * (service.Price * 0.5)
	}

	return totalPrice
}