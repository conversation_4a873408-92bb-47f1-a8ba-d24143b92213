package controllers

import (
	"fmt"
	"net/http"
	"phongnha-valley/backend/internal/models"

	"github.com/gin-gonic/gin"
	"github.com/jmoiron/sqlx"
)

type SettingsController struct {
	DB *sqlx.DB
}

func NewSettingsController(db *sqlx.DB) *SettingsController {
	return &SettingsController{DB: db}
}

// GetSettings retrieves all settings in structured format
func (sc *SettingsController) GetSettings(c *gin.Context) {
	var settings []models.Setting
	
	query := `
		SELECT id, setting_key, setting_value, setting_type, description, is_active, created_at, updated_at
		FROM settings
		WHERE is_active = true
		ORDER BY setting_key
	`
	
	err := sc.DB.Select(&settings, query)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to fetch settings",
			"details": err.<PERSON>rror(),
		})
		return
	}

	// Convert to structured response
	response := sc.structureSettings(settings)
	
	c.J<PERSON>(http.StatusOK, gin.H{
		"settings": response,
		"raw_settings": settings, // For debugging
	})
}

// GetSetting retrieves a single setting by key
func (sc *SettingsController) GetSetting(c *gin.Context) {
	key := c.Param("key")
	if key == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Setting key is required"})
		return
	}

	var setting models.Setting
	query := `
		SELECT id, setting_key, setting_value, setting_type, description, is_active, created_at, updated_at
		FROM settings
		WHERE setting_key = $1 AND is_active = true
	`
	
	err := sc.DB.Get(&setting, query, key)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Setting not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"setting": setting})
}

// UpdateSettings updates multiple settings
func (sc *SettingsController) UpdateSettings(c *gin.Context) {
	var request models.UpdateSettingsRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// Convert structured request to individual setting updates
	updates := sc.flattenSettingsRequest(request)
	
	if len(updates) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No settings to update"})
		return
	}

	// Update each setting
	for _, update := range updates {
		err := sc.updateSingleSetting(update.Key, update.Value)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": fmt.Sprintf("Failed to update setting %s", update.Key),
				"details": err.Error(),
			})
			return
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Settings updated successfully",
		"updated_count": len(updates),
	})
}

// BatchUpdateSettings updates settings using batch format
func (sc *SettingsController) BatchUpdateSettings(c *gin.Context) {
	var request models.BatchUpdateRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	if len(request.Settings) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No settings to update"})
		return
	}

	// Update each setting
	for _, update := range request.Settings {
		err := sc.updateSingleSetting(update.Key, update.Value)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": fmt.Sprintf("Failed to update setting %s", update.Key),
				"details": err.Error(),
			})
			return
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Settings updated successfully",
		"updated_count": len(request.Settings),
	})
}

// Helper function to update a single setting
func (sc *SettingsController) updateSingleSetting(key, value string) error {
	query := `
		UPDATE settings 
		SET setting_value = $1, updated_at = CURRENT_TIMESTAMP
		WHERE setting_key = $2 AND is_active = true
	`
	
	result, err := sc.DB.Exec(query, value, key)
	if err != nil {
		return err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}

	if rowsAffected == 0 {
		return fmt.Errorf("setting with key '%s' not found or inactive", key)
	}

	return nil
}

// Helper function to structure settings into response format
func (sc *SettingsController) structureSettings(settings []models.Setting) models.SettingsResponse {
	response := models.SettingsResponse{}
	
	for _, setting := range settings {
		value := ""
		if setting.SettingValue != nil {
			value = *setting.SettingValue
		}

		switch setting.SettingKey {
		// General settings
		case "site_title":
			response.General.SiteTitle = value
		
		// Contact settings
		case "contact_email":
			response.Contact.Email = value
		case "contact_phone":
			response.Contact.Phone = value
		case "address":
			response.Contact.Address = value
		case "booking_email":
			response.Contact.BookingEmail = value
		case "emergency_phone":
			response.Contact.EmergencyPhone = value
		
		// Social media settings
		case "instagram_url":
			response.SocialMedia.Instagram = value
		case "facebook_url":
			response.SocialMedia.Facebook = value
		case "youtube_url":
			response.SocialMedia.YouTube = value
		
		// Business settings
		case "opening_hours":
			response.Business.OpeningHours = value
		case "google_maps_embed":
			response.Business.GoogleMapsEmbed = value
		}
	}
	
	return response
}

// Helper function to flatten structured request into individual updates
func (sc *SettingsController) flattenSettingsRequest(request models.UpdateSettingsRequest) []models.SettingUpdate {
	var updates []models.SettingUpdate

	// General settings
	if request.General != nil {
		if request.General.SiteTitle != "" {
			updates = append(updates, models.SettingUpdate{
				Key:   "site_title",
				Value: request.General.SiteTitle,
			})
		}
	}

	// Contact settings
	if request.Contact != nil {
		if request.Contact.Email != "" {
			updates = append(updates, models.SettingUpdate{
				Key:   "contact_email",
				Value: request.Contact.Email,
			})
		}
		if request.Contact.Phone != "" {
			updates = append(updates, models.SettingUpdate{
				Key:   "contact_phone",
				Value: request.Contact.Phone,
			})
		}
		if request.Contact.Address != "" {
			updates = append(updates, models.SettingUpdate{
				Key:   "address",
				Value: request.Contact.Address,
			})
		}
		if request.Contact.BookingEmail != "" {
			updates = append(updates, models.SettingUpdate{
				Key:   "booking_email",
				Value: request.Contact.BookingEmail,
			})
		}
		if request.Contact.EmergencyPhone != "" {
			updates = append(updates, models.SettingUpdate{
				Key:   "emergency_phone",
				Value: request.Contact.EmergencyPhone,
			})
		}
	}

	// Social media settings
	if request.SocialMedia != nil {
		if request.SocialMedia.Instagram != "" {
			updates = append(updates, models.SettingUpdate{
				Key:   "instagram_url",
				Value: request.SocialMedia.Instagram,
			})
		}
		if request.SocialMedia.Facebook != "" {
			updates = append(updates, models.SettingUpdate{
				Key:   "facebook_url",
				Value: request.SocialMedia.Facebook,
			})
		}
		if request.SocialMedia.YouTube != "" {
			updates = append(updates, models.SettingUpdate{
				Key:   "youtube_url",
				Value: request.SocialMedia.YouTube,
			})
		}
	}

	// Business settings
	if request.Business != nil {
		if request.Business.OpeningHours != "" {
			updates = append(updates, models.SettingUpdate{
				Key:   "opening_hours",
				Value: request.Business.OpeningHours,
			})
		}
		if request.Business.GoogleMapsEmbed != "" {
			updates = append(updates, models.SettingUpdate{
				Key:   "google_maps_embed",
				Value: request.Business.GoogleMapsEmbed,
			})
		}
	}

	return updates
}

// ResetSettings resets all settings to default values
func (sc *SettingsController) ResetSettings(c *gin.Context) {
	// This would reset settings to default values
	// Implementation depends on business requirements
	c.JSON(http.StatusOK, gin.H{
		"message": "Settings reset functionality not implemented yet",
	})
}
