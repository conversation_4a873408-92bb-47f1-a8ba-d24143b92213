package controllers

import (
	"fmt"
	"io"
	"math"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/jmoiron/sqlx"
)

type ContentController struct {
	DB *sqlx.DB
}

func NewContentController(db *sqlx.DB) *ContentController {
	return &ContentController{DB: db}
}

// Homepage content endpoints
func (cc *ContentController) GetHomepageContent(c *gin.Context) {
	sectionKey := c.Param("section")
	if sectionKey == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Section key is required"})
		return
	}

	type HomepageContentRow struct {
		ID        string  `db:"id"`
		SectionKey string `db:"section_key"`
		Title     *string `db:"title"`
		Subtitle  *string `db:"subtitle"`
		Content   *string `db:"content"`
		ImageURL  *string `db:"image_url"`
		Images    *string `db:"images"`
		Metadata  *string `db:"metadata"`
		IsActive  bool    `db:"is_active"`
		CreatedAt string  `db:"created_at"`
		UpdatedAt string  `db:"updated_at"`
	}

	var row HomepageContentRow
	query := `
		SELECT
			id::text as id, section_key, title, subtitle, content, image_url,
			CASE
				WHEN images IS NOT NULL THEN array_to_string(images, ',')
				ELSE NULL
			END as images,
			metadata::text as metadata, is_active,
			created_at::text as created_at,
			updated_at::text as updated_at
		FROM homepage_content
		WHERE section_key = $1 AND is_active = true`

	err := cc.DB.Get(&row, query, sectionKey)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Homepage content not found"})
		return
	}

	// Parse images string back to array for JSON response
	var imagesArray []string
	if row.Images != nil && *row.Images != "" {
		imagesArray = strings.Split(*row.Images, ",")
	}

	c.JSON(http.StatusOK, gin.H{
		"id":          row.ID,
		"section_key": row.SectionKey,
		"title":       row.Title,
		"subtitle":    row.Subtitle,
		"content":     row.Content,
		"image_url":   row.ImageURL,
		"images":      imagesArray,
		"metadata":    row.Metadata,
		"is_active":   row.IsActive,
		"created_at":  row.CreatedAt,
		"updated_at":  row.UpdatedAt,
	})
}

func (cc *ContentController) UpdateHomepageContent(c *gin.Context) {
	sectionKey := c.Param("section")
	if sectionKey == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Section key is required"})
		return
	}

	type UpdateHomepageContentRequest struct {
		Title    *string   `json:"title"`
		Subtitle *string   `json:"subtitle"`
		Content  *string   `json:"content"`
		ImageURL *string   `json:"image_url"`
		Images   []string  `json:"images"`
		Metadata *string   `json:"metadata"`
	}

	var req UpdateHomepageContentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Build dynamic update query
	setParts := []string{}
	args := []interface{}{}
	argIndex := 1

	if req.Title != nil {
		setParts = append(setParts, fmt.Sprintf("title = $%d", argIndex))
		args = append(args, *req.Title)
		argIndex++
	}
	if req.Subtitle != nil {
		setParts = append(setParts, fmt.Sprintf("subtitle = $%d", argIndex))
		args = append(args, *req.Subtitle)
		argIndex++
	}
	if req.Content != nil {
		setParts = append(setParts, fmt.Sprintf("content = $%d", argIndex))
		args = append(args, *req.Content)
		argIndex++
	}
	if req.ImageURL != nil {
		setParts = append(setParts, fmt.Sprintf("image_url = $%d", argIndex))
		args = append(args, *req.ImageURL)
		argIndex++
	}

	if req.Images != nil {
		setParts = append(setParts, fmt.Sprintf("images = $%d", argIndex))
		args = append(args, fmt.Sprintf("{%s}", strings.Join(req.Images, ",")))
		argIndex++
	}
	if req.Metadata != nil {
		setParts = append(setParts, fmt.Sprintf("metadata = $%d", argIndex))
		args = append(args, *req.Metadata)
		argIndex++
	}

	if len(setParts) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No fields to update"})
		return
	}

	// Add updated_at
	setParts = append(setParts, fmt.Sprintf("updated_at = $%d", argIndex))
	args = append(args, time.Now())
	argIndex++

	// Add section_key for WHERE clause
	args = append(args, sectionKey)

	query := fmt.Sprintf(`
		UPDATE homepage_content
		SET %s
		WHERE section_key = $%d AND is_active = true`,
		strings.Join(setParts, ", "), argIndex)

	result, err := cc.DB.Exec(query, args...)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update homepage content"})
		return
	}

	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "Homepage content not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Homepage content updated successfully"})
}

// Article endpoints
func (cc *ContentController) ListArticles(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "List articles endpoint"})
}

func (cc *ContentController) GetArticle(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "Get article endpoint"})
}

func (cc *ContentController) CreateArticle(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "Create article endpoint"})
}

func (cc *ContentController) UpdateArticle(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "Update article endpoint"})
}

func (cc *ContentController) DeleteArticle(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "Delete article endpoint"})
}

// Gallery endpoints
func (cc *ContentController) ListGalleryItems(c *gin.Context) {
	type GalleryRow struct {
		ID           string  `db:"id"`
		Title        string  `db:"title"`
		Description  *string `db:"description"`
		ImageURL     string  `db:"image_url"`
		AltText      *string `db:"alt_text"`
		DisplayOrder int     `db:"display_order"`
		IsActive     bool    `db:"is_active"`
		CreatedAt    string  `db:"created_at"`
		UpdatedAt    string  `db:"updated_at"`
	}

	// Parse pagination parameters
	page := 1
	limit := 12 // Default items per page

	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	if limitStr := c.Query("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 100 {
			limit = l
		}
	}

	// Calculate offset
	offset := (page - 1) * limit

	// Get total count first
	var totalCount int
	countQuery := `SELECT COUNT(*) FROM gallery WHERE is_active = true`
	err := cc.DB.Get(&totalCount, countQuery)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to count gallery items",
			"details": err.Error(),
		})
		return
	}

	// Get paginated results
	var rows []GalleryRow
	query := `
		SELECT
			id::text as id, title, description, image_url, alt_text,
			display_order, is_active,
			created_at::text as created_at,
			updated_at::text as updated_at
		FROM gallery
		WHERE is_active = true
		ORDER BY display_order ASC, created_at DESC
		LIMIT $1 OFFSET $2`

	err = cc.DB.Select(&rows, query, limit, offset)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to fetch gallery items",
			"details": err.Error(),
		})
		return
	}

	// Convert rows to response format
	gallery := make([]map[string]interface{}, len(rows))
	for i, row := range rows {
		gallery[i] = map[string]interface{}{
			"id":            row.ID,
			"title":         row.Title,
			"description":   row.Description,
			"image_url":     row.ImageURL,
			"alt_text":      row.AltText,
			"display_order": row.DisplayOrder,
			"is_active":     row.IsActive,
			"created_at":    row.CreatedAt,
			"updated_at":    row.UpdatedAt,
		}
	}

	// Calculate pagination metadata
	totalPages := int(math.Ceil(float64(totalCount) / float64(limit)))
	hasNext := page < totalPages
	hasPrev := page > 1

	c.JSON(http.StatusOK, gin.H{
		"gallery": gallery,
		"pagination": gin.H{
			"current_page":  page,
			"per_page":      limit,
			"total_count":   totalCount,
			"total_pages":   totalPages,
			"has_next":      hasNext,
			"has_previous":  hasPrev,
		},
	})
}

func (cc *ContentController) GetGalleryItem(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "Get gallery item endpoint"})
}

// Content blocks endpoints
func (cc *ContentController) GetContentBlock(c *gin.Context) {
	blockKey := c.Param("key")
	if blockKey == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Block key is required"})
		return
	}

	type ContentBlockRow struct {
		ID        string  `db:"id"`
		BlockKey  string  `db:"block_key"`
		Title     *string `db:"title"`
		Content   *string `db:"content"`
		ImageURL  *string `db:"image_url"`
		Metadata  *string `db:"metadata"`
		IsActive  bool    `db:"is_active"`
		CreatedAt string  `db:"created_at"`
		UpdatedAt string  `db:"updated_at"`
	}

	var row ContentBlockRow
	query := `
		SELECT
			id::text as id, block_key, title, content, image_url,
			metadata::text as metadata, is_active,
			created_at::text as created_at,
			updated_at::text as updated_at
		FROM content_blocks
		WHERE block_key = $1 AND is_active = true`

	err := cc.DB.Get(&row, query, blockKey)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Content block not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"id":         row.ID,
		"block_key":  row.BlockKey,
		"title":      row.Title,
		"content":    row.Content,
		"image_url":  row.ImageURL,
		"metadata":   row.Metadata,
		"is_active":  row.IsActive,
		"created_at": row.CreatedAt,
		"updated_at": row.UpdatedAt,
	})
}

func (cc *ContentController) CreateGalleryItem(c *gin.Context) {
	type CreateGalleryRequest struct {
		Title       string  `json:"title" binding:"required"`
		Description *string `json:"description"`
		ImageURL    string  `json:"image_url" binding:"required"`
		AltText     *string `json:"alt_text"`
	}

	var req CreateGalleryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Insert gallery item
	galleryID := uuid.New()
	query := `
		INSERT INTO gallery (id, title, description, image_url, alt_text)
		VALUES ($1, $2, $3, $4, $5)`

	_, err := cc.DB.Exec(query, galleryID, req.Title, req.Description, req.ImageURL, req.AltText)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create gallery item"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"id":      galleryID,
		"message": "Gallery item created successfully",
	})
}

func (cc *ContentController) UpdateGalleryItem(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Gallery item ID is required"})
		return
	}

	type UpdateGalleryRequest struct {
		Title       *string `json:"title"`
		Description *string `json:"description"`
		ImageURL    *string `json:"image_url"`
		AltText     *string `json:"alt_text"`
		IsActive    *bool   `json:"is_active"`
	}

	var req UpdateGalleryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Build dynamic update query
	var setParts []string
	var args []interface{}
	argIndex := 1

	if req.Title != nil {
		setParts = append(setParts, fmt.Sprintf("title = $%d", argIndex))
		args = append(args, *req.Title)
		argIndex++
	}
	if req.Description != nil {
		setParts = append(setParts, fmt.Sprintf("description = $%d", argIndex))
		args = append(args, *req.Description)
		argIndex++
	}
	if req.ImageURL != nil {
		setParts = append(setParts, fmt.Sprintf("image_url = $%d", argIndex))
		args = append(args, *req.ImageURL)
		argIndex++
	}
	if req.AltText != nil {
		setParts = append(setParts, fmt.Sprintf("alt_text = $%d", argIndex))
		args = append(args, *req.AltText)
		argIndex++
	}
	if req.IsActive != nil {
		setParts = append(setParts, fmt.Sprintf("is_active = $%d", argIndex))
		args = append(args, *req.IsActive)
		argIndex++
	}

	if len(setParts) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No fields to update"})
		return
	}

	// Add updated_at
	setParts = append(setParts, fmt.Sprintf("updated_at = $%d", argIndex))
	args = append(args, time.Now())
	argIndex++

	// Add ID for WHERE clause
	args = append(args, id)

	query := fmt.Sprintf("UPDATE gallery SET %s WHERE id = $%d", strings.Join(setParts, ", "), argIndex)
	result, err := cc.DB.Exec(query, args...)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update gallery item"})
		return
	}

	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "Gallery item not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Gallery item updated successfully"})
}

func (cc *ContentController) DeleteGalleryItem(c *gin.Context) {
	id := c.Param("id")
	fmt.Printf("🗑️ DELETE Gallery Item - ID: %s\n", id)

	if id == "" {
		fmt.Println("❌ DELETE Gallery Item - Missing ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Gallery item ID is required"})
		return
	}

	// Get gallery item image URL before deletion for cleanup
	var imageURL string
	err := cc.DB.Get(&imageURL, "SELECT COALESCE(image_url, '') FROM gallery WHERE id = $1", id)
	if err != nil {
		fmt.Printf("⚠️ DELETE Gallery Item - Failed to get image URL: %v\n", err)
		// Continue with deletion even if we can't get image URL
	}

	// Delete gallery item from database
	query := `DELETE FROM gallery WHERE id = $1`
	fmt.Printf("🔍 DELETE Gallery Item - Executing query: %s with ID: %s\n", query, id)

	result, err := cc.DB.Exec(query, id)
	if err != nil {
		fmt.Printf("❌ DELETE Gallery Item - Database error: %v\n", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete gallery item"})
		return
	}

	rowsAffected, _ := result.RowsAffected()
	fmt.Printf("📊 DELETE Gallery Item - Rows affected: %d\n", rowsAffected)

	if rowsAffected == 0 {
		fmt.Printf("❌ DELETE Gallery Item - No rows affected, item not found\n")
		c.JSON(http.StatusNotFound, gin.H{"error": "Gallery item not found"})
		return
	}

	// Delete associated image file after successful database deletion
	if imageURL != "" {
		cc.deleteGalleryImage(imageURL)
	}

	fmt.Printf("✅ DELETE Gallery Item - Successfully deleted ID: %s\n", id)
	c.JSON(http.StatusOK, gin.H{"message": "Gallery item deleted successfully"})
}

// deleteGalleryImage deletes image file from storage
func (cc *ContentController) deleteGalleryImage(imageURL string) {
	if imageURL == "" || strings.Contains(imageURL, "placeholder") {
		return
	}

	workingDir, err := os.Getwd()
	if err != nil {
		fmt.Printf("❌ DELETE Gallery Image - Failed to get working directory: %v\n", err)
		return
	}

	uploadDir := filepath.Join(workingDir, "frontend", "public", "assets", "uploads")

	// Extract filename from URL (handle both /assets/uploads/filename and just filename)
	filename := filepath.Base(imageURL)
	if strings.HasPrefix(imageURL, "/assets/uploads/") {
		filename = strings.TrimPrefix(imageURL, "/assets/uploads/")
	}

	filePath := filepath.Join(uploadDir, filename)

	// Check if file exists and delete
	if _, err := os.Stat(filePath); err == nil {
		if err := os.Remove(filePath); err != nil {
			fmt.Printf("❌ DELETE Gallery Image - Failed to delete %s: %v\n", filename, err)
		} else {
			fmt.Printf("✅ DELETE Gallery Image - Successfully deleted: %s\n", filename)
		}
	} else {
		fmt.Printf("⚠️ DELETE Gallery Image - File not found: %s\n", filename)
	}
}

// UploadImages handles multiple image uploads
func (cc *ContentController) UploadImages(c *gin.Context) {
	// Parse multipart form
	err := c.Request.ParseMultipartForm(32 << 20) // 32 MB max
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to parse multipart form"})
		return
	}

	form := c.Request.MultipartForm
	files := form.File["images"]

	if len(files) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No files uploaded"})
		return
	}

	// Create uploads directory if it doesn't exist
	// Use absolute path to avoid issues with working directory
	workingDir, err := os.Getwd()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get working directory"})
		return
	}
	uploadDir := filepath.Join(workingDir, "frontend", "public", "assets", "uploads")
	if err := os.MkdirAll(uploadDir, 0755); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create upload directory"})
		return
	}

	var uploadedFiles []map[string]interface{}

	for _, fileHeader := range files {
		// Validate file type
		if !isValidImageType(fileHeader.Filename) {
			continue // Skip invalid files
		}

		// Create unique filename to avoid conflicts
		ext := filepath.Ext(fileHeader.Filename)
		nameWithoutExt := strings.TrimSuffix(fileHeader.Filename, ext)
		timestamp := time.Now().Unix()
		filename := fmt.Sprintf("%s_%d%s", nameWithoutExt, timestamp, ext)
		filePath := filepath.Join(uploadDir, filename)

		// Open uploaded file
		file, err := fileHeader.Open()
		if err != nil {
			continue // Skip files that can't be opened
		}
		defer file.Close()

		// Create destination file
		dst, err := os.Create(filePath)
		if err != nil {
			continue // Skip files that can't be created
		}
		defer dst.Close()

		// Copy file content
		if _, err := io.Copy(dst, file); err != nil {
			os.Remove(filePath) // Clean up on error
			continue
		}

		// Add to uploaded files list
		uploadedFiles = append(uploadedFiles, map[string]interface{}{
			"filename":     filename,
			"original_name": fileHeader.Filename,
			"url":          fmt.Sprintf("/assets/uploads/%s", filename),
			"size":         fileHeader.Size,
		})
	}

	if len(uploadedFiles) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No valid image files uploaded"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Images uploaded successfully",
		"files":   uploadedFiles,
		"count":   len(uploadedFiles),
	})
}

// DeleteImage handles single image deletion
func (cc *ContentController) DeleteImage(c *gin.Context) {
	type DeleteImageRequest struct {
		Filename string `json:"filename" binding:"required"`
	}

	var req DeleteImageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate filename (security check)
	if strings.Contains(req.Filename, "..") || strings.Contains(req.Filename, "/") {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid filename"})
		return
	}

	// Construct file path using same logic as UploadImages
	workingDir, err := os.Getwd()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get working directory"})
		return
	}
	uploadDir := filepath.Join(workingDir, "frontend", "public", "assets", "uploads")
	filePath := filepath.Join(uploadDir, req.Filename)

	// Check if file exists
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		c.JSON(http.StatusNotFound, gin.H{"error": "File not found"})
		return
	}

	// Delete the file
	if err := os.Remove(filePath); err != nil {
		fmt.Printf("❌ DELETE Image - Failed to delete file: %v\n", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete file"})
		return
	}

	fmt.Printf("✅ DELETE Image - Successfully deleted: %s\n", req.Filename)
	c.JSON(http.StatusOK, gin.H{"message": "Image deleted successfully"})
}

// CleanupOrphanedImages removes image files that are not referenced in database (admin only)
func (cc *ContentController) CleanupOrphanedImages(c *gin.Context) {
	fmt.Println("🧹 Starting orphaned images cleanup via API...")

	// Get working directory
	workingDir, err := os.Getwd()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get working directory"})
		return
	}

	uploadDir := filepath.Join(workingDir, "frontend", "public", "assets", "uploads")

	// Get all image files from uploads directory
	files, err := os.ReadDir(uploadDir)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to read uploads directory"})
		return
	}

	// Get all referenced images from database
	referencedImages, err := cc.getAllReferencedImages()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get referenced images"})
		return
	}

	// Create map for faster lookup
	referencedMap := make(map[string]bool)
	for _, img := range referencedImages {
		filename := filepath.Base(img)
		referencedMap[filename] = true
	}

	// Check each file and delete orphaned ones
	var deletedFiles []string
	for _, file := range files {
		if file.IsDir() || strings.HasPrefix(file.Name(), ".") {
			continue
		}

		filename := file.Name()
		if !referencedMap[filename] {
			filePath := filepath.Join(uploadDir, filename)
			if err := os.Remove(filePath); err != nil {
				fmt.Printf("❌ Failed to delete orphaned image %s: %v\n", filename, err)
			} else {
				fmt.Printf("✅ Deleted orphaned image: %s\n", filename)
				deletedFiles = append(deletedFiles, filename)
			}
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Orphaned images cleanup completed",
		"deleted_count": len(deletedFiles),
		"deleted_files": deletedFiles,
	})
}

// getAllReferencedImages gets all image filenames referenced in database
func (cc *ContentController) getAllReferencedImages() ([]string, error) {
	var images []string

	// Get images from services table
	serviceQuery := `SELECT UNNEST(images) as image FROM services WHERE images IS NOT NULL`
	rows, err := cc.DB.Query(serviceQuery)
	if err == nil {
		defer rows.Close()
		for rows.Next() {
			var image string
			if err := rows.Scan(&image); err == nil && image != "" {
				images = append(images, image)
			}
		}
	}

	// Get images from gallery table
	galleryQuery := `SELECT image_url FROM gallery WHERE image_url IS NOT NULL AND image_url != ''`
	rows, err = cc.DB.Query(galleryQuery)
	if err == nil {
		defer rows.Close()
		for rows.Next() {
			var imageURL string
			if err := rows.Scan(&imageURL); err == nil && imageURL != "" {
				filename := filepath.Base(imageURL)
				if strings.HasPrefix(imageURL, "/assets/uploads/") {
					filename = strings.TrimPrefix(imageURL, "/assets/uploads/")
				}
				images = append(images, filename)
			}
		}
	}

	return images, nil
}

// Helper function to validate image file types
func isValidImageType(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	validExts := []string{".jpg", ".jpeg", ".png", ".gif", ".webp"}

	for _, validExt := range validExts {
		if ext == validExt {
			return true
		}
	}
	return false
}