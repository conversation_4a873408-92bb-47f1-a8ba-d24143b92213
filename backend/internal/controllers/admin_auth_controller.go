package controllers

import (
	"crypto/rand"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"net/http"
	"os"
	"phongnha-valley/backend/internal/models"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/jmoiron/sqlx"
	"golang.org/x/crypto/bcrypt"
)

type AdminAuthController struct {
	DB *sqlx.DB
}

func NewAdminAuthController(db *sqlx.DB) *AdminAuthController {
	return &AdminAuthController{DB: db}
}

type AdminLoginRequest struct {
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required"`
}

type AdminAuthResponse struct {
	Token        string      `json:"token"`
	RefreshToken string      `json:"refresh_token"`
	User         models.User `json:"user"`
	ExpiresAt    time.Time   `json:"expires_at"`
}

type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" binding:"required"`
}

type ChangePasswordRequest struct {
	CurrentPassword string `json:"current_password" binding:"required"`
	NewPassword     string `json:"new_password" binding:"required,min=6"`
}

type CreateUserRequest struct {
	Email     string `json:"email" binding:"required,email"`
	Password  string `json:"password" binding:"required,min=6"`
	FirstName string `json:"first_name"`
	LastName  string `json:"last_name"`
	Phone     string `json:"phone"`
	Role      string `json:"role" binding:"required"`
}

type UpdateUserRequest struct {
	Email     *string `json:"email,omitempty"`
	Password  *string `json:"password,omitempty"`
	FirstName *string `json:"first_name,omitempty"`
	LastName  *string `json:"last_name,omitempty"`
	Phone     *string `json:"phone,omitempty"`
	Role      *string `json:"role,omitempty"`
}

// AdminLogin handles admin authentication with enhanced security
func (aac *AdminAuthController) AdminLogin(c *gin.Context) {
	var req AdminLoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get user from database and verify admin or booking_manager role
	var user models.User
	query := `
		SELECT id, email, password, first_name, last_name, phone, role, created_at
		FROM users WHERE email = $1 AND (role = 'admin' OR role = 'booking_manager')`

	if err := aac.DB.Get(&user, query, req.Email); err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid credentials"})
		return
	}

	// Check password
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(req.Password)); err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid credentials"})
		return
	}

	// Generate JWT token
	token, expiresAt, err := aac.generateAdminToken(user.ID, user.Email, user.Role)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate token"})
		return
	}

	// Generate refresh token
	refreshToken, err := aac.generateRefreshToken(user.ID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate refresh token"})
		return
	}

	// Store refresh token in database
	if err := aac.storeRefreshToken(user.ID, refreshToken); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to store refresh token"})
		return
	}

	// Remove password from response
	user.Password = ""

	c.JSON(http.StatusOK, AdminAuthResponse{
		Token:        token,
		RefreshToken: refreshToken,
		User:         user,
		ExpiresAt:    expiresAt,
	})
}

// RefreshAdminToken handles token refresh for admin users
func (aac *AdminAuthController) RefreshAdminToken(c *gin.Context) {
	var req RefreshTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate refresh token
	userID, err := aac.validateRefreshToken(req.RefreshToken)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid refresh token"})
		return
	}

	// Get user details
	var user models.User
	query := `SELECT id, email, first_name, last_name, phone, role, created_at FROM users WHERE id = $1 AND role = 'admin'`
	if err := aac.DB.Get(&user, query, userID); err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not found"})
		return
	}

	// Generate new token
	token, expiresAt, err := aac.generateAdminToken(user.ID, user.Email, user.Role)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate token"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"token":      token,
		"expires_at": expiresAt,
	})
}

// AdminLogout handles admin logout
func (aac *AdminAuthController) AdminLogout(c *gin.Context) {
	userID := c.GetString("user_id")

	// Remove all refresh tokens for this user
	query := "DELETE FROM refresh_tokens WHERE user_id = $1"
	aac.DB.Exec(query, userID)

	c.JSON(http.StatusOK, gin.H{"message": "Logged out successfully"})
}

// ChangePassword handles admin password change
func (aac *AdminAuthController) ChangePassword(c *gin.Context) {
	var req ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	userID := c.GetString("user_id")

	// Get current user from database
	var user models.User
	query := `SELECT id, email, password, role FROM users WHERE id = $1 AND (role = 'admin' OR role = 'booking_manager')`
	if err := aac.DB.Get(&user, query, userID); err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	// Verify current password
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(req.CurrentPassword)); err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Current password is incorrect"})
		return
	}

	// Hash new password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.NewPassword), bcrypt.DefaultCost)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to hash new password"})
		return
	}

	// Update password in database
	updateQuery := `UPDATE users SET password = $1, updated_at = NOW() WHERE id = $2`
	if _, err := aac.DB.Exec(updateQuery, string(hashedPassword), userID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update password"})
		return
	}

	// Remove all refresh tokens for this user (force re-login)
	aac.DB.Exec("DELETE FROM refresh_tokens WHERE user_id = $1", userID)

	c.JSON(http.StatusOK, gin.H{"message": "Password changed successfully"})
}

// GetAdminProfile returns the current admin's profile
func (aac *AdminAuthController) GetAdminProfile(c *gin.Context) {
	userID := c.GetString("user_id")

	var user models.User
	query := `SELECT id, email, first_name, last_name, phone, role, created_at FROM users WHERE id = $1`
	if err := aac.DB.Get(&user, query, userID); err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	c.JSON(http.StatusOK, user)
}

// UpdateAdminProfile updates the current admin's profile
func (aac *AdminAuthController) UpdateAdminProfile(c *gin.Context) {
	userID := c.GetString("user_id")

	var req models.UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Build dynamic update query
	var setParts []string
	var args []interface{}
	argIndex := 1

	if req.FirstName != nil {
		setParts = append(setParts, "first_name = $"+string(rune('0'+argIndex)))
		args = append(args, *req.FirstName)
		argIndex++
	}
	if req.LastName != nil {
		setParts = append(setParts, "last_name = $"+string(rune('0'+argIndex)))
		args = append(args, *req.LastName)
		argIndex++
	}
	if req.Phone != nil {
		setParts = append(setParts, "phone = $"+string(rune('0'+argIndex)))
		args = append(args, *req.Phone)
		argIndex++
	}

	if len(setParts) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No fields to update"})
		return
	}

	// Add user ID as the last parameter
	args = append(args, userID)

	query := "UPDATE users SET " + strings.Join(setParts, ", ") + ", updated_at = CURRENT_TIMESTAMP WHERE id = $" + string(rune('0'+argIndex))

	_, err := aac.DB.Exec(query, args...)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update profile"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Profile updated successfully"})
}

// ListUsers returns all users (admin only)
func (aac *AdminAuthController) ListUsers(c *gin.Context) {
	var users []models.User
	query := `SELECT id, email, first_name, last_name, phone, role, created_at FROM users ORDER BY created_at DESC`

	if err := aac.DB.Select(&users, query); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch users"})
		return
	}

	c.JSON(http.StatusOK, users)
}

// GetUser returns a specific user by ID (admin only)
func (aac *AdminAuthController) GetUser(c *gin.Context) {
	userID := c.Param("id")

	var user models.User
	query := `SELECT id, email, first_name, last_name, phone, role, created_at FROM users WHERE id = $1`

	if err := aac.DB.Get(&user, query, userID); err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	c.JSON(http.StatusOK, user)
}

// CreateUser creates a new user (admin only)
func (aac *AdminAuthController) CreateUser(c *gin.Context) {
	var req CreateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate required fields
	if req.Email == "" || req.Password == "" || req.Role == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Email, password, and role are required"})
		return
	}

	// Validate role
	if req.Role != "admin" && req.Role != "booking_manager" && req.Role != "user" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid role. Must be admin, booking_manager, or user"})
		return
	}

	// Check if email already exists
	var existingUser models.User
	checkQuery := `SELECT id FROM users WHERE email = $1`
	if err := aac.DB.Get(&existingUser, checkQuery, req.Email); err == nil {
		c.JSON(http.StatusConflict, gin.H{"error": "Email already exists"})
		return
	}

	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to hash password"})
		return
	}

	// Create user
	userID := uuid.New().String()
	insertQuery := `
		INSERT INTO users (id, email, password, first_name, last_name, phone, role, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())
		RETURNING id, email, first_name, last_name, phone, role, created_at`

	var newUser models.User
	if err := aac.DB.Get(&newUser, insertQuery, userID, req.Email, string(hashedPassword),
		req.FirstName, req.LastName, req.Phone, req.Role); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create user"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "User created successfully",
		"user": newUser,
	})
}

// UpdateUser updates a user (admin only)
func (aac *AdminAuthController) UpdateUser(c *gin.Context) {
	userID := c.Param("id")

	var req UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Build dynamic update query
	var setParts []string
	var args []interface{}
	argIndex := 1

	if req.FirstName != nil {
		setParts = append(setParts, fmt.Sprintf("first_name = $%d", argIndex))
		args = append(args, *req.FirstName)
		argIndex++
	}

	if req.LastName != nil {
		setParts = append(setParts, fmt.Sprintf("last_name = $%d", argIndex))
		args = append(args, *req.LastName)
		argIndex++
	}

	if req.Email != nil {
		// Check if email already exists for another user
		var existingUser models.User
		checkQuery := `SELECT id FROM users WHERE email = $1 AND id != $2`
		if err := aac.DB.Get(&existingUser, checkQuery, *req.Email, userID); err == nil {
			c.JSON(http.StatusConflict, gin.H{"error": "Email already exists"})
			return
		}

		setParts = append(setParts, fmt.Sprintf("email = $%d", argIndex))
		args = append(args, *req.Email)
		argIndex++
	}

	if req.Phone != nil {
		setParts = append(setParts, fmt.Sprintf("phone = $%d", argIndex))
		args = append(args, *req.Phone)
		argIndex++
	}

	if req.Role != nil {
		// Validate role
		if *req.Role != "admin" && *req.Role != "booking_manager" && *req.Role != "user" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid role"})
			return
		}
		setParts = append(setParts, fmt.Sprintf("role = $%d", argIndex))
		args = append(args, *req.Role)
		argIndex++
	}

	if req.Password != nil {
		// Hash new password
		hashedPassword, err := bcrypt.GenerateFromPassword([]byte(*req.Password), bcrypt.DefaultCost)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to hash password"})
			return
		}
		setParts = append(setParts, fmt.Sprintf("password = $%d", argIndex))
		args = append(args, string(hashedPassword))
		argIndex++
	}

	if len(setParts) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No fields to update"})
		return
	}

	// Add updated_at
	setParts = append(setParts, fmt.Sprintf("updated_at = $%d", argIndex))
	args = append(args, time.Now())
	argIndex++

	// Add userID for WHERE clause
	args = append(args, userID)

	query := fmt.Sprintf("UPDATE users SET %s WHERE id = $%d", strings.Join(setParts, ", "), argIndex)

	result, err := aac.DB.Exec(query, args...)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update user"})
		return
	}

	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	// Return updated user
	var updatedUser models.User
	selectQuery := `SELECT id, email, first_name, last_name, phone, role, created_at FROM users WHERE id = $1`
	if err := aac.DB.Get(&updatedUser, selectQuery, userID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch updated user"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "User updated successfully",
		"user": updatedUser,
	})
}

// DeleteUser deletes a user (admin only)
func (aac *AdminAuthController) DeleteUser(c *gin.Context) {
	userID := c.Param("id")

	query := "DELETE FROM users WHERE id = $1 AND role != 'admin'"
	result, err := aac.DB.Exec(query, userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete user"})
		return
	}

	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found or cannot delete admin"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "User deleted successfully"})
}

// GetDashboardStats returns dashboard statistics (admin only)
func (aac *AdminAuthController) GetDashboardStats(c *gin.Context) {
	var totalUsers, totalServices, totalBookings, recentBookings int

	// Get total users
	aac.DB.Get(&totalUsers, "SELECT COUNT(*) FROM users WHERE role = 'customer'")

	// Get total services
	aac.DB.Get(&totalServices, "SELECT COUNT(*) FROM services WHERE is_active = true")

	// Get total bookings
	aac.DB.Get(&totalBookings, "SELECT COUNT(*) FROM bookings")

	// Get recent bookings (last 7 days)
	aac.DB.Get(&recentBookings, "SELECT COUNT(*) FROM bookings WHERE created_at >= NOW() - INTERVAL '7 days'")

	stats := gin.H{
		"total_users":    totalUsers,
		"total_services": totalServices,
		"total_bookings": totalBookings,
		"recent_bookings": recentBookings,
	}

	c.JSON(http.StatusOK, stats)
}

// Helper methods

// generateAdminToken creates a JWT token for admin users with shorter expiry
func (aac *AdminAuthController) generateAdminToken(userID uuid.UUID, email, role string) (string, time.Time, error) {
	expiresAt := time.Now().Add(2 * time.Hour) // Shorter expiry for admin tokens

	claims := jwt.MapClaims{
		"user_id": userID.String(),
		"email":   email,
		"role":    role,
		"exp":     expiresAt.Unix(),
		"iat":     time.Now().Unix(),
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	secret := os.Getenv("JWT_SECRET")
	if secret == "" {
		secret = "your-secret-key" // Default for development
	}

	tokenString, err := token.SignedString([]byte(secret))
	return tokenString, expiresAt, err
}

// generateRefreshToken creates a secure refresh token
func (aac *AdminAuthController) generateRefreshToken(userID uuid.UUID) (string, error) {
	// Generate random bytes
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}

	// Create hash
	hash := sha256.Sum256(bytes)
	return hex.EncodeToString(hash[:]), nil
}

// storeRefreshToken stores the refresh token in the database
func (aac *AdminAuthController) storeRefreshToken(userID uuid.UUID, token string) error {
	// First, remove any existing refresh tokens for this user
	deleteQuery := "DELETE FROM refresh_tokens WHERE user_id = $1"
	aac.DB.Exec(deleteQuery, userID)

	// Insert new refresh token
	insertQuery := `
		INSERT INTO refresh_tokens (user_id, token, expires_at)
		VALUES ($1, $2, $3)`

	expiresAt := time.Now().Add(7 * 24 * time.Hour) // 7 days
	_, err := aac.DB.Exec(insertQuery, userID, token, expiresAt)
	return err
}

// validateRefreshToken validates a refresh token and returns the user ID
func (aac *AdminAuthController) validateRefreshToken(token string) (uuid.UUID, error) {
	var userID uuid.UUID
	query := `
		SELECT user_id FROM refresh_tokens
		WHERE token = $1 AND expires_at > NOW()`

	err := aac.DB.Get(&userID, query, token)
	return userID, err
}