package controllers

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"phongnha-valley/backend/internal/models"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/jmoiron/sqlx"
)

type ServiceController struct {
	DB *sqlx.DB
}

func NewServiceController(db *sqlx.DB) *ServiceController {
	return &ServiceController{DB: db}
}

// ListServices retrieves all active services with their details (public endpoint)
func (sc *ServiceController) ListServices(c *gin.Context) {
	// Create a simplified struct for database scanning
	type ServiceRow struct {
		ID             string  `db:"id"`
		Type           string  `db:"type"`
		Name           string  `db:"name"`
		Description    *string `db:"description"`
		Price          float64 `db:"price"`
		ChildPrice     *float64 `db:"child_price"`
		IsActive       bool    `db:"is_active"`
		Capacity       int     `db:"capacity"`
		OpenTime       string  `db:"open_time"`
		CloseTime      string  `db:"close_time"`
		Images         string  `db:"images"`
		CreatedAt      string  `db:"created_at"`
		Inclusions     string  `db:"inclusions"`
		Requirements   string  `db:"requirements"`
		AgeRestriction string  `db:"age_restriction"`
		BookingPolicy  string  `db:"booking_policy"`
	}

	var rows []ServiceRow

	// Simplified query with string conversion for UUID
	query := `
		SELECT
			s.id::text as id, s.type, s.name, s.description, s.price, s.child_price,
			s.is_active, s.capacity, s.open_time, s.close_time,
			COALESCE(array_to_json(s.images)::text, '[]') as images,
			s.created_at::text as created_at,
			COALESCE(array_to_json(s.inclusions)::text, '[]') as inclusions,
			COALESCE(array_to_json(s.requirements)::text, '[]') as requirements,
			COALESCE(s.age_restriction::text, '{}') as age_restriction,
			COALESCE(s.booking_policy::text, '{}') as booking_policy
		FROM services s
		WHERE s.is_active = true
		ORDER BY s.name ASC, s.id ASC`

	err := sc.DB.Select(&rows, query)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to fetch services",
			"details": err.Error(),
		})
		return
	}

	// Convert rows to response format
	services := make([]map[string]interface{}, len(rows))
	for i, row := range rows {
		// Parse images array from JSON string
		var images []string
		if err := json.Unmarshal([]byte(row.Images), &images); err != nil {
			images = []string{} // Default to empty array if parsing fails
		}

		// Get first image for backward compatibility
		var firstImage *string
		if len(images) > 0 {
			firstImage = &images[0]
		}

		services[i] = map[string]interface{}{
			"id":              row.ID,
			"type":            row.Type,
			"name":            row.Name,
			"description":     row.Description,
			"price":           row.Price,
			"child_price":     row.ChildPrice,
			"is_active":       row.IsActive,
			"capacity":        row.Capacity,
			"open_time":       row.OpenTime,
			"close_time":      row.CloseTime,
			"images":          images,
			"image":           firstImage, // For backward compatibility
			"created_at":      row.CreatedAt,
			"inclusions":      row.Inclusions,
			"requirements":    row.Requirements,
			"age_restriction": row.AgeRestriction,
			"booking_policy":  row.BookingPolicy,
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"services": services,
		"count":    len(services),
	})
}

// ListAllServices retrieves all services (both active and inactive) for admin
func (sc *ServiceController) ListAllServices(c *gin.Context) {
	// Create a simplified struct for database scanning
	type ServiceRow struct {
		ID             string  `db:"id"`
		Type           string  `db:"type"`
		Name           string  `db:"name"`
		Description    *string `db:"description"`
		Price          float64 `db:"price"`
		ChildPrice     *float64 `db:"child_price"`
		IsActive       bool    `db:"is_active"`
		Capacity       int     `db:"capacity"`
		OpenTime       string  `db:"open_time"`
		CloseTime      string  `db:"close_time"`
		Images         string  `db:"images"`
		CreatedAt      string  `db:"created_at"`
		Inclusions     string  `db:"inclusions"`
		Requirements   string  `db:"requirements"`
		AgeRestriction string  `db:"age_restriction"`
		BookingPolicy  string  `db:"booking_policy"`
	}

	var rows []ServiceRow

	// Query to get ALL services (both active and inactive) for admin
	query := `
		SELECT
			s.id::text as id, s.type, s.name, s.description, s.price, s.child_price,
			s.is_active, s.capacity, s.open_time, s.close_time,
			COALESCE(array_to_json(s.images)::text, '[]') as images,
			s.created_at::text as created_at,
			COALESCE(array_to_json(s.inclusions)::text, '[]') as inclusions,
			COALESCE(array_to_json(s.requirements)::text, '[]') as requirements,
			COALESCE(s.age_restriction::text, '{}') as age_restriction,
			COALESCE(s.booking_policy::text, '{}') as booking_policy
		FROM services s
		ORDER BY s.name ASC, s.id ASC`

	if err := sc.DB.Select(&rows, query); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch services"})
		return
	}

	// Convert rows to response format
	services := make([]map[string]interface{}, len(rows))
	for i, row := range rows {
		// Parse images array from JSON string
		var images []string
		if err := json.Unmarshal([]byte(row.Images), &images); err != nil {
			images = []string{} // Default to empty array if parsing fails
		}

		// Get first image for backward compatibility
		var firstImage *string
		if len(images) > 0 {
			firstImage = &images[0]
		}

		services[i] = map[string]interface{}{
			"id":              row.ID,
			"type":            row.Type,
			"name":            row.Name,
			"description":     row.Description,
			"price":           row.Price,
			"child_price":     row.ChildPrice,
			"is_active":       row.IsActive,
			"capacity":        row.Capacity,
			"open_time":       row.OpenTime,
			"close_time":      row.CloseTime,
			"images":          images,
			"image":           firstImage, // For backward compatibility
			"created_at":      row.CreatedAt,
			"inclusions":      row.Inclusions,
			"requirements":    row.Requirements,
			"age_restriction": row.AgeRestriction,
			"booking_policy":  row.BookingPolicy,
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"services": services,
		"count":    len(services),
	})
}

// GetService retrieves a specific service by ID
func (sc *ServiceController) GetService(c *gin.Context) {
	serviceID := c.Param("id")

	// Validate UUID
	if _, err := uuid.Parse(serviceID); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid service ID"})
		return
	}

	// Use the same simplified struct as ListServices for consistency
	type ServiceRow struct {
		ID             string  `db:"id"`
		Type           string  `db:"type"`
		Name           string  `db:"name"`
		Description    *string `db:"description"`
		Price          float64 `db:"price"`
		ChildPrice     *float64 `db:"child_price"`
		IsActive       bool    `db:"is_active"`
		Capacity       int     `db:"capacity"`
		OpenTime       string  `db:"open_time"`
		CloseTime      string  `db:"close_time"`
		Images         string  `db:"images"`
		CreatedAt      string  `db:"created_at"`
		Inclusions     string  `db:"inclusions"`
		Requirements   string  `db:"requirements"`
		AgeRestriction string  `db:"age_restriction"`
		BookingPolicy  string  `db:"booking_policy"`
	}

	var row ServiceRow

	query := `
		SELECT
			s.id::text as id, s.type, s.name, s.description, s.price, s.child_price,
			s.is_active, s.capacity, s.open_time, s.close_time,
			COALESCE(array_to_json(s.images)::text, '[]') as images,
			s.created_at::text as created_at,
			COALESCE(array_to_json(s.inclusions)::text, '[]') as inclusions,
			COALESCE(array_to_json(s.requirements)::text, '[]') as requirements,
			COALESCE(s.age_restriction::text, '{}') as age_restriction,
			COALESCE(s.booking_policy::text, '{}') as booking_policy
		FROM services s
		WHERE s.id = $1 AND s.is_active = true`

	if err := sc.DB.Get(&row, query, serviceID); err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Service not found"})
		return
	}

	// Convert to response format (same as ListServices)
	// Parse images array from JSON string
	var images []string
	if err := json.Unmarshal([]byte(row.Images), &images); err != nil {
		images = []string{} // Default to empty array if parsing fails
	}

	// Get first image for backward compatibility
	var firstImage *string
	if len(images) > 0 {
		firstImage = &images[0]
	}

	service := map[string]interface{}{
		"id":              row.ID,
		"type":            row.Type,
		"name":            row.Name,
		"description":     row.Description,
		"price":           row.Price,
		"child_price":     row.ChildPrice,
		"is_active":       row.IsActive,
		"capacity":        row.Capacity,
		"open_time":       row.OpenTime,
		"close_time":      row.CloseTime,
		"images":          images,
		"image":           firstImage, // For backward compatibility
		"created_at":      row.CreatedAt,
		"inclusions":      row.Inclusions,
		"requirements":    row.Requirements,
		"age_restriction": row.AgeRestriction,
		"booking_policy":  row.BookingPolicy,
	}

	c.JSON(http.StatusOK, service)
}

// CreateService creates a new service (admin only)
func (sc *ServiceController) CreateService(c *gin.Context) {
	var req models.CreateServiceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Validate required fields
	if req.Name == "" || req.Type == "" || req.Price <= 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Name, type, and price are required"})
		return
	}

	// Prepare service details
	var inclusions, requirements models.StringArray
	var ageRestriction, bookingPolicy interface{}

	if req.Details != nil {
		inclusions = req.Details.Inclusions
		requirements = req.Details.Requirements
		ageRestriction = req.Details.AgeRestriction
		bookingPolicy = req.Details.BookingPolicy
	} else {
		inclusions = models.StringArray{}
		requirements = models.StringArray{}
		ageRestriction = "{}"
		bookingPolicy = "{}"
	}

	// Insert service with all details in one table
	serviceID := uuid.New()
	serviceQuery := `
		INSERT INTO services (id, type, name, description, price, child_price, capacity, open_time, close_time, images, inclusions, requirements, age_restriction, booking_policy)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)`

	_, err := sc.DB.Exec(serviceQuery, serviceID, req.Type, req.Name, req.Description,
		req.Price, req.ChildPrice, req.Capacity, req.OpenTime, req.CloseTime, req.Images,
		inclusions, requirements, ageRestriction, bookingPolicy)
	if err != nil {
		log.Printf("Error creating service: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create service", "details": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{"id": serviceID, "message": "Service created successfully"})
}

// UpdateService updates an existing service (admin only)
func (sc *ServiceController) UpdateService(c *gin.Context) {
	serviceID := c.Param("id")

	// Validate UUID
	if _, err := uuid.Parse(serviceID); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid service ID"})
		return
	}

	var req models.UpdateServiceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Build dynamic update query
	var setParts []string
	var args []interface{}
	argIndex := 1

	if req.Name != nil {
		setParts = append(setParts, fmt.Sprintf("name = $%d", argIndex))
		args = append(args, *req.Name)
		argIndex++
	}
	if req.Description != nil {
		setParts = append(setParts, fmt.Sprintf("description = $%d", argIndex))
		args = append(args, *req.Description)
		argIndex++
	}
	if req.Price != nil {
		setParts = append(setParts, fmt.Sprintf("price = $%d", argIndex))
		args = append(args, *req.Price)
		argIndex++
	}
	if req.ChildPrice != nil {
		setParts = append(setParts, fmt.Sprintf("child_price = $%d", argIndex))
		args = append(args, *req.ChildPrice)
		argIndex++
	}
	if req.IsActive != nil {
		setParts = append(setParts, fmt.Sprintf("is_active = $%d", argIndex))
		args = append(args, *req.IsActive)
		argIndex++
	}
	if req.Capacity != nil {
		setParts = append(setParts, fmt.Sprintf("capacity = $%d", argIndex))
		args = append(args, *req.Capacity)
		argIndex++
	}
	if req.OpenTime != nil {
		setParts = append(setParts, fmt.Sprintf("open_time = $%d", argIndex))
		args = append(args, *req.OpenTime)
		argIndex++
	}
	if req.CloseTime != nil {
		setParts = append(setParts, fmt.Sprintf("close_time = $%d", argIndex))
		args = append(args, *req.CloseTime)
		argIndex++
	}
	if req.Images != nil {
		setParts = append(setParts, fmt.Sprintf("images = $%d", argIndex))
		args = append(args, req.Images)
		argIndex++
	}
	if req.Inclusions != nil {
		setParts = append(setParts, fmt.Sprintf("inclusions = $%d", argIndex))
		args = append(args, req.Inclusions)
		argIndex++
	}
	if req.Requirements != nil {
		setParts = append(setParts, fmt.Sprintf("requirements = $%d", argIndex))
		args = append(args, req.Requirements)
		argIndex++
	}

	if len(setParts) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No fields to update"})
		return
	}

	// Add service ID as the last parameter
	args = append(args, serviceID)

	query := "UPDATE services SET " + strings.Join(setParts, ", ") + " WHERE id = $" + fmt.Sprintf("%d", argIndex)

	_, err := sc.DB.Exec(query, args...)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update service"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Service updated successfully"})
}

// DeleteService permanently deletes a service (admin only)
func (sc *ServiceController) DeleteService(c *gin.Context) {
	serviceID := c.Param("id")

	// Validate UUID
	if _, err := uuid.Parse(serviceID); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid service ID"})
		return
	}

	// Check if there are any bookings for this service
	var bookingCount int
	err := sc.DB.Get(&bookingCount, "SELECT COUNT(*) FROM bookings WHERE service_id = $1", serviceID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check service bookings"})
		return
	}

	if bookingCount > 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Cannot delete service with existing bookings. Please cancel all bookings first."})
		return
	}

	// Get service images before deletion for cleanup
	var serviceImages []string
	err = sc.DB.Get(&serviceImages, "SELECT COALESCE(images, '{}') FROM services WHERE id = $1", serviceID)
	if err != nil {
		fmt.Printf("⚠️ DELETE Service - Failed to get service images: %v\n", err)
		// Continue with deletion even if we can't get images
	}

	// Start transaction to ensure data consistency
	tx, err := sc.DB.Beginx()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to start transaction"})
		return
	}
	defer tx.Rollback()

	// First, delete related service_details
	_, err = tx.Exec("DELETE FROM service_details WHERE service_id = $1", serviceID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete service details"})
		return
	}

	// Then, delete the service itself
	result, err := tx.Exec("DELETE FROM services WHERE id = $1", serviceID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete service"})
		return
	}

	rowsAffected, _ := result.RowsAffected()
	if rowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "Service not found"})
		return
	}

	// Commit transaction
	if err := tx.Commit(); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to commit transaction"})
		return
	}

	// Delete associated image files after successful database deletion
	if len(serviceImages) > 0 {
		sc.deleteServiceImages(serviceImages)
	}

	fmt.Printf("✅ DELETE Service - Successfully deleted service ID: %s\n", serviceID)
	c.JSON(http.StatusOK, gin.H{"message": "Service deleted successfully"})
}

// deleteServiceImages deletes image files from storage
func (sc *ServiceController) deleteServiceImages(images []string) {
	workingDir, err := os.Getwd()
	if err != nil {
		fmt.Printf("❌ DELETE Service Images - Failed to get working directory: %v\n", err)
		return
	}

	uploadDir := filepath.Join(workingDir, "frontend", "public", "assets", "uploads")

	for _, imageName := range images {
		if imageName == "" || strings.Contains(imageName, "placeholder") {
			continue
		}

		// Extract filename if it's a path
		filename := filepath.Base(imageName)
		filePath := filepath.Join(uploadDir, filename)

		// Check if file exists and delete
		if _, err := os.Stat(filePath); err == nil {
			if err := os.Remove(filePath); err != nil {
				fmt.Printf("❌ DELETE Service Images - Failed to delete %s: %v\n", filename, err)
			} else {
				fmt.Printf("✅ DELETE Service Images - Successfully deleted: %s\n", filename)
			}
		} else {
			fmt.Printf("⚠️ DELETE Service Images - File not found: %s\n", filename)
		}
	}
}