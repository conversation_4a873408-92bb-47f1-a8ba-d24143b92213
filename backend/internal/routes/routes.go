package routes

import (
	"net/http"
	"phongnha-valley/backend/internal/controllers"
	"phongnha-valley/backend/internal/middleware"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/jmoiron/sqlx"
)

func SetupRoutes(router *gin.Engine, db *sqlx.DB) {
	// Add security headers middleware
	router.Use(middleware.SecurityHeadersMiddleware())

	// Health check endpoint (always available)
	router.GET("/health", func(c *gin.Context) {
		status := "ok"
		dbStatus := "connected"
		if db == nil {
			dbStatus = "disconnected"
		}
		c.JSON(http.StatusOK, gin.H{
			"status": status,
			"database": dbStatus,
		})
	})

	// If database is not available, only serve static files and health check
	if db == nil {
		setupStaticRoutes(router)
		return
	}

	// Initialize controllers (only if database is available)
	authController := controllers.NewAuthController(db)
	adminAuthController := controllers.NewAdminAuthController(db)
	serviceController := controllers.NewServiceController(db)
	bookingController := controllers.NewBookingController(db)
	contentController := controllers.NewContentController(db)
	settingsController := controllers.NewSettingsController(db)
	menuController := controllers.NewMenuController(db)

	// API routes FIRST (before static files)
	api := router.Group("/api/v1")



	// Debug endpoint to test database connection
	api.GET("/debug/simple", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status": "debug endpoint working",
			"message": "This is a simple debug endpoint",
		})
	})

	// Debug endpoint to test database
	api.GET("/debug/db", func(c *gin.Context) {
		// Test database connection
		err := db.Ping()
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Database connection failed",
				"details": err.Error(),
			})
			return
		}

		// Test simple query
		var count int
		err = db.Get(&count, "SELECT COUNT(*) FROM services")
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Database query failed",
				"details": err.Error(),
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"status": "ok",
			"services_count": count,
		})
	})
	{
		// Public routes
		public := api.Group("")
		{
			// Auth routes
			auth := public.Group("/auth")
			{
				auth.POST("/register", authController.Register)
				auth.POST("/login", authController.Login)
			}

			// Public service routes
			services := public.Group("/services")
			{
				services.GET("/", serviceController.ListServices)
				services.GET("/:id", serviceController.GetService)
			}

			// Public content routes
			content := public.Group("/content")
			{
				content.GET("/articles", contentController.ListArticles)
				content.GET("/articles/:id", contentController.GetArticle)
				content.GET("/gallery", contentController.ListGalleryItems)
				content.GET("/homepage/:section", contentController.GetHomepageContent)
				content.GET("/:key", contentController.GetContentBlock)
			}

			// Public menu routes
			menus := public.Group("/menus")
			{
				menus.GET("/", menuController.ListMenus)
				menus.GET("/:id", menuController.GetMenu)
			}

			// Public settings routes
			settings := public.Group("/settings")
			{
				settings.GET("/", settingsController.GetSettings)
				settings.GET("/:key", settingsController.GetSetting)
			}

			// Public booking routes
			bookings := public.Group("/bookings")
			{
				bookings.POST("/", bookingController.CreateBooking)
				bookings.GET("/:id", bookingController.GetBooking)
			}
		}

		// Protected routes (require authentication)
		protected := api.Group("")
		protected.Use(middleware.AuthMiddleware())
		{
			// User booking routes
			bookings := protected.Group("/bookings")
			{
				bookings.GET("/", bookingController.ListUserBookings)
				bookings.PUT("/:id", bookingController.UpdateBooking)
				bookings.DELETE("/:id", bookingController.CancelBooking)
			}
		}

		// Admin routes
		admin := api.Group("/admin")
		{
			// Admin auth routes (no middleware)
			admin.POST("/login", adminAuthController.AdminLogin)
			admin.POST("/refresh", adminAuthController.RefreshAdminToken)

			// Protected admin routes
			adminProtected := admin.Group("")
			adminProtected.Use(middleware.AdminAuthMiddleware())
			{
				// Admin service management
				services := adminProtected.Group("/services")
				{
					services.GET("/", serviceController.ListAllServices)
					services.POST("/", serviceController.CreateService)
					services.GET("/:id", serviceController.GetService)
					services.PUT("/:id", serviceController.UpdateService)
					services.DELETE("/:id", serviceController.DeleteService)
				}

				// Admin booking management
				bookings := adminProtected.Group("/bookings")
				{
					bookings.GET("/", bookingController.ListAllBookings)
					bookings.GET("/:id", bookingController.GetBooking)
					bookings.PUT("/:id", bookingController.UpdateBooking)
					bookings.PUT("/:id/cancel", bookingController.CancelBooking)
					bookings.DELETE("/:id", bookingController.DeleteBooking)
				}

				// Admin content management
				content := adminProtected.Group("/content")
				{
					content.GET("/articles", contentController.ListArticles)
					content.POST("/articles", contentController.CreateArticle)
					content.GET("/articles/:id", contentController.GetArticle)
					content.PUT("/articles/:id", contentController.UpdateArticle)
					content.DELETE("/articles/:id", contentController.DeleteArticle)

					content.GET("/gallery", contentController.ListGalleryItems)
					content.POST("/gallery", contentController.CreateGalleryItem)
					content.GET("/gallery/:id", contentController.GetGalleryItem)
					content.PUT("/gallery/:id", contentController.UpdateGalleryItem)
					content.DELETE("/gallery/:id", contentController.DeleteGalleryItem)

					// Homepage content management
					content.GET("/homepage/:section", contentController.GetHomepageContent)
					content.PUT("/homepage/:section", contentController.UpdateHomepageContent)

					// File upload and delete endpoints
					content.POST("/upload", contentController.UploadImages)
					content.DELETE("/delete-image", contentController.DeleteImage)
					content.POST("/cleanup-orphaned-images", contentController.CleanupOrphanedImages)
				}

				// Admin user management
				users := adminProtected.Group("/users")
				{
					users.GET("/", adminAuthController.ListUsers)
					users.POST("/", adminAuthController.CreateUser)
					users.GET("/:id", adminAuthController.GetUser)
					users.PUT("/:id", adminAuthController.UpdateUser)
					users.DELETE("/:id", adminAuthController.DeleteUser)
				}

				// Admin dashboard
				dashboard := adminProtected.Group("/dashboard")
				{
					dashboard.GET("/stats", adminAuthController.GetDashboardStats)
				}

				// Admin settings management
				settings := adminProtected.Group("/settings")
				{
					settings.GET("/", settingsController.GetSettings)
					settings.PUT("/", settingsController.UpdateSettings)
					settings.POST("/batch", settingsController.BatchUpdateSettings)
					settings.GET("/:key", settingsController.GetSetting)
					settings.POST("/reset", settingsController.ResetSettings)
				}

				// Admin menu management
				menus := adminProtected.Group("/menus")
				{
					menus.GET("/", menuController.ListMenus)
					menus.POST("/", menuController.CreateMenu)
					menus.GET("/:id", menuController.GetMenu)
					menus.PUT("/:id", menuController.UpdateMenu)
					menus.DELETE("/:id", menuController.DeleteMenu)
					menus.POST("/upload", menuController.UploadMenuImages)
				}

				// Admin session management
				adminProtected.POST("/logout", adminAuthController.AdminLogout)
				adminProtected.GET("/profile", adminAuthController.GetAdminProfile)
				adminProtected.PUT("/profile", adminAuthController.UpdateAdminProfile)
				adminProtected.POST("/change-password", adminAuthController.ChangePassword)
			}
		}
	}

	// Serve static assets
	router.Static("/assets", "./frontend/public/assets")

	// Serve font files
	router.Static("/font", "./frontend/public/font")

	// Serve logo files
	router.Static("/logo", "./frontend/public/logo")

	// Admin panel routes
	router.GET("/admin", func(c *gin.Context) {
		c.File("./frontend/public/admin/index.html")
	})
	router.GET("/admin/", func(c *gin.Context) {
		c.File("./frontend/public/admin/index.html")
	})

	// Explicitly handle root path
	router.GET("/", func(c *gin.Context) {
		c.File("./frontend/public/index.html")
	})

	// About page routes
	router.GET("/about", func(c *gin.Context) {
		c.File("./frontend/public/about.html")
	})
	router.GET("/about.html", func(c *gin.Context) {
		c.File("./frontend/public/about.html")
	})

	// Service page routes
	router.GET("/service", func(c *gin.Context) {
		c.File("./frontend/public/service.html")
	})
	router.GET("/service.html", func(c *gin.Context) {
		c.File("./frontend/public/service.html")
	})

	// Services page routes
	router.GET("/services", func(c *gin.Context) {
		c.File("./frontend/public/services.html")
	})
	router.GET("/services.html", func(c *gin.Context) {
		c.File("./frontend/public/services.html")
	})

	// Contact page routes
	router.GET("/contact", func(c *gin.Context) {
		c.File("./frontend/public/contact.html")
	})
	router.GET("/contact.html", func(c *gin.Context) {
		c.File("./frontend/public/contact.html")
	})

	// Gallery page routes
	router.GET("/gallery", func(c *gin.Context) {
		c.File("./frontend/public/gallery.html")
	})
	router.GET("/gallery.html", func(c *gin.Context) {
		c.File("./frontend/public/gallery.html")
	})

	// Menu page routes
	router.GET("/menu", func(c *gin.Context) {
		c.File("./frontend/public/menu.html")
	})
	router.GET("/menu.html", func(c *gin.Context) {
		c.File("./frontend/public/menu.html")
	})

	// Booking Manager page routes
	router.GET("/booking-manager", func(c *gin.Context) {
		c.File("./frontend/public/booking-manager.html")
	})
	router.GET("/booking-manager.html", func(c *gin.Context) {
		c.File("./frontend/public/booking-manager.html")
	})



	// Catch-all route for SPA (only for non-API routes)
	router.NoRoute(func(c *gin.Context) {
		// If it's an API request, return 404 JSON
		if strings.HasPrefix(c.Request.URL.Path, "/api/") {
			c.JSON(404, gin.H{"error": "API endpoint not found"})
			return
		}
		// If it's an admin request, serve admin panel
		if strings.HasPrefix(c.Request.URL.Path, "/admin") {
			c.File("./frontend/public/admin/index.html")
			return
		}
		// Otherwise serve the main SPA
		c.File("./frontend/public/index.html")
	})
}

// setupStaticRoutes sets up only static file serving when database is unavailable
func setupStaticRoutes(router *gin.Engine) {
	// Serve static files
	router.Static("/static", "./frontend/public/static")
	router.Static("/assets", "./frontend/public/assets")
	router.Static("/uploads", "./uploads")
	router.Static("/admin", "./frontend/public/admin")

	// Serve main pages
	router.GET("/", func(c *gin.Context) {
		c.File("./frontend/public/index.html")
	})

	router.GET("/about", func(c *gin.Context) {
		c.File("./frontend/public/about.html")
	})

	router.GET("/contact", func(c *gin.Context) {
		c.File("./frontend/public/contact.html")
	})

	// Admin panel routes (avoid conflict with existing routes)
	router.GET("/admin", func(c *gin.Context) {
		c.File("./frontend/public/admin/index.html")
	})

	// API endpoints return database unavailable error
	api := router.Group("/api/v1")
	api.GET("/*path", func(c *gin.Context) {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "Database unavailable",
			"message": "The database connection is not available. Please try again later.",
		})
	})

	api.POST("/*path", func(c *gin.Context) {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "Database unavailable",
			"message": "The database connection is not available. Please try again later.",
		})
	})

	api.PUT("/*path", func(c *gin.Context) {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "Database unavailable",
			"message": "The database connection is not available. Please try again later.",
		})
	})

	api.DELETE("/*path", func(c *gin.Context) {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "Database unavailable",
			"message": "The database connection is not available. Please try again later.",
		})
	})

	// Catch-all route for SPA
	router.NoRoute(func(c *gin.Context) {
		// If it's an API request, return database unavailable
		if strings.HasPrefix(c.Request.URL.Path, "/api/") {
			c.JSON(http.StatusServiceUnavailable, gin.H{
				"error": "Database unavailable",
				"message": "The database connection is not available. Please try again later.",
			})
			return
		}
		// If it's an admin request, serve admin panel
		if strings.HasPrefix(c.Request.URL.Path, "/admin") {
			c.File("./frontend/public/admin/index.html")
			return
		}
		// Otherwise serve the main SPA
		c.File("./frontend/public/index.html")
	})
}