package models

import (
	"time"

	"github.com/google/uuid"
	"github.com/lib/pq"
)

// Article represents a blog article or news item
type Article struct {
	ID          uuid.UUID      `json:"id" db:"id"`
	Title       string         `json:"title" db:"title"`
	Content     string         `json:"content" db:"content"`
	Summary     *string        `json:"summary" db:"summary"`
	Images      pq.StringArray `json:"images" db:"images"`
	Tags        pq.StringArray `json:"tags" db:"tags"`
	IsPublished bool           `json:"is_published" db:"is_published"`
	AuthorID    uuid.UUID      `json:"author_id" db:"author_id"`
	CreatedAt   time.Time      `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at" db:"updated_at"`
}

// GalleryItem represents an item in the photo gallery
type GalleryItem struct {
	ID          uuid.UUID `json:"id" db:"id"`
	Title       string    `json:"title" db:"title"`
	Description *string   `json:"description" db:"description"`
	ImageURL    string    `json:"image_url" db:"image_url"`
	Category    string    `json:"category" db:"category"`
	IsActive    bool      `json:"is_active" db:"is_active"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
}

// CreateArticleRequest represents the request payload for creating an article
type CreateArticleRequest struct {
	Title    string         `json:"title" binding:"required"`
	Content  string         `json:"content" binding:"required"`
	Summary  *string        `json:"summary"`
	Images   pq.StringArray `json:"images"`
	Tags     pq.StringArray `json:"tags"`
}

// UpdateArticleRequest represents the request payload for updating an article
type UpdateArticleRequest struct {
	Title       *string        `json:"title"`
	Content     *string        `json:"content"`
	Summary     *string        `json:"summary"`
	Images      *pq.StringArray `json:"images"`
	Tags        *pq.StringArray `json:"tags"`
	IsPublished *bool          `json:"is_published"`
}

// CreateGalleryItemRequest represents the request payload for creating a gallery item
type CreateGalleryItemRequest struct {
	Title       string  `json:"title" binding:"required"`
	Description *string `json:"description"`
	ImageURL    string  `json:"image_url" binding:"required"`
	Category    string  `json:"category" binding:"required"`
}

// UpdateGalleryItemRequest represents the request payload for updating a gallery item
type UpdateGalleryItemRequest struct {
	Title       *string `json:"title"`
	Description *string `json:"description"`
	ImageURL    *string `json:"image_url"`
	Category    *string `json:"category"`
	IsActive    *bool   `json:"is_active"`
}