package models

import (
	"errors"
	"time"

	"github.com/google/uuid"
)

// Menu represents a menu item (restaurant, accommodation, afternoon tea) - similar to gallery structure
type Menu struct {
	ID           uuid.UUID `json:"id" db:"id"`
	Title        string    `json:"title" db:"title"`
	Description  *string   `json:"description" db:"description"`
	ImageURL     string    `json:"image_url" db:"image_url"`
	AltText      *string   `json:"alt_text" db:"alt_text"`
	Category     string    `json:"category" db:"category"` // restaurant, accommodation, afternoon_tea
	MenuImage    *string   `json:"menu_image" db:"menu_image"` // Full menu image for display
	DisplayOrder int       `json:"display_order" db:"display_order"`
	IsActive     bool      `json:"is_active" db:"is_active"`
	CreatedAt    time.Time `json:"created_at" db:"created_at"`
	UpdatedAt    time.Time `json:"updated_at" db:"updated_at"`
}

// MenuCategory constants
const (
	CategoryRestaurant    = "restaurant"
	CategoryAccommodation = "accommodation"
	CategoryAfternoonTea  = "afternoon_tea"
)

// CreateMenuRequest represents the request payload for creating a menu
type CreateMenuRequest struct {
	Title       string  `json:"title" binding:"required"`
	Description *string `json:"description"`
	ImageURL    string  `json:"image_url" binding:"required"`
	AltText     *string `json:"alt_text"`
	Category    string  `json:"category" binding:"required"`
	MenuImage   *string `json:"menu_image"`
}

// UpdateMenuRequest represents the request payload for updating a menu
type UpdateMenuRequest struct {
	Title       *string `json:"title"`
	Description *string `json:"description"`
	ImageURL    *string `json:"image_url"`
	AltText     *string `json:"alt_text"`
	Category    *string `json:"category"`
	MenuImage   *string `json:"menu_image"`
	IsActive    *bool   `json:"is_active"`
}



// Validate validates the menu category
func (req *CreateMenuRequest) Validate() error {
	validCategories := map[string]bool{
		CategoryRestaurant:    true,
		CategoryAccommodation: true,
		CategoryAfternoonTea:  true,
	}

	if !validCategories[req.Category] {
		return errors.New("invalid category. Must be one of: restaurant, accommodation, afternoon_tea")
	}

	return nil
}

// Validate validates the update menu request
func (req *UpdateMenuRequest) Validate() error {
	if req.Category != nil {
		validCategories := map[string]bool{
			CategoryRestaurant:    true,
			CategoryAccommodation: true,
			CategoryAfternoonTea:  true,
		}

		if !validCategories[*req.Category] {
			return errors.New("invalid category. Must be one of: restaurant, accommodation, afternoon_tea")
		}
	}

	return nil
}


