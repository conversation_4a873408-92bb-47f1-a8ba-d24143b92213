package models

import (
	"time"
)

// Setting represents a single setting in the database
type Setting struct {
	ID           string    `json:"id" db:"id"`
	SettingKey   string    `json:"setting_key" db:"setting_key"`
	SettingValue *string   `json:"setting_value" db:"setting_value"`
	SettingType  string    `json:"setting_type" db:"setting_type"`
	Description  *string   `json:"description" db:"description"`
	IsActive     bool      `json:"is_active" db:"is_active"`
	CreatedAt    time.Time `json:"created_at" db:"created_at"`
	UpdatedAt    time.Time `json:"updated_at" db:"updated_at"`
}

// SettingsResponse represents the structured settings response
type SettingsResponse struct {
	General     GeneralSettings     `json:"general"`
	Contact     ContactSettings     `json:"contact"`
	SocialMedia SocialMediaSettings `json:"social_media"`
	Business    BusinessSettings    `json:"business"`
}

// GeneralSettings represents general site settings
type GeneralSettings struct {
	SiteTitle string `json:"site_title"`
}

// ContactSettings represents contact information
type ContactSettings struct {
	Email         string `json:"email"`
	Phone         string `json:"phone"`
	Address       string `json:"address"`
	BookingEmail  string `json:"booking_email"`
	EmergencyPhone string `json:"emergency_phone"`
}

// SocialMediaSettings represents social media links
type SocialMediaSettings struct {
	Instagram string `json:"instagram"`
	Facebook  string `json:"facebook"`
	YouTube   string `json:"youtube"`
}

// BusinessSettings represents business operation settings
type BusinessSettings struct {
	OpeningHours    string `json:"opening_hours"`
	GoogleMapsEmbed string `json:"google_maps_embed"`
}

// UpdateSettingsRequest represents the request to update settings
type UpdateSettingsRequest struct {
	General     *GeneralSettings     `json:"general,omitempty"`
	Contact     *ContactSettings     `json:"contact,omitempty"`
	SocialMedia *SocialMediaSettings `json:"social_media,omitempty"`
	Business    *BusinessSettings    `json:"business,omitempty"`
}

// SettingUpdate represents a single setting update
type SettingUpdate struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

// BatchUpdateRequest represents a batch update of settings
type BatchUpdateRequest struct {
	Settings []SettingUpdate `json:"settings"`
}
