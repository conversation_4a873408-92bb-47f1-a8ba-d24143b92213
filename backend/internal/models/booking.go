package models

import (
	"time"

	"github.com/google/uuid"
)

// Booking represents a service booking
type Booking struct {
	ID               uuid.UUID  `json:"id" db:"id"`
	UserID           *uuid.UUID `json:"user_id" db:"user_id"`
	ServiceID        uuid.UUID  `json:"service_id" db:"service_id"`
	BookingDate      time.Time  `json:"booking_date" db:"booking_date"`
	CheckInDate      *time.Time `json:"check_in_date" db:"check_in_date"`
	CheckOutDate     *time.Time `json:"check_out_date" db:"check_out_date"`
	CustomerName     string     `json:"customer_name" db:"customer_name"`
	CustomerEmail    *string    `json:"customer_email" db:"customer_email"`
	CustomerPhone    string     `json:"customer_phone" db:"customer_phone"`
	Adults           int        `json:"adults" db:"adults"`
	Children         int        `json:"children" db:"children"`
	ChildrenUnder6   int        `json:"children_under_6" db:"children_under_6"`
	Children6To11    int        `json:"children_6_to_11" db:"children_6_to_11"`
	TotalPrice       float64    `json:"total_price" db:"total_price"`
	DepositAmount    float64    `json:"deposit_amount" db:"deposit_amount"`
	Status           string     `json:"status" db:"status"`           // 'PENDING', 'CONFIRMED', 'CANCELLED', 'COMPLETED'
	PaymentStatus    string     `json:"payment_status" db:"payment_status"` // 'PENDING', 'PARTIAL', 'COMPLETED', 'REFUNDED'
	SpecialNotes     *string    `json:"special_notes" db:"special_notes"`
	CreatedAt        time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt        time.Time  `json:"updated_at" db:"updated_at"`
}

// CreateBookingRequest represents the request payload for creating a booking
type CreateBookingRequest struct {
	ServiceID        uuid.UUID  `json:"service_id" binding:"required"`
	BookingDate      time.Time  `json:"booking_date" binding:"required"`
	CheckInDate      *time.Time `json:"check_in_date"`
	CheckOutDate     *time.Time `json:"check_out_date"`
	CustomerName     string     `json:"customer_name" binding:"required"`
	CustomerPhone    string     `json:"customer_phone" binding:"required"`
	Adults           int        `json:"adults" binding:"required,min=1"`
	ChildrenUnder6   int        `json:"children_under_6" binding:"min=0"`
	Children6To11    int        `json:"children_6_to_11" binding:"min=0"`
	SpecialNotes     *string    `json:"special_notes"`
}

// UpdateBookingRequest represents the request payload for updating a booking
type UpdateBookingRequest struct {
	BookingDate      *time.Time `json:"booking_date"`
	CheckInDate      *time.Time `json:"check_in_date"`
	CheckOutDate     *time.Time `json:"check_out_date"`
	CustomerName     *string    `json:"customer_name"`
	CustomerPhone    *string    `json:"customer_phone"`
	Adults           *int       `json:"adults"`
	ChildrenUnder6   *int       `json:"children_under_6"`
	Children6To11    *int       `json:"children_6_to_11"`
	Status           *string    `json:"status"`
	PaymentStatus    *string    `json:"payment_status"`
	SpecialNotes     *string    `json:"special_notes"`
}