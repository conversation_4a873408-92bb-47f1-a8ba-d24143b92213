package models

import (
	"database/sql/driver"
	"encoding/json"
	"strings"
	"time"

	"github.com/google/uuid"
)

// Service represents a service offered by the company
type Service struct {
	ID           uuid.UUID      `json:"id" db:"id"`
	Type         string         `json:"type" db:"type"`
	Name         string         `json:"name" db:"name"`
	Description  *string        `json:"description" db:"description"`
	Price        float64        `json:"price" db:"price"`
	ChildPrice   *float64       `json:"child_price" db:"child_price"`
	IsActive     bool           `json:"is_active" db:"is_active"`
	Capacity     int            `json:"capacity" db:"capacity"`
	OpenTime     string         `json:"open_time" db:"open_time"`
	CloseTime    string         `json:"close_time" db:"close_time"`
	Images       StringArray    `json:"images" db:"images"`
	CreatedAt    time.Time      `json:"created_at" db:"created_at"`

	// Embedded details from service_details table
	Inclusions     StringArray `json:"inclusions" db:"inclusions"`
	Requirements   StringArray `json:"requirements" db:"requirements"`
	AgeRestriction string      `json:"age_restriction" db:"age_restriction"`
	BookingPolicy  string      `json:"booking_policy" db:"booking_policy"`
}

// StringArray is a custom type for handling JSON arrays in PostgreSQL
type StringArray []string

// Scan implements the sql.Scanner interface
func (sa *StringArray) Scan(value interface{}) error {
	if value == nil {
		*sa = StringArray{}
		return nil
	}

	switch v := value.(type) {
	case []byte:
		return json.Unmarshal(v, sa)
	case string:
		return json.Unmarshal([]byte(v), sa)
	default:
		*sa = StringArray{}
		return nil
	}
}

// Value implements the driver.Valuer interface
func (sa StringArray) Value() (driver.Value, error) {
	if len(sa) == 0 {
		return "{}", nil
	}
	// Convert to PostgreSQL array format
	result := "{"
	for i, s := range sa {
		if i > 0 {
			result += ","
		}
		// Escape quotes and wrap in quotes
		escaped := strings.ReplaceAll(s, "\"", "\\\"")
		result += "\"" + escaped + "\""
	}
	result += "}"
	return result, nil
}

// ServiceDetails represents additional service details
type ServiceDetails struct {
	ServiceID      uuid.UUID   `json:"service_id" db:"service_id"`
	Inclusions     StringArray `json:"inclusions" db:"inclusions"`
	Requirements   StringArray `json:"requirements" db:"requirements"`
	AgeRestriction string      `json:"age_restriction" db:"age_restriction"`
	BookingPolicy  string      `json:"booking_policy" db:"booking_policy"`
	CreatedAt      time.Time   `json:"created_at" db:"created_at"`
	UpdatedAt      time.Time   `json:"updated_at" db:"updated_at"`
}

// CreateServiceRequest represents the request payload for creating a service
type CreateServiceRequest struct {
	Type        string                      `json:"type" binding:"required"`
	Name        string                      `json:"name" binding:"required"`
	Description *string                     `json:"description"`
	Price       float64                     `json:"price" binding:"required,gt=0"`
	ChildPrice  *float64                    `json:"child_price"`
	Capacity    int                         `json:"capacity" binding:"required,gt=0"`
	OpenTime    string                      `json:"open_time" binding:"required"`
	CloseTime   string                      `json:"close_time" binding:"required"`
	Images      StringArray                 `json:"images"`
	Details     *CreateServiceDetailsRequest `json:"details"`
}

// CreateServiceDetailsRequest represents the request payload for service details
type CreateServiceDetailsRequest struct {
	Inclusions     StringArray `json:"inclusions"`
	Requirements   StringArray `json:"requirements"`
	AgeRestriction string      `json:"age_restriction"`
	BookingPolicy  string      `json:"booking_policy"`
}



// UpdateServiceRequest represents the request payload for updating a service
type UpdateServiceRequest struct {
	Name         *string               `json:"name"`
	Description  *string               `json:"description"`
	Price        *float64              `json:"price"`
	ChildPrice   *float64              `json:"child_price"`
	IsActive     *bool                 `json:"is_active"`
	Capacity     *int                  `json:"capacity"`
	OpenTime     *string               `json:"open_time"`
	CloseTime    *string               `json:"close_time"`
	Images       StringArray           `json:"images"`
	Inclusions   StringArray           `json:"inclusions"`
	Requirements StringArray           `json:"requirements"`
}