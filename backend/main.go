package main

import (
	"fmt"
	"log"
	"os"
	"phongnha-valley/backend/internal/routes"
	"phongnha-valley/backend/pkg/database"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/jmoiron/sqlx"
	"github.com/joho/godotenv"
)

func main() {
	// Check for commands
	if len(os.Args) > 1 {
		switch os.Args[1] {
		case "migrate":
			runMigrations()
			return
		case "seed":
			seedDatabase()
			return
		case "version":
			fmt.Println("Phong Nha Valley Server v1.0")
			return
		}
	}

	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Printf("Warning: .env file not found")
	}

	// Initialize database connection (non-blocking)
	var db *sqlx.DB
	var err error

	log.Printf("🔄 Attempting database connection...")

	// Try to connect to database, but don't block startup
	db, err = database.InitDB()
	if err == nil {
		log.Printf("✅ Database connected successfully")
		defer db.Close()
	} else {
		log.Printf("⚠️ Database connection failed: %v", err)
		log.Printf("⚠️ Server will start without database. Database features will be unavailable.")
		db = nil
	}

	// Set Gin mode
	if os.Getenv("GIN_MODE") == "release" {
		gin.SetMode(gin.ReleaseMode)
	}

	// Initialize router
	router := gin.Default()

	// Configure CORS
	config := cors.DefaultConfig()
	if os.Getenv("GIN_MODE") == "release" {
		// Production CORS - restrict to actual domain
		config.AllowOrigins = []string{
			"https://phongnhavalley.com",
			"https://www.phongnhavalley.com",
			"http://localhost:8080", // Backend serving frontend
		}
	} else {
		// Development CORS
		config.AllowOrigins = []string{
			"http://localhost:8080", // Backend serving frontend
			"http://localhost:3000", // Development frontend
			"http://127.0.0.1:8080",
			"http://127.0.0.1:3000",
		}
	}
	config.AllowCredentials = true
	config.AllowHeaders = []string{"Origin", "Content-Length", "Content-Type", "Authorization"}
	router.Use(cors.New(config))



	// Initialize routes
	routes.SetupRoutes(router, db)

	// Start server
	port := os.Getenv("PORT")
	if port == "" {
		port = "8080"
	}

	log.Printf("🚀 Server starting on port %s", port)
	log.Printf("📁 Serving static files from: ./frontend/public")
	log.Printf("📁 Upload directory: ./uploads")

	if db != nil {
		log.Printf("✅ Database: Connected and ready")
	} else {
		log.Printf("⚠️ Database: Unavailable - running in static mode")
	}

	log.Printf("🌐 Server ready at: http://0.0.0.0:%s", port)

	// Start server with graceful error handling
	if err := router.Run("0.0.0.0:" + port); err != nil {
		log.Printf("❌ Server failed to start: %v", err)
		log.Printf("🔧 Troubleshooting:")
		log.Printf("   - Check if port %s is available", port)
		log.Printf("   - Verify file permissions")
		log.Printf("   - Check environment variables")
		panic(err)
	}
}

// runMigrations runs database migrations
func runMigrations() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Printf("Warning: .env file not found")
	}

	// Initialize database connection
	db, err := database.InitDB()
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	fmt.Println("🔄 Running database migrations...")

	// Simple migration - create tables if not exist
	migrations := []string{
		`CREATE EXTENSION IF NOT EXISTS "uuid-ossp";`,
		`CREATE EXTENSION IF NOT EXISTS "pgcrypto";`,
		`CREATE TABLE IF NOT EXISTS users (
			id TEXT PRIMARY KEY DEFAULT uuid_generate_v4()::text,
			email VARCHAR(255) UNIQUE NOT NULL,
			password TEXT NOT NULL,
			first_name VARCHAR(100),
			last_name VARCHAR(100),
			phone VARCHAR(20),
			role VARCHAR(20) DEFAULT 'user',
			created_at TIMESTAMP DEFAULT NOW(),
			updated_at TIMESTAMP DEFAULT NOW()
		);`,
		`CREATE TABLE IF NOT EXISTS services (
			id TEXT PRIMARY KEY DEFAULT uuid_generate_v4()::text,
			type VARCHAR(100) DEFAULT 'tour',
			name VARCHAR(255) NOT NULL,
			description TEXT,
			price DECIMAL(10,2) DEFAULT 0,
			child_price DECIMAL(10,2),
			capacity INTEGER DEFAULT 1,
			open_time VARCHAR(10) DEFAULT '08:00',
			close_time VARCHAR(10) DEFAULT '18:00',
			images TEXT[],
			inclusions TEXT[],
			requirements TEXT[],
			age_restriction JSONB DEFAULT '{}',
			booking_policy JSONB DEFAULT '{}',
			is_active BOOLEAN DEFAULT true,
			created_at TIMESTAMP DEFAULT NOW(),
			updated_at TIMESTAMP DEFAULT NOW()
		);`,
		`CREATE TABLE IF NOT EXISTS bookings (
			id TEXT PRIMARY KEY DEFAULT uuid_generate_v4()::text,
			service_id TEXT REFERENCES services(id),
			customer_name VARCHAR(255) NOT NULL,
			customer_phone VARCHAR(20) NOT NULL,
			booking_date DATE NOT NULL,
			check_in_date TIMESTAMP,
			check_out_date TIMESTAMP,
			adults INTEGER DEFAULT 1 CHECK (adults >= 1),
			children INTEGER DEFAULT 0,
			children_under_6 INTEGER DEFAULT 0 CHECK (children_under_6 >= 0),
			children_6_to_11 INTEGER DEFAULT 0 CHECK (children_6_to_11 >= 0),
			total_price DECIMAL(10,2) DEFAULT 0 CHECK (total_price >= 0),
			deposit_amount DECIMAL(10,2) DEFAULT 0 CHECK (deposit_amount >= 0),
			status VARCHAR(20) DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'CONFIRMED', 'COMPLETED', 'CANCELLED')),
			payment_status VARCHAR(20) DEFAULT 'PENDING' CHECK (payment_status IN ('PENDING', 'PARTIAL', 'COMPLETED', 'REFUNDED')),
			special_notes TEXT,
			created_at TIMESTAMP DEFAULT NOW(),
			updated_at TIMESTAMP DEFAULT NOW()
		);`,
		`CREATE TABLE IF NOT EXISTS content_blocks (
			id TEXT PRIMARY KEY DEFAULT uuid_generate_v4()::text,
			key VARCHAR(255) UNIQUE NOT NULL,
			value TEXT,
			type VARCHAR(50) DEFAULT 'text',
			created_at TIMESTAMP DEFAULT NOW(),
			updated_at TIMESTAMP DEFAULT NOW()
		);`,
		`CREATE TABLE IF NOT EXISTS settings (
			id TEXT PRIMARY KEY DEFAULT uuid_generate_v4()::text,
			key VARCHAR(255) UNIQUE NOT NULL,
			value TEXT,
			type VARCHAR(50) DEFAULT 'text',
			created_at TIMESTAMP DEFAULT NOW(),
			updated_at TIMESTAMP DEFAULT NOW()
		);`,
		`CREATE TABLE IF NOT EXISTS gallery_items (
			id TEXT PRIMARY KEY DEFAULT uuid_generate_v4()::text,
			title VARCHAR(255),
			description TEXT,
			image_url TEXT NOT NULL,
			alt_text VARCHAR(255),
			is_active BOOLEAN DEFAULT true,
			sort_order INTEGER DEFAULT 0,
			created_at TIMESTAMP DEFAULT NOW(),
			updated_at TIMESTAMP DEFAULT NOW()
		);`,
		// Update services table to add missing columns
		`ALTER TABLE services ADD COLUMN IF NOT EXISTS type VARCHAR(100) DEFAULT 'tour';`,
		`ALTER TABLE services ADD COLUMN IF NOT EXISTS child_price DECIMAL(10,2);`,
		`ALTER TABLE services ADD COLUMN IF NOT EXISTS capacity INTEGER DEFAULT 1;`,
		`ALTER TABLE services ADD COLUMN IF NOT EXISTS open_time VARCHAR(10) DEFAULT '08:00';`,
		`ALTER TABLE services ADD COLUMN IF NOT EXISTS close_time VARCHAR(10) DEFAULT '18:00';`,
		`ALTER TABLE services ADD COLUMN IF NOT EXISTS inclusions TEXT[];`,
		`ALTER TABLE services ADD COLUMN IF NOT EXISTS requirements TEXT[];`,
		`ALTER TABLE services ADD COLUMN IF NOT EXISTS age_restriction JSONB DEFAULT '{}';`,
		`ALTER TABLE services ADD COLUMN IF NOT EXISTS booking_policy JSONB DEFAULT '{}';`,
		// Update price column type
		`ALTER TABLE services ALTER COLUMN price TYPE DECIMAL(10,2);`,
		// Create indexes for bookings table
		`CREATE INDEX IF NOT EXISTS idx_bookings_status ON bookings(status);`,
		`CREATE INDEX IF NOT EXISTS idx_bookings_payment_status ON bookings(payment_status);`,
		`CREATE INDEX IF NOT EXISTS idx_bookings_booking_date ON bookings(booking_date);`,
		`CREATE INDEX IF NOT EXISTS idx_bookings_service_id ON bookings(service_id);`,
		`CREATE INDEX IF NOT EXISTS idx_bookings_customer_phone ON bookings(customer_phone);`,
	}

	for _, migration := range migrations {
		if _, err := db.Exec(migration); err != nil {
			log.Printf("Migration warning: %v", err)
		}
	}

	fmt.Println("✅ Database migrations completed successfully")
}

// seedDatabase seeds the database with initial data
func seedDatabase() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Printf("Warning: .env file not found")
	}

	// Initialize database connection
	db, err := database.InitDB()
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	fmt.Println("🌱 Seeding database with initial data...")

	// Seed admin user
	fmt.Println("👤 Creating admin user...")
	query := `
	INSERT INTO users (id, email, password, first_name, last_name, phone, role, created_at, updated_at)
	VALUES (
		gen_random_uuid()::text,
		'<EMAIL>',
		crypt('password', gen_salt('bf')),
		'Admin',
		'User',
		'+84 123 456 789',
		'admin',
		NOW(),
		NOW()
	)
	ON CONFLICT (email) DO UPDATE SET
		password = crypt('password', gen_salt('bf')),
		first_name = 'Admin',
		last_name = 'User',
		role = 'admin',
		updated_at = NOW()
	`

	_, err = db.Exec(query)
	if err != nil {
		log.Printf("Failed to seed admin user: %v", err)
	} else {
		fmt.Println("✅ Admin user created/updated")
	}

	fmt.Println("✅ Database seeding completed successfully")
}
