-- Fix bookings table schema to match controller expectations
-- This migration ensures the bookings table has all required fields

-- First, add missing columns if they don't exist
ALTER TABLE bookings 
ADD COLUMN IF NOT EXISTS adults INTEGER DEFAULT 1,
ADD COLUMN IF NOT EXISTS children INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS children_under_6 INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS children_6_to_11 INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS total_price DECIMAL(10,2) DEFAULT 0,
ADD COLUMN IF NOT EXISTS deposit_amount DECIMAL(10,2) DEFAULT 0,
ADD COLUMN IF NOT EXISTS payment_status VARCHAR(20) DEFAULT 'PENDING',
ADD COLUMN IF NOT EXISTS check_in_date TIMESTAMP,
ADD COLUMN IF NOT EXISTS check_out_date TIMESTAMP,
ADD COLUMN IF NOT EXISTS special_notes TEXT,
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT NOW();

-- Update existing data if needed
-- Migrate guests to adults if guests exists and adults is 0
UPDATE bookings 
SET adults = COALESCE(guests, 1) 
WHERE adults = 0 OR adults IS NULL;

-- Migrate notes to special_notes if notes exists and special_notes is null
UPDATE bookings 
SET special_notes = notes 
WHERE special_notes IS NULL AND notes IS NOT NULL;

-- Update status values to match expected format
UPDATE bookings 
SET status = UPPER(status) 
WHERE status IN ('pending', 'confirmed', 'cancelled', 'completed');

-- Set default status if invalid
UPDATE bookings 
SET status = 'PENDING' 
WHERE status NOT IN ('PENDING', 'CONFIRMED', 'CANCELLED', 'COMPLETED');

-- Set default payment_status
UPDATE bookings 
SET payment_status = 'PENDING' 
WHERE payment_status IS NULL OR payment_status = '';

-- Add constraints
ALTER TABLE bookings 
ADD CONSTRAINT IF NOT EXISTS check_adults CHECK (adults >= 1),
ADD CONSTRAINT IF NOT EXISTS check_children_under_6_positive CHECK (children_under_6 >= 0),
ADD CONSTRAINT IF NOT EXISTS check_children_6_to_11_positive CHECK (children_6_to_11 >= 0),
ADD CONSTRAINT IF NOT EXISTS check_total_price_positive CHECK (total_price >= 0),
ADD CONSTRAINT IF NOT EXISTS check_deposit_amount_positive CHECK (deposit_amount >= 0);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_bookings_status ON bookings(status);
CREATE INDEX IF NOT EXISTS idx_bookings_payment_status ON bookings(payment_status);
CREATE INDEX IF NOT EXISTS idx_bookings_booking_date ON bookings(booking_date);
CREATE INDEX IF NOT EXISTS idx_bookings_service_id ON bookings(service_id);

-- Add comments for documentation
COMMENT ON COLUMN bookings.adults IS 'Number of adults (required, minimum 1)';
COMMENT ON COLUMN bookings.children IS 'Total number of children (computed from children_under_6 + children_6_to_11)';
COMMENT ON COLUMN bookings.children_under_6 IS 'Number of children under 6 years old (free)';
COMMENT ON COLUMN bookings.children_6_to_11 IS 'Number of children 6-11 years old (50% discount)';
COMMENT ON COLUMN bookings.total_price IS 'Total booking price in VND';
COMMENT ON COLUMN bookings.deposit_amount IS 'Required deposit amount in VND';
COMMENT ON COLUMN bookings.payment_status IS 'Payment status: PENDING, PARTIAL, COMPLETED, REFUNDED';
COMMENT ON COLUMN bookings.status IS 'Booking status: PENDING, CONFIRMED, COMPLETED, CANCELLED';
