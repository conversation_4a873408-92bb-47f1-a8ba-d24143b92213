-- Add separate fields for children age groups and customer contact info
ALTER TABLE bookings 
ADD COLUMN customer_name VARCHAR(255),
ADD COLUMN customer_phone VARCHAR(20),
ADD COLUMN children_under_6 INTEGER DEFAULT 0,
ADD COLUMN children_6_to_11 INTEGER DEFAULT 0;

-- Update existing children field to be computed field (for backward compatibility)
-- We'll keep the children field but it will be computed from the two new fields

-- Add check constraints
ALTER TABLE bookings 
ADD CONSTRAINT check_children_under_6 CHECK (children_under_6 >= 0),
ADD CONSTRAINT check_children_6_to_11 CHECK (children_6_to_11 >= 0);

-- Create index for better performance
CREATE INDEX idx_bookings_customer_phone ON bookings(customer_phone);
CREATE INDEX idx_bookings_customer_name ON bookings(customer_name);

-- Add comments for documentation
COMMENT ON COLUMN bookings.customer_name IS 'Customer full name for booking contact';
COMMENT ON COLUMN bookings.customer_phone IS 'Customer phone number for booking contact';
COMMENT ON COLUMN bookings.children_under_6 IS 'Number of children under 6 years old (free)';
COMMENT ON COLUMN bookings.children_6_to_11 IS 'Number of children 6-11 years old (50% discount)';
