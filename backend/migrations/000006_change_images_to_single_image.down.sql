-- Rollback: Change services.image back to images array
-- Add back the images column as array
ALTER TABLE services ADD COLUMN images TEXT[];

-- Migrate existing data: convert single image to array
UPDATE services 
SET images = CASE 
    WHEN image IS NOT NULL AND image != '' 
    THEN ARRAY[image] 
    ELSE ARRAY[]::TEXT[] 
END;

-- Drop the single image column
ALTER TABLE services DROP COLUMN image;
