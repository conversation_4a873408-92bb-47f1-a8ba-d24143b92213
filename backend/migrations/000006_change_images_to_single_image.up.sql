-- Change services.images from array to single image string
-- First, add the new image column
ALTER TABLE services ADD COLUMN image VARCHAR(500);

-- Migrate existing data: take the first image from the array
UPDATE services 
SET image = CASE 
    WHEN images IS NOT NULL AND array_length(images, 1) > 0 
    THEN images[1] 
    ELSE NULL 
END;

-- Drop the old images column
ALTER TABLE services DROP COLUMN images;

-- Update seed data to use single images
UPDATE services SET image = 'tent1.jpg' WHERE id = '550e8400-e29b-41d4-a716-446655440001';
UPDATE services SET image = 'tea1.jpg' WHERE id = '550e8400-e29b-41d4-a716-446655440002';
UPDATE services SET image = 'premium_tea1.jpg' WHERE id = '550e8400-e29b-41d4-a716-446655440003';
UPDATE services SET image = 'water1.jpg' WHERE id = '550e8400-e29b-41d4-a716-446655440004';
UPDATE services SET image = 'sightseeing1.jpg' WHERE id = '550e8400-e29b-41d4-a716-446655440005';
UPDATE services SET image = 'gokart1.jpg' WHERE id = '550e8400-e29b-41d4-a716-446655440006';
UPDATE services SET image = 'water1.jpg' WHERE id = '550e8400-e29b-41d4-a716-446655440007';
UPDATE services SET image = 'sightseeing1.jpg' WHERE id = '550e8400-e29b-41d4-a716-446655440008';
