-- Create settings table for general site settings
CREATE TABLE IF NOT EXISTS settings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type VARCHAR(50) DEFAULT 'string', -- 'string', 'number', 'boolean', 'json'
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Insert default settings
INSERT INTO settings (setting_key, setting_value, setting_type, description) VALUES 
('site_title', 'Phong Nha Valley', 'string', 'Website title displayed in header and browser tab'),
('contact_email', '<EMAIL>', 'string', 'Primary contact email address'),
('contact_phone', '************', 'string', 'Primary contact phone number'),
('address', '<PERSON><PERSON><PERSON>, Phúc Trạch Commune
Bố Trạch District, Quảng Bình Province', 'string', 'Physical address of the business'),
('opening_hours', '6:30 – 21:00', 'string', 'Business operating hours'),
('instagram_url', '', 'string', 'Instagram profile URL'),
('facebook_url', '', 'string', 'Facebook page URL'),
('youtube_url', '', 'string', 'YouTube channel URL'),
('google_maps_embed', '', 'string', 'Google Maps embed code for location'),
('booking_email', '<EMAIL>', 'string', 'Email for booking inquiries'),
('emergency_phone', '************', 'string', 'Emergency contact number');

-- Create index for performance
CREATE INDEX idx_settings_key ON settings(setting_key);
CREATE INDEX idx_settings_active ON settings(is_active);

-- Create trigger for updated_at
CREATE TRIGGER update_settings_updated_at
    BEFORE UPDATE ON settings
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
