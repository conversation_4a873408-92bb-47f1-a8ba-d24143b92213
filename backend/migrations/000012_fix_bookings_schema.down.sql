-- Rollback bookings schema fixes

-- Remove indexes
DROP INDEX IF EXISTS idx_bookings_service_id;
DROP INDEX IF EXISTS idx_bookings_booking_date;
DROP INDEX IF EXISTS idx_bookings_payment_status;
DROP INDEX IF EXISTS idx_bookings_status;

-- Remove constraints
ALTER TABLE bookings 
DROP CONSTRAINT IF EXISTS check_deposit_amount_positive,
DROP CONSTRAINT IF EXISTS check_total_price_positive,
DROP CONSTRAINT IF EXISTS check_children_6_to_11_positive,
DROP CONSTRAINT IF EXISTS check_children_under_6_positive,
DROP CONSTRAINT IF EXISTS check_adults;

-- Note: We don't remove the columns as they might contain important data
-- and other parts of the system might depend on them
-- If you really need to remove them, uncomment the lines below:

-- ALTER TABLE bookings 
-- DROP COLUMN IF EXISTS updated_at,
-- DROP COLUMN IF EXISTS special_notes,
-- DROP COLUMN IF EXISTS check_out_date,
-- DROP COLUMN IF EXISTS check_in_date,
-- DROP COLUMN IF EXISTS payment_status,
-- DROP COLUMN IF EXISTS deposit_amount,
-- DROP COLUMN IF EXISTS total_price,
-- DROP COLUMN IF EXISTS children_6_to_11,
-- DROP COLUMN IF EXISTS children_under_6,
-- DROP COLUMN IF EXISTS children,
-- DROP COLUMN IF EXISTS adults;
