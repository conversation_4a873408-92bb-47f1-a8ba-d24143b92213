-- Create menus table for restaurant menu management
CREATE TABLE IF NOT EXISTS menus (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100) NOT NULL, -- 'restaurant', 'accommodation', 'afternoon_tea'
    images TEXT[], -- Array of image filenames
    price DECIMAL(10,2),
    is_available BOOLEAN DEFAULT true,
    display_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create index for better query performance
CREATE INDEX IF NOT EXISTS idx_menus_category ON menus(category);
CREATE INDEX IF NOT EXISTS idx_menus_available ON menus(is_available);
CREATE INDEX IF NOT EXISTS idx_menus_display_order ON menus(display_order);

-- Insert sample data
INSERT INTO menus (name, description, category, images, price, is_available, display_order) VALUES
('Phở Bò Đặc Biệt', 'Phở bò truyền thống với thịt bò tươi ngon, nước dùng đậm đà', 'restaurant', '{}', 85000, true, 1),
('Bún Chả Hà Nội', 'Bún chả Hà Nội chính gốc với thịt nướng thơm lừng', 'restaurant', '{}', 75000, true, 2),
('Cơm Tấm Sài Gòn', 'Cơm tấm sườn nướng đặc sản miền Nam', 'restaurant', '{}', 65000, true, 3),

('Phòng Deluxe', 'Phòng nghỉ cao cấp với view sông Chày tuyệt đẹp', 'accommodation', '{}', 1200000, true, 1),
('Phòng Standard', 'Phòng nghỉ tiêu chuẩn thoải mái, tiện nghi đầy đủ', 'accommodation', '{}', 800000, true, 2),
('Glamping Tent', 'Lều glamping độc đáo giữa thiên nhiên hoang sơ', 'accommodation', '{}', 1500000, true, 3),

('Set Trà Chiều Cổ Điển', 'Set trà chiều với bánh ngọt và trà thảo mộc', 'afternoon_tea', '{}', 150000, true, 1),
('Set Trà Chiều Cao Cấp', 'Set trà chiều premium với bánh Pháp và trà nhập khẩu', 'afternoon_tea', '{}', 250000, true, 2),
('Set Trà Chiều Địa Phương', 'Set trà chiều với đặc sản địa phương Quảng Bình', 'afternoon_tea', '{}', 120000, true, 3);
