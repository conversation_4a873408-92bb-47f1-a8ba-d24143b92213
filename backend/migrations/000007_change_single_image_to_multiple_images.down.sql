-- Rollback: Change services.images array back to single image string
-- Add back the image column
ALTER TABLE services ADD COLUMN image VARCHAR(500);

-- Migrate existing data: take the first image from the array
UPDATE services 
SET image = CASE 
    WHEN images IS NOT NULL AND array_length(images, 1) > 0 
    THEN images[1] 
    ELSE NULL 
END;

-- Drop the images column
ALTER TABLE services DROP COLUMN images;
