-- Add menu_image column to menus table for displaying full menu images
ALTER TABLE menus ADD COLUMN IF NOT EXISTS menu_image VARCHAR(500);

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_menus_category_active ON menus(category, is_active);

-- Insert sample menu images for each category
UPDATE menus SET menu_image = '/assets/images/menu-restaurant.jpg' 
WHERE category = 'restaurant' AND menu_image IS NULL;

UPDATE menus SET menu_image = '/assets/images/menu-accommodation.jpg' 
WHERE category = 'accommodation' AND menu_image IS NULL;

UPDATE menus SET menu_image = '/assets/images/menu-tea.jpg' 
WHERE category = 'afternoon_tea' AND menu_image IS NULL;
