# 🚀 Phong Nha Valley - Deployment Guide for Fly.io

## 📋 Tổng quan Fly.io Deployment

**Fly.io** là platform cloud hiện đại, hoàn hảo cho việc deploy ứng dụng Docker với:

### **🎯 Ưu điểm của Fly.io:**
- ✅ **Global Edge Network** - Deploy gần user nhất
- ✅ **Docker Native** - Hỗ trợ Docker containers
- ✅ **PostgreSQL Managed** - Database tự động backup
- ✅ **Auto-scaling** - Scale theo traffic
- ✅ **Free tier** - $5/month credit miễn phí
- ✅ **SSL tự động** - HTTPS out-of-the-box
- ✅ **Volume storage** - Persistent file storage
- ✅ **Health checks** - Monitoring tự động

### **💰 Chi phí ước tính:**
```
Shared CPU (256MB RAM): ~$2-5/month
PostgreSQL (1GB): ~$5-10/month
Volume storage (1GB): ~$0.15/month
Total: ~$7-15/month
```

---

## 🏗️ **Kiến trúc Deployment**

### **📊 Architecture Overview:**
```
Internet → Fly.io Load Balancer → Docker Container
                                 ├── Frontend (Vue.js)
                                 ├── Backend (Go API)
                                 └── Volume Storage (uploads)
                                 
External Services:
├── Fly PostgreSQL (managed database)
├── Fly Volumes (persistent storage)
└── Fly Metrics (monitoring)
```

### **🐳 Docker Strategy:**
```dockerfile
Multi-stage build:
1. Frontend Build Stage → Static files
2. Backend Build Stage → Go binary
3. Production Stage → Nginx + Go + Static files
```

---

## 📦 **Prerequisites**

### **🛠️ Required Tools:**
```bash
# 1. Install Fly CLI
curl -L https://fly.io/install.sh | sh

# 2. Login to Fly.io
flyctl auth login

# 3. Verify installation
flyctl version
```

### **💳 Account Setup:**
1. **Sign up**: https://fly.io/app/sign-up
2. **Add payment method** (required for PostgreSQL)
3. **Verify email**

---

## 🐳 **Docker Configuration**

### **📝 Production Dockerfile:**
```dockerfile
# Multi-stage build for production
FROM node:18-alpine AS frontend-builder

WORKDIR /app/frontend
COPY frontend/package*.json ./
RUN npm ci --only=production
COPY frontend/ ./
RUN npm run build

# Backend builder
FROM golang:1.21-alpine AS backend-builder

WORKDIR /app/backend
COPY backend/go.mod backend/go.sum ./
RUN go mod download
COPY backend/ ./
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o phongnha-server main.go

# Production stage
FROM nginx:alpine

# Install supervisor for process management
RUN apk add --no-cache supervisor

# Copy frontend build
COPY --from=frontend-builder /app/frontend/dist /usr/share/nginx/html

# Copy backend binary
COPY --from=backend-builder /app/backend/phongnha-server /usr/local/bin/

# Copy configuration files
COPY deploy/nginx.conf /etc/nginx/nginx.conf
COPY deploy/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# Create directories
RUN mkdir -p /app/uploads /app/logs /var/log/supervisor

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8080/api/v1/health || exit 1

# Start supervisor
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]
```

### **🌐 Nginx Configuration:**
```nginx
# deploy/nginx.conf
events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    
    # Logging
    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log;
    
    # Gzip compression
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    
    server {
        listen 8080;
        server_name _;
        
        # Frontend static files
        location / {
            root /usr/share/nginx/html;
            try_files $uri $uri/ /index.html;
            
            # Cache static assets
            location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
                expires 1y;
                add_header Cache-Control "public, immutable";
            }
        }
        
        # API proxy to Go backend
        location /api/ {
            proxy_pass http://127.0.0.1:3000;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # CORS headers
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
            add_header Access-Control-Allow-Headers "Content-Type, Authorization";
            
            if ($request_method = 'OPTIONS') {
                return 204;
            }
        }
        
        # File uploads
        location /uploads/ {
            alias /app/uploads/;
            expires 1y;
            add_header Cache-Control "public";
        }
    }
}
```

### **⚙️ Supervisor Configuration:**
```ini
# deploy/supervisord.conf
[supervisord]
nodaemon=true
user=root
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid

[program:nginx]
command=nginx -g "daemon off;"
autostart=true
autorestart=true
stderr_logfile=/var/log/supervisor/nginx.err.log
stdout_logfile=/var/log/supervisor/nginx.out.log

[program:phongnha-api]
command=/usr/local/bin/phongnha-server
directory=/app
autostart=true
autorestart=true
environment=PORT=3000,GIN_MODE=release
stderr_logfile=/var/log/supervisor/api.err.log
stdout_logfile=/var/log/supervisor/api.out.log
```

---

## 🚀 **Fly.io Configuration**

### **📝 fly.toml Configuration:**
```toml
# fly.toml
app = "phongnha-valley"
primary_region = "sin"  # Singapore for Vietnam users

[build]
  dockerfile = "Dockerfile.fly"

[env]
  GIN_MODE = "release"
  PORT = "8080"

[http_service]
  internal_port = 8080
  force_https = true
  auto_stop_machines = true
  auto_start_machines = true
  min_machines_running = 1
  processes = ["app"]

  [[http_service.checks]]
    grace_period = "10s"
    interval = "30s"
    method = "GET"
    timeout = "5s"
    path = "/api/v1/health"

[vm]
  cpu_kind = "shared"
  cpus = 1
  memory_mb = 512

[[mounts]]
  source = "phongnha_uploads"
  destination = "/app/uploads"

[metrics]
  port = 9091
  path = "/metrics"
```

### **🗄️ Database Configuration:**
```bash
# Create PostgreSQL database
flyctl postgres create --name phongnha-db --region sin

# Attach database to app
flyctl postgres attach --app phongnha-valley phongnha-db
```

### **💾 Volume Storage:**
```bash
# Create volume for file uploads
flyctl volumes create phongnha_uploads --region sin --size 1
```

---

## 🛠️ **Environment Variables**

### **🔐 Secrets Management:**
```bash
# Set environment variables
flyctl secrets set \
  DB_HOST="phongnha-db.internal" \
  DB_PORT="5432" \
  DB_USER="postgres" \
  DB_PASSWORD="your_db_password" \
  DB_NAME="phongnha_production" \
  JWT_SECRET="your_jwt_secret_key" \
  ADMIN_PASSWORD="your_admin_password" \
  CORS_ORIGINS="https://phongnha-valley.fly.dev"

# List secrets
flyctl secrets list
```

### **📋 Environment Template:**
```env
# Production environment for Fly.io
DB_HOST=phongnha-db.internal
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=your_secure_password
DB_NAME=phongnha_production
DB_SSLMODE=require

PORT=3000
GIN_MODE=release

CORS_ORIGINS=https://phongnha-valley.fly.dev,https://www.yourdomain.com

UPLOAD_DIR=/app/uploads
MAX_UPLOAD_SIZE=10485760
ALLOWED_EXTENSIONS=jpg,jpeg,png,gif,webp,svg

JWT_SECRET=your_super_secure_jwt_secret_key
ADMIN_PASSWORD=your_admin_password

LOG_LEVEL=info
```

---

## 📋 **Deployment Scripts**

### **🚀 Main Deployment Script:**
```bash
#!/bin/bash
# deploy-fly.sh

set -e

echo "🚀 Deploying Phong Nha Valley to Fly.io"
echo "======================================"

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    if ! command -v flyctl &> /dev/null; then
        print_error "Fly CLI not installed. Install from: https://fly.io/docs/hands-on/install-flyctl/"
        exit 1
    fi
    
    if ! flyctl auth whoami &> /dev/null; then
        print_error "Not logged in to Fly.io. Run: flyctl auth login"
        exit 1
    fi
    
    print_status "Prerequisites check passed"
}

# Create Fly app if not exists
create_app() {
    print_status "Creating Fly.io application..."
    
    if flyctl apps list | grep -q "phongnha-valley"; then
        print_warning "App 'phongnha-valley' already exists"
    else
        flyctl apps create phongnha-valley --org personal
        print_status "App created successfully"
    fi
}

# Setup database
setup_database() {
    print_status "Setting up PostgreSQL database..."
    
    if flyctl apps list | grep -q "phongnha-db"; then
        print_warning "Database 'phongnha-db' already exists"
    else
        flyctl postgres create --name phongnha-db --region sin --vm-size shared-cpu-1x --volume-size 1
        print_status "Database created successfully"
    fi
    
    # Attach database
    flyctl postgres attach --app phongnha-valley phongnha-db
    print_status "Database attached to app"
}

# Create volume
create_volume() {
    print_status "Creating volume for file uploads..."
    
    if flyctl volumes list --app phongnha-valley | grep -q "phongnha_uploads"; then
        print_warning "Volume 'phongnha_uploads' already exists"
    else
        flyctl volumes create phongnha_uploads --region sin --size 1 --app phongnha-valley
        print_status "Volume created successfully"
    fi
}

# Set secrets
set_secrets() {
    print_status "Setting environment secrets..."
    
    # Generate secure secrets if not provided
    JWT_SECRET=${JWT_SECRET:-$(openssl rand -hex 32)}
    ADMIN_PASSWORD=${ADMIN_PASSWORD:-$(openssl rand -base64 12)}
    
    flyctl secrets set \
        JWT_SECRET="$JWT_SECRET" \
        ADMIN_PASSWORD="$ADMIN_PASSWORD" \
        CORS_ORIGINS="https://phongnha-valley.fly.dev" \
        --app phongnha-valley
    
    print_status "Secrets configured"
    print_warning "Admin password: $ADMIN_PASSWORD (save this!)"
}

# Deploy application
deploy_app() {
    print_status "Deploying application..."
    
    flyctl deploy --app phongnha-valley
    
    if [ $? -eq 0 ]; then
        print_status "Deployment successful!"
        print_status "App URL: https://phongnha-valley.fly.dev"
    else
        print_error "Deployment failed"
        exit 1
    fi
}

# Run database migrations
run_migrations() {
    print_status "Running database migrations..."
    
    # Connect to database and run migrations
    flyctl ssh console --app phongnha-valley -C "/usr/local/bin/phongnha-server migrate"
    
    print_status "Migrations completed"
}

# Main deployment process
main() {
    check_prerequisites
    create_app
    setup_database
    create_volume
    set_secrets
    deploy_app
    run_migrations
    
    echo ""
    print_status "🎉 Deployment completed successfully!"
    echo ""
    echo "📋 Next steps:"
    echo "  1. Visit: https://phongnha-valley.fly.dev"
    echo "  2. Admin panel: https://phongnha-valley.fly.dev/admin"
    echo "  3. Monitor: flyctl logs --app phongnha-valley"
    echo "  4. Scale: flyctl scale count 2 --app phongnha-valley"
    echo ""
}

# Run main function
main "$@"
```

---

## 🔧 **Management Scripts**

### **📊 Monitoring Script:**
```bash
#!/bin/bash
# monitor-fly.sh

APP_NAME="phongnha-valley"

echo "📊 Phong Nha Valley - Fly.io Monitoring"
echo "======================================"

# App status
echo "🚀 App Status:"
flyctl status --app $APP_NAME

echo ""
echo "📈 Metrics:"
flyctl metrics --app $APP_NAME

echo ""
echo "📋 Recent Logs:"
flyctl logs --app $APP_NAME -n 20

echo ""
echo "💾 Volume Status:"
flyctl volumes list --app $APP_NAME

echo ""
echo "🗄️ Database Status:"
flyctl postgres list

echo ""
echo "🔐 Secrets:"
flyctl secrets list --app $APP_NAME
```

### **🔄 Update Script:**
```bash
#!/bin/bash
# update-fly.sh

APP_NAME="phongnha-valley"

echo "🔄 Updating Phong Nha Valley on Fly.io"
echo "====================================="

# Build and deploy
echo "🏗️ Building and deploying..."
flyctl deploy --app $APP_NAME

# Check deployment status
echo "✅ Checking deployment..."
flyctl status --app $APP_NAME

# Show logs
echo "📋 Recent logs:"
flyctl logs --app $APP_NAME -n 10

echo "🎉 Update completed!"
```

### **💾 Backup Script:**
```bash
#!/bin/bash
# backup-fly.sh

APP_NAME="phongnha-valley"
DB_NAME="phongnha-db"
BACKUP_DIR="./backups"

echo "💾 Backing up Phong Nha Valley"
echo "=============================="

mkdir -p $BACKUP_DIR

# Database backup
echo "🗄️ Backing up database..."
flyctl postgres connect --app $DB_NAME -c "pg_dump phongnha_production" > $BACKUP_DIR/db_$(date +%Y%m%d_%H%M%S).sql

# Volume backup (files)
echo "📁 Backing up uploaded files..."
flyctl ssh sftp get -r /app/uploads $BACKUP_DIR/uploads_$(date +%Y%m%d_%H%M%S) --app $APP_NAME

echo "✅ Backup completed in $BACKUP_DIR"
```

---

## 🎯 **Custom Domain Setup**

### **🌐 Domain Configuration:**
```bash
# Add custom domain
flyctl certs create yourdomain.com --app phongnha-valley
flyctl certs create www.yourdomain.com --app phongnha-valley

# Check certificate status
flyctl certs list --app phongnha-valley

# Update DNS records (at your domain provider):
# A record: yourdomain.com → [Fly.io IP]
# CNAME: www.yourdomain.com → phongnha-valley.fly.dev
```

### **🔄 Update CORS for custom domain:**
```bash
flyctl secrets set CORS_ORIGINS="https://yourdomain.com,https://www.yourdomain.com,https://phongnha-valley.fly.dev" --app phongnha-valley
```

---

## 📊 **Monitoring & Scaling**

### **📈 Scaling Commands:**
```bash
# Scale up
flyctl scale count 2 --app phongnha-valley

# Scale memory
flyctl scale memory 1024 --app phongnha-valley

# Scale to different regions
flyctl scale count 1 --region sin --app phongnha-valley
flyctl scale count 1 --region nrt --app phongnha-valley
```

### **📋 Monitoring Commands:**
```bash
# Real-time logs
flyctl logs --app phongnha-valley -f

# App metrics
flyctl metrics --app phongnha-valley

# SSH into container
flyctl ssh console --app phongnha-valley

# Database console
flyctl postgres connect --app phongnha-db
```

---

## 🆘 **Troubleshooting**

### **❌ Common Issues:**

#### **1. Build failures:**
```bash
# Check build logs
flyctl logs --app phongnha-valley

# Local build test
docker build -f Dockerfile.fly -t phongnha-test .
docker run -p 8080:8080 phongnha-test
```

#### **2. Database connection issues:**
```bash
# Check database status
flyctl postgres list

# Test connection
flyctl ssh console --app phongnha-valley -C "nc -zv phongnha-db.internal 5432"
```

#### **3. Volume mount issues:**
```bash
# Check volume status
flyctl volumes list --app phongnha-valley

# SSH and check mount
flyctl ssh console --app phongnha-valley -C "ls -la /app/uploads"
```

#### **4. SSL certificate issues:**
```bash
# Check certificate status
flyctl certs list --app phongnha-valley

# Force certificate renewal
flyctl certs create yourdomain.com --app phongnha-valley
```

---

## 💰 **Cost Optimization**

### **💡 Cost-saving tips:**
```bash
# Auto-stop machines when idle
flyctl apps update --auto-stop-machines=true --app phongnha-valley

# Use shared CPU
flyctl scale vm shared-cpu-1x --app phongnha-valley

# Monitor usage
flyctl billing show

# Set spending limits
flyctl orgs billing-alerts create --threshold 20
```

---

## ✅ **Deployment Checklist**

### **Pre-deployment:**
- [ ] Fly CLI installed and authenticated
- [ ] Payment method added to Fly.io account
- [ ] Domain DNS configured (if using custom domain)
- [ ] Environment variables prepared

### **Deployment:**
- [ ] Run `./deploy-fly.sh`
- [ ] Verify app is running: `flyctl status`
- [ ] Check logs: `flyctl logs`
- [ ] Test API: `curl https://phongnha-valley.fly.dev/api/v1/health`
- [ ] Test frontend: Visit https://phongnha-valley.fly.dev

### **Post-deployment:**
- [ ] Configure custom domain (if needed)
- [ ] Setup monitoring alerts
- [ ] Test file uploads
- [ ] Verify database operations
- [ ] Setup backup schedule
- [ ] Configure scaling rules

---

## 🎉 **Conclusion**

**Fly.io deployment offers:**
- ✅ **Global performance** with edge locations
- ✅ **Auto-scaling** based on demand  
- ✅ **Managed PostgreSQL** with backups
- ✅ **SSL certificates** automatically managed
- ✅ **Docker-native** deployment
- ✅ **Cost-effective** for small to medium apps

**Total setup time:** 15-30 minutes
**Monthly cost:** $7-15 for small traffic
**Scalability:** Global, auto-scaling

🚀 **Ready to deploy? Run `./deploy-fly.sh` to get started!**

---

## 🎯 **QUICK START GUIDE**

### **📋 Prerequisites:**
```bash
# 1. Install Fly CLI
curl -L https://fly.io/install.sh | sh

# 2. Login to Fly.io
flyctl auth login

# 3. Add payment method (required for PostgreSQL)
# Visit: https://fly.io/dashboard/billing
```

### **🚀 One-Command Deployment:**
```bash
# Deploy everything automatically
./deploy-fly.sh

# Output:
✅ App created: phongnha-valley
✅ Database created: phongnha-db
✅ Volume created: phongnha_uploads
✅ Secrets configured
✅ App deployed: https://phongnha-valley.fly.dev
✅ Admin password: [generated]
```

### **📁 Files Created:**
- ✅ **`Dockerfile.fly`** - Production Docker container
- ✅ **`fly.toml`** - Fly.io configuration
- ✅ **`deploy-fly.sh`** - Main deployment script
- ✅ **`monitor-fly.sh`** - Monitoring dashboard
- ✅ **`update-fly.sh`** - Update management
- ✅ **`backup-fly.sh`** - Backup automation
- ✅ **`setup-domain-fly.sh`** - Custom domain setup

---

## 🛠️ **Management Scripts**

### **📊 Monitoring:**
```bash
# Complete monitoring dashboard
./monitor-fly.sh

# Interactive monitoring
./monitor-fly.sh --interactive

# Real-time logs
./monitor-fly.sh --logs

# Health checks only
./monitor-fly.sh --health
```

### **🔄 Updates:**
```bash
# Full update (recommended)
./update-fly.sh

# Quick update (deploy only)
./update-fly.sh --quick

# Safe update (with auto-rollback)
./update-fly.sh --safe

# Manual rollback
./update-fly.sh --rollback
```

### **💾 Backups:**
```bash
# Full backup (database + files + config)
./backup-fly.sh

# Database only
./backup-fly.sh --database-only

# Files only
./backup-fly.sh --files-only

# List backups
./backup-fly.sh --list

# Restore from backup
./backup-fly.sh --restore
```

### **🌐 Custom Domain:**
```bash
# Setup custom domain
./setup-domain-fly.sh

# Test domain
./setup-domain-fly.sh --test yourdomain.com

# Show DNS configuration
./setup-domain-fly.sh --dns yourdomain.com

# Remove domain
./setup-domain-fly.sh --remove yourdomain.com
```

---

## 📊 **Cost & Performance**

### **💰 Estimated Monthly Costs:**
```
🖥️  Shared CPU (512MB RAM): $5-8/month
🗄️  PostgreSQL (1GB): $5-10/month
💾 Volume (1GB): $0.15/month
🌐 Bandwidth: $0.02/GB
📊 Total: ~$10-20/month
```

### **⚡ Performance Features:**
- **Global Edge Network** - Deploy in 30+ regions
- **Auto-scaling** - Scale from 0 to 1000+ instances
- **Health checks** - Automatic restart on failure
- **Load balancing** - Built-in traffic distribution
- **SSL termination** - Automatic HTTPS
- **Volume storage** - Persistent file storage

### **📈 Scaling Commands:**
```bash
# Scale instances
flyctl scale count 2 --app phongnha-valley

# Scale memory
flyctl scale memory 1024 --app phongnha-valley

# Scale to multiple regions
flyctl scale count 1 --region sin --app phongnha-valley  # Singapore
flyctl scale count 1 --region nrt --app phongnha-valley  # Tokyo
flyctl scale count 1 --region fra --app phongnha-valley  # Frankfurt
```

---

## 🔧 **Advanced Configuration**

### **🌍 Multi-Region Deployment:**
```bash
# Deploy to multiple regions for global performance
flyctl regions add sin nrt fra --app phongnha-valley
flyctl scale count 1 --region sin --app phongnha-valley
flyctl scale count 1 --region nrt --app phongnha-valley
flyctl scale count 1 --region fra --app phongnha-valley
```

### **🔒 Security Hardening:**
```bash
# Enable private networking
flyctl wireguard create

# Setup firewall rules
flyctl ips allocate-v4 --private --app phongnha-valley

# Configure secrets rotation
flyctl secrets set JWT_SECRET="$(openssl rand -hex 32)" --app phongnha-valley
```

### **📊 Monitoring & Alerts:**
```bash
# Setup monitoring
flyctl metrics --app phongnha-valley

# Configure alerts
flyctl orgs billing-alerts create --threshold 50

# Custom metrics endpoint
curl https://phongnha-valley.fly.dev/metrics
```

---

## 🆘 **Troubleshooting**

### **❌ Common Issues:**

#### **1. Build failures:**
```bash
# Check build logs
flyctl logs --app phongnha-valley

# Local build test
docker build -f Dockerfile.fly -t phongnha-test .
docker run -p 8080:8080 phongnha-test

# Clear build cache
flyctl deploy --no-cache --app phongnha-valley
```

#### **2. Database connection issues:**
```bash
# Check database status
flyctl postgres list

# Test connection
flyctl ssh console --app phongnha-valley -C "nc -zv phongnha-db.internal 5432"

# Check database logs
flyctl logs --app phongnha-db
```

#### **3. Volume mount issues:**
```bash
# Check volume status
flyctl volumes list --app phongnha-valley

# SSH and check mount
flyctl ssh console --app phongnha-valley -C "ls -la /app/uploads"

# Recreate volume if needed
flyctl volumes destroy phongnha_uploads --app phongnha-valley
flyctl volumes create phongnha_uploads --region sin --size 1 --app phongnha-valley
```

#### **4. SSL certificate issues:**
```bash
# Check certificate status
flyctl certs list --app phongnha-valley

# Force certificate renewal
flyctl certs create yourdomain.com --app phongnha-valley

# Check DNS propagation
nslookup yourdomain.com
dig yourdomain.com
```

#### **5. Performance issues:**
```bash
# Check resource usage
flyctl metrics --app phongnha-valley

# Scale up resources
flyctl scale memory 1024 --app phongnha-valley
flyctl scale count 2 --app phongnha-valley

# Check slow queries
flyctl ssh console --app phongnha-valley -C "tail -f /app/logs/app.log"
```

---

## 📋 **Production Checklist**

### **🚀 Pre-deployment:**
- [ ] Fly CLI installed and authenticated
- [ ] Payment method added to Fly.io account
- [ ] Domain DNS configured (if using custom domain)
- [ ] Environment variables prepared
- [ ] Docker tested locally

### **🔧 Deployment:**
- [ ] Run `./deploy-fly.sh`
- [ ] Verify app status: `flyctl status --app phongnha-valley`
- [ ] Test endpoints: `curl https://phongnha-valley.fly.dev/api/v1/health`
- [ ] Check logs: `flyctl logs --app phongnha-valley`
- [ ] Verify database: `flyctl postgres connect --app phongnha-db`

### **🌐 Post-deployment:**
- [ ] Setup custom domain: `./setup-domain-fly.sh`
- [ ] Configure monitoring: `./monitor-fly.sh`
- [ ] Test file uploads
- [ ] Verify admin panel access
- [ ] Setup backup schedule: `./backup-fly.sh`
- [ ] Configure scaling rules
- [ ] Test disaster recovery

### **🔒 Security:**
- [ ] Change default admin password
- [ ] Setup SSL certificates
- [ ] Configure CORS origins
- [ ] Enable security headers
- [ ] Setup monitoring alerts
- [ ] Regular security updates

### **📊 Performance:**
- [ ] Configure caching
- [ ] Optimize images
- [ ] Setup CDN (if needed)
- [ ] Monitor resource usage
- [ ] Configure auto-scaling
- [ ] Performance testing

---

## 🎉 **Success Indicators**

### **✅ Deployment Success:**
1. **App running**: `flyctl status` shows "running"
2. **Health checks**: All endpoints return 200 OK
3. **Database connected**: Admin panel loads data
4. **File uploads**: Images upload and display
5. **SSL working**: HTTPS redirects properly
6. **Monitoring active**: Metrics and logs available

### **📈 Performance Targets:**
- **Response time**: < 500ms for API calls
- **Uptime**: > 99.9% availability
- **Load time**: < 3 seconds for homepage
- **Database queries**: < 100ms average
- **File uploads**: < 5 seconds for 10MB files

### **💰 Cost Optimization:**
- **Auto-stop**: Machines stop when idle
- **Right-sizing**: CPU/memory optimized
- **Region selection**: Closest to users
- **Volume size**: Appropriate for data
- **Monitoring**: Track usage and costs

---

## 📞 **Support & Resources**

### **📚 Documentation:**
- [Fly.io Documentation](https://fly.io/docs/)
- [Docker Best Practices](https://docs.docker.com/develop/best-practices/)
- [PostgreSQL on Fly.io](https://fly.io/docs/postgres/)
- [Custom Domains](https://fly.io/docs/app-guides/custom-domains-with-fly/)

### **🛠️ Tools:**
- **Fly CLI**: `flyctl --help`
- **Docker**: `docker --help`
- **Monitoring**: Fly.io Dashboard
- **Logs**: `flyctl logs`

### **🆘 Getting Help:**
- **Fly.io Community**: https://community.fly.io/
- **Discord**: https://fly.io/discord
- **Support**: https://fly.io/docs/about/support/
- **Status**: https://status.fly.io/

---

## 🎯 **Next Steps**

### **🚀 Immediate (Day 1):**
1. Deploy application: `./deploy-fly.sh`
2. Test all functionality
3. Setup monitoring: `./monitor-fly.sh`
4. Configure custom domain: `./setup-domain-fly.sh`

### **📊 Week 1:**
1. Monitor performance and costs
2. Setup automated backups
3. Configure scaling rules
4. Performance optimization

### **🔧 Month 1:**
1. Security audit and hardening
2. Disaster recovery testing
3. Performance tuning
4. User feedback integration

### **📈 Ongoing:**
1. Regular updates: `./update-fly.sh`
2. Backup verification: `./backup-fly.sh`
3. Cost optimization
4. Feature development

---

**🎊 Congratulations! Your Phong Nha Valley website is now running on Fly.io's global edge network!**

**🌐 Live URL**: https://phongnha-valley.fly.dev
**🔧 Admin Panel**: https://phongnha-valley.fly.dev/admin
**📊 Monitor**: `./monitor-fly.sh`
**🔄 Update**: `./update-fly.sh`

**Happy hosting on the edge! 🚀**
