# 🚀 Fast Build System

H<PERSON> thống build tối ưu cho development, không ảnh hưởng đến dự án chính.

## 📋 Tổng quan

Fast Build System được thiết kế để:
- ⚡ <PERSON>i<PERSON><PERSON> thời gian build từ 10+ phút xuống 2-3 phút
- 🔄 Tận dụng Docker layer caching
- 🎯 Tách biệt hoàn toàn với hệ thống build chính
- 💾 Sử dụng tài nguyên hiệu quả hơn

## 🛠️ Cài đặt

```bash
# 1. Chạy script setup
bash setup-fast-build.sh

# 2. Bắt đầu sử dụng
./manage-fast.sh rebuild
```

## 📖 Sử dụng

### C<PERSON><PERSON> l<PERSON>nh cơ bản:

```bash
# Build và start toàn bộ
./manage-fast.sh rebuild

# Start services
./manage-fast.sh start

# Stop services  
./manage-fast.sh stop

# Xem status
./manage-fast.sh status

# Xem logs
./manage-fast.sh logs

# Build frontend only (r<PERSON><PERSON> n<PERSON>)
./manage-fast.sh frontend
```

### Ports và URLs:

- **Fast Build**: http://localhost:8080
- **Database**: localhost:5433
- **Normal Build**: http://localhost:3000 (không thay đổi)

## 🔧 Tối ưu hóa

### 1. Multi-stage Docker Build
```dockerfile
# Tách biệt frontend và backend build
FROM node:18-alpine AS frontend-deps
FROM golang:1.21-alpine AS go-deps
```

### 2. Layer Caching
- Dependencies được cache riêng biệt
- Source code thay đổi không ảnh hưởng dependency cache

### 3. Build Flags Optimization
```bash
# Loại bỏ debug info, giảm kích thước binary
CGO_ENABLED=0 GOOS=linux go build -ldflags="-w -s" -trimpath
```

### 4. Resource Limits
```yaml
deploy:
  resources:
    limits:
      memory: 512M
      cpus: '1.0'
```

## 📊 So sánh Performance

| Aspect | Normal Build | Fast Build | Improvement |
|--------|-------------|------------|-------------|
| First Build | 10-15 min | 3-5 min | 60-70% faster |
| Rebuild | 8-12 min | 1-3 min | 75-85% faster |
| Frontend Only | 2-3 min | 30-60s | 50-75% faster |
| Image Size | ~500MB | ~200MB | 60% smaller |

## 🔄 Switching Between Systems

### Chuyển sang Fast Build:
```bash
cp .dockerignore.fast .dockerignore
./manage-fast.sh rebuild
```

### Quay về Normal Build:
```bash
cp .dockerignore.backup .dockerignore
./manage.sh rebuild
```

## 🗂️ File Structure

```
├── Dockerfile.fast              # Optimized Dockerfile
├── docker-compose.fast.yml      # Fast build compose
├── manage-fast.sh               # Fast build manager
├── .dockerignore.fast           # Optimized dockerignore
├── frontend/Makefile.fast       # Frontend fast build
└── setup-fast-build.sh         # Setup script
```

## ⚠️ Lưu ý

1. **Không ảnh hưởng dự án chính**: Tất cả files có suffix `.fast`
2. **Ports khác nhau**: Fast build dùng 8080, 5433
3. **Database riêng biệt**: Volume `phongnha_db_data_fast`
4. **Environment variables**: Giống hệt dự án chính

## 🐛 Troubleshooting

### Build chậm?
```bash
# Clean Docker cache
docker system prune -f

# Rebuild from scratch
./manage-fast.sh clean
./manage-fast.sh rebuild
```

### Port conflicts?
```bash
# Check ports
netstat -tulpn | grep :8080
netstat -tulpn | grep :5433

# Stop conflicting services
./manage-fast.sh stop
```

### Memory issues?
```bash
# Check Docker memory
docker system df

# Increase Docker memory limit in Docker Desktop
```

## 🎯 Best Practices

1. **Development workflow**:
   ```bash
   # Frontend changes only
   ./manage-fast.sh frontend
   
   # Backend changes
   ./manage-fast.sh rebuild
   ```

2. **Testing**:
   - Sử dụng fast build cho development
   - Sử dụng normal build cho production testing

3. **Resource management**:
   - Clean up định kỳ: `./manage-fast.sh clean`
   - Monitor disk space: `docker system df`

## 📞 Support

Nếu gặp vấn đề:
1. Check logs: `./manage-fast.sh logs`
2. Check status: `./manage-fast.sh status`
3. Clean và rebuild: `./manage-fast.sh clean && ./manage-fast.sh rebuild`
