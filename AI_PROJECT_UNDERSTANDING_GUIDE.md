# 🤖 AI Project Understanding Guide

## 📖 Hướng dẫn cho AI để hiểu và build dự án một cách chính xác

Khi được yêu cầu đọc, hiểu và build/rebuild một dự án, <PERSON> nên đọc các files theo thứ tự ưu tiên sau để có hiểu biết chính xác và toàn diện nhất.

---

## 🎯 **BƯỚC 1: Files quan trọng nhất (PHẢI ĐỌC TRƯỚC)**

### 1. **DEPLOYMENT_README.md** - Tổng quan toàn bộ dự án
**Mục đích**: Hiểu big picture của dự án
```bash
# File này cung cấp:
- Cấu trúc tổng thể dự án
- Các scripts và tools có sẵn
- Quick start commands
- System requirements
- Architecture overview
```

### 2. **manage.sh** - Script quản lý chính
**<PERSON><PERSON><PERSON> đích**: Hiểu cách vận hành dự án
```bash
# File này cho biết:
- <PERSON><PERSON><PERSON> commands có sẵn để quản lý dự án
- Cách start/stop/rebuild services
- Environment variables cần thiết
- Docker configuration
- Project workflow
```

### 3. **Dockerfile** - Container configuration
**Mục đích**: Hiểu cách build application
```bash
# File này cho biết:
- Cách build application
- Dependencies và requirements
- Build process và structure
- Multi-stage build setup
```

---

## 📋 **BƯỚC 2: Files hỗ trợ quan trọng**

### 4. **docker-compose.yml** - Services configuration
**Mục đích**: Hiểu architecture và services
```bash
# Hiểu về:
- Services architecture (app, database, etc.)
- Network configuration
- Volume mounts và data persistence
- Environment variables
- Service dependencies
```

### 5. **package.json** (frontend) - Frontend dependencies
**Mục đích**: Hiểu frontend build process
```bash
# Hiểu về:
- Frontend framework (Vue.js, React, etc.)
- Build scripts và commands
- Dependencies và devDependencies
- Build tools (Webpack, Vite, etc.)
```

### 6. **go.mod** hoặc **requirements.txt** - Backend dependencies
**Mục đích**: Hiểu backend technology stack
```bash
# Hiểu về:
- Backend language và framework
- Dependencies và versions
- Module structure
```

---

## 🔧 **BƯỚC 3: Files cấu hình chi tiết**

### 7. **.dockerignore** - Build optimization
### 8. **frontend/Makefile** - Frontend build process
### 9. **DOCKER_VOLUMES_GUIDE.md** - Data persistence strategy
### 10. **IMAGE_UPLOAD_FLOW_GUIDE.md** - Image upload flow and volume mount troubleshooting
### 11. **CLOUD_DEPLOYMENT_GUIDE.md** - Deployment strategies

---

## 📁 **BƯỚC 4: Cấu trúc thư mục**

**Yêu cầu xem cấu trúc:**
```bash
# Lệnh để hiểu cấu trúc project:
tree -L 3 -I 'node_modules|.git|dist|build'
# hoặc
find . -type d -name "node_modules" -prune -o -type d -print | head -20
```

**Các thư mục quan trọng cần hiểu:**
- `frontend/` - Frontend code
- `backend/` - Backend code  
- `migrations/` - Database migrations
- `docker/` - Docker configurations
- `scripts/` - Utility scripts

---

## 🎯 **BƯỚC 5: Kiểm tra trạng thái hiện tại**

### Commands để hiểu trạng thái:
```bash
# 1. Kiểm tra project status
./manage.sh status

# 2. Kiểm tra Docker containers
docker ps -a

# 3. Kiểm tra Docker volumes
docker volume ls | grep project_name

# 4. Kiểm tra network
docker network ls

# 5. Kiểm tra logs nếu có issues
./manage.sh logs
```

---

## 💡 **BƯỚC 6: Thu thập thông tin bổ sung**

### **Environment hiện tại:**
Hỏi user về:
- Dự án đang chạy hay chưa?
- Có data có sẵn không?
- Cần fresh install hay rebuild?
- Có issues gì đang gặp phải không?
- Local development hay production deployment?

### **Mục tiêu cụ thể:**
Xác định user muốn:
- Fresh build từ đầu?
- Rebuild với preserved data?
- Fix issues cụ thể nào?
- Deploy lên cloud?
- Update/upgrade project?

---

## 🚀 **Workflow thực tế cho AI**

### **Phase 1: Information Gathering**
```bash
# 1. Đọc overview
view DEPLOYMENT_README.md

# 2. Hiểu management commands
view manage.sh

# 3. Hiểu build process
view Dockerfile

# 4. Kiểm tra trạng thái
./manage.sh status
```

### **Phase 2: Deep Understanding**
```bash
# 5. Hiểu services architecture
view docker-compose.yml

# 6. Hiểu frontend
view frontend/package.json
view frontend/Makefile

# 7. Hiểu backend
view backend/go.mod  # hoặc requirements.txt

# 8. Hiểu data strategy
view DOCKER_VOLUMES_GUIDE.md

# 9. Hiểu image upload flow (QUAN TRỌNG cho troubleshooting)
view IMAGE_UPLOAD_FLOW_GUIDE.md
```

### **Phase 3: Action Planning**
Dựa vào thông tin đã thu thập, đề xuất action plan:

**Nếu project chưa chạy:**
```bash
./manage.sh start
# hoặc
./deploy.sh fresh-install
```

**Nếu project đang chạy và cần update:**
```bash
./manage.sh rebuild
# hoặc
./deploy.sh update
```

**Nếu có issues:**
```bash
./manage.sh logs
# Analyze logs và suggest fixes
```

**Nếu có vấn đề về images (upload/display):**
```bash
# 1. Kiểm tra volume mount
docker inspect phongnha_app | grep -A 10 "Mounts"

# 2. Kiểm tra files trong volume
docker run --rm -v phongnha_uploads:/uploads alpine ls -la /uploads

# 3. Test image access
curl -I http://localhost:8080/assets/uploads/[filename]

# 4. Đọc troubleshooting guide
view IMAGE_UPLOAD_FLOW_GUIDE.md
```

---

## 📋 **Template câu hỏi cho AI**

Khi được yêu cầu hiểu project, AI nên hỏi:

1. **"Tôi sẽ đọc các files theo thứ tự để hiểu project. Bạn có thể cho tôi xem:"**
   - `DEPLOYMENT_README.md`
   - `manage.sh` 
   - `Dockerfile`

2. **"Sau khi hiểu cơ bản, tôi cần kiểm tra trạng thái hiện tại:"**
   - `./manage.sh status`
   - Cấu trúc thư mục project

3. **"Cuối cùng, bạn muốn tôi làm gì cụ thể?"**
   - Fresh install?
   - Rebuild với data?
   - Fix issues?
   - Deploy to cloud?

---

## ⚡ **Quick Reference cho AI**

### **Minimum files cần đọc:**
1. `DEPLOYMENT_README.md` - Overview
2. `manage.sh` - Commands
3. `./manage.sh status` - Current state

### **Comprehensive understanding:**
4. `Dockerfile` - Build process
5. `docker-compose.yml` - Architecture
6. `package.json` - Frontend
7. `go.mod/requirements.txt` - Backend

### **Common actions:**
- **Fresh start**: `./manage.sh start` hoặc `./deploy.sh fresh-install`
- **Rebuild**: `./manage.sh rebuild`
- **Update**: `./deploy.sh update`
- **Debug**: `./manage.sh logs`

---

## 🎯 **Success Criteria**

AI đã hiểu project đúng khi có thể:

✅ **Identify project type** (web app, API, microservices, etc.)
✅ **Understand tech stack** (frontend + backend technologies)
✅ **Know available commands** (start, stop, rebuild, deploy)
✅ **Assess current state** (running, stopped, needs setup)
✅ **Suggest appropriate actions** based on user goals
✅ **Handle common issues** (port conflicts, permission errors, etc.)

---

## 📞 **Emergency Quick Start**

Nếu user cần immediate help và không có thời gian đọc hết:

```bash
# 1. Quick overview
head -50 DEPLOYMENT_README.md

# 2. Available commands
grep -A 5 -B 5 "Commands:" manage.sh

# 3. Current status
./manage.sh status

# 4. Take action based on status
```

---

**💡 Lưu ý cho AI:**
- Luôn đọc files theo thứ tự ưu tiên
- Kiểm tra trạng thái trước khi action
- Hỏi rõ mục tiêu của user
- Suggest best practices
- Backup trước khi thay đổi quan trọng
